"""
任务管理器
负责任务的创建、存储、状态管理
"""

import uuid
import time
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from threading import Lock
from collections import deque
import logging

from .base_module import TaskStatus

class Task:
    """任务实体类"""
    
    def __init__(self, task_type: str, target: str, action: str, params: Dict = None):
        self.task_id = str(uuid.uuid4())
        self.type = task_type
        self.target = target
        self.action = action
        self.params = params or {}
        self.priority = params.get('priority', 5)
        self.timeout = params.get('timeout', 300)
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.retry_count = 0
        self.max_retries = 3
        self.result = None
        self.error = None
        self.progress = 0.0
        self.metadata = {}
        
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "type": self.type,
            "target": self.target,
            "action": self.action,
            "params": self.params,
            "priority": self.priority,
            "timeout": self.timeout,
            "status": self.status.value,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "retry_count": self.retry_count,
            "progress": self.progress,
            "result": self.result,
            "error": self.error,
            "metadata": self.metadata
        }
        
    def is_timeout(self) -> bool:
        """检查是否超时"""
        if self.started_at:
            elapsed = (datetime.now() - self.started_at).total_seconds()
            return elapsed > self.timeout
        return False
        
    def can_retry(self) -> bool:
        """是否可以重试"""
        return self.retry_count < self.max_retries

class TaskManager:
    """任务管理器"""
    
    def __init__(self, max_queue_size: int = 1000):
        self.tasks = {}  # task_id -> Task
        self.pending_queue = deque()  # 待执行任务队列
        self.running_tasks = {}  # module -> task_id
        self.completed_tasks = deque(maxlen=1000)  # 已完成任务（保留最近1000个）
        self.max_queue_size = max_queue_size
        self.lock = Lock()
        self.logger = logging.getLogger("TaskBus.TaskManager")
        
    def create_task(self, task_type: str, target: str, action: str, params: Dict = None) -> Task:
        """创建任务"""
        task = Task(task_type, target, action, params)
        
        with self.lock:
            if len(self.pending_queue) >= self.max_queue_size:
                raise Exception("Task queue is full")
                
            self.tasks[task.task_id] = task
            self.pending_queue.append(task.task_id)
            
        self.logger.info(f"Created task: {task.task_id} - {target}.{action}")
        return task
        
    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        return self.tasks.get(task_id)
        
    def get_pending_task(self, target_module: str = None) -> Optional[Task]:
        """获取待执行任务"""
        with self.lock:
            # 按优先级排序
            if self.pending_queue:
                sorted_tasks = sorted(
                    [self.tasks[tid] for tid in self.pending_queue],
                    key=lambda t: t.priority,
                    reverse=True
                )
                
                for task in sorted_tasks:
                    if target_module is None or task.target == target_module:
                        self.pending_queue.remove(task.task_id)
                        return task
        return None
        
    def update_task_status(self, task_id: str, status: TaskStatus, error: str = None):
        """更新任务状态"""
        task = self.get_task(task_id)
        if not task:
            return
            
        with self.lock:
            task.status = status
            
            if status == TaskStatus.RUNNING:
                task.started_at = datetime.now()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED]:
                task.completed_at = datetime.now()
                if error:
                    task.error = error
                    
                # 移到已完成队列
                if task_id in self.tasks:
                    self.completed_tasks.append(task_id)
                    # 从运行中移除
                    for module, tid in list(self.running_tasks.items()):
                        if tid == task_id:
                            del self.running_tasks[module]
                            
        self.logger.info(f"Task {task_id} status updated to {status.value}")
        
    def update_task_progress(self, task_id: str, progress: float, message: str = None):
        """更新任务进度"""
        task = self.get_task(task_id)
        if task:
            task.progress = min(100.0, max(0.0, progress))
            if message:
                task.metadata['progress_message'] = message
                
    def retry_task(self, task_id: str) -> bool:
        """重试任务"""
        task = self.get_task(task_id)
        if not task or not task.can_retry():
            return False
            
        with self.lock:
            task.retry_count += 1
            task.status = TaskStatus.PENDING
            task.error = None
            self.pending_queue.append(task_id)
            
        self.logger.info(f"Retrying task {task_id}, attempt {task.retry_count}")
        return True
        
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        task = self.get_task(task_id)
        if not task:
            return False
            
        with self.lock:
            if task.status == TaskStatus.PENDING:
                self.pending_queue.remove(task_id)
            task.status = TaskStatus.CANCELLED
            task.completed_at = datetime.now()
            
        self.logger.info(f"Task {task_id} cancelled")
        return True
        
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        with self.lock:
            pending_count = len(self.pending_queue)
            running_count = len(self.running_tasks)
            completed_count = len(self.completed_tasks)
            
            # 计算成功率
            completed_tasks_list = [self.tasks.get(tid) for tid in self.completed_tasks if tid in self.tasks]
            success_count = sum(1 for t in completed_tasks_list if t and t.status == TaskStatus.COMPLETED)
            success_rate = (success_count / completed_count * 100) if completed_count > 0 else 0
            
        return {
            "total_tasks": len(self.tasks),
            "pending": pending_count,
            "running": running_count,
            "completed": completed_count,
            "success_rate": round(success_rate, 2),
            "queue_usage": round(pending_count / self.max_queue_size * 100, 2)
        }
        
    def cleanup_old_tasks(self, keep_hours: int = 24):
        """清理旧任务"""
        cutoff_time = datetime.now().timestamp() - (keep_hours * 3600)
        
        with self.lock:
            old_tasks = []
            for task_id, task in self.tasks.items():
                if task.completed_at and task.completed_at.timestamp() < cutoff_time:
                    old_tasks.append(task_id)
                    
            for task_id in old_tasks:
                del self.tasks[task_id]
                
        self.logger.info(f"Cleaned up {len(old_tasks)} old tasks")