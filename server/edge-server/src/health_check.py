#!/usr/bin/env python3
"""
边缘服务器健康检查模块
实现基础健康检查和深度健康检查
"""

import asyncio
import aiohttp
import logging
import socket
import time
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from enum import Enum
import subprocess
import yaml
import os

# 配置日志
logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


class ServiceStatus(Enum):
    """服务状态枚举"""
    RUNNING = "running"
    STOPPED = "stopped"
    ERROR = "error"
    UNKNOWN = "unknown"


class HealthChecker:
    """健康检查器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.last_check_time = None
        self.last_status = HealthStatus.UNKNOWN
        self.last_error = None
        
    async def check(self) -> <PERSON><PERSON>[HealthStatus, Dict[str, Any]]:
        """执行健康检查"""
        raise NotImplementedError
        

class NetworkHealthChecker(HealthChecker):
    """网络连通性检查"""
    
    async def check(self) -> Tuple[HealthStatus, Dict[str, Any]]:
        """检查网络连通性"""
        try:
            # 检查串口服务器ping
            serial_server_ip = self.config.get('hardware', {}).get('io_control', {}).get('serial_server_ip', '*************')
            serial_ping_result = await self._ping_host(serial_server_ip)
            
            # 检查NVR连接
            nvr_ip = self.config.get('camera', {}).get('nvr', {}).get('ip', '*************')
            nvr_ping_result = await self._ping_host(nvr_ip)
            
            # 检查激光雷达网络（仅检查网络可达性，不上电）
            lidar_ip = self.config.get('lidar', {}).get('sdk_config', {}).get('ip_address', '*************')
            lidar_ping_result = await self._ping_host(lidar_ip)
            
            details = {
                "serial_server": {
                    "ip": serial_server_ip,
                    "reachable": serial_ping_result,
                    "status": "healthy" if serial_ping_result else "unhealthy"
                },
                "nvr": {
                    "ip": nvr_ip,
                    "reachable": nvr_ping_result,
                    "status": "healthy" if nvr_ping_result else "unhealthy"
                },
                "lidar_network": {
                    "ip": lidar_ip,
                    "reachable": lidar_ping_result,
                    "status": "healthy" if lidar_ping_result else "degraded"  # 降级状态，因为激光雷达可能未上电
                }
            }
            
            # 判断整体状态
            if serial_ping_result and nvr_ping_result:
                status = HealthStatus.HEALTHY
            elif serial_ping_result or nvr_ping_result:
                status = HealthStatus.DEGRADED
            else:
                status = HealthStatus.UNHEALTHY
                
            self.last_check_time = datetime.now()
            self.last_status = status
            
            return status, details
            
        except Exception as e:
            logger.error(f"网络健康检查失败: {e}")
            self.last_error = str(e)
            return HealthStatus.UNHEALTHY, {"error": str(e)}
            
    async def _ping_host(self, host: str, timeout: int = 2) -> bool:
        """Ping主机"""
        try:
            cmd = ["ping", "-c", "1", "-W", str(timeout), host]
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            return process.returncode == 0
        except Exception as e:
            logger.error(f"Ping {host} 失败: {e}")
            return False


class ServiceHealthChecker(HealthChecker):
    """服务状态检查"""
    
    async def check(self) -> Tuple[HealthStatus, Dict[str, Any]]:
        """检查各服务状态"""
        try:
            details = {}
            
            # 检查硬件控制服务
            hardware_service_url = self.config.get('hardware', {}).get('service', {}).get('base_url', 'http://localhost:7080')
            hardware_status = await self._check_http_service(hardware_service_url + "/health")
            details["hardware_service"] = hardware_status
            
            # 检查摄像头服务
            camera_service_url = self.config.get('camera', {}).get('service_url', 'http://localhost:7090')
            camera_status = await self._check_http_service(camera_service_url + "/health")
            details["camera_service"] = camera_status
            
            # 检查gRPC服务
            grpc_port = self.config.get('grpc', {}).get('server', {}).get('port', 51011)
            grpc_status = await self._check_port("localhost", grpc_port)
            details["grpc_service"] = {
                "port": grpc_port,
                "status": "healthy" if grpc_status else "unhealthy",
                "reachable": grpc_status
            }
            
            # 判断整体状态
            all_healthy = all([
                hardware_status.get("status") == "healthy",
                camera_status.get("status") == "healthy",
                grpc_status
            ])
            
            if all_healthy:
                status = HealthStatus.HEALTHY
            elif any([hardware_status.get("reachable"), camera_status.get("reachable"), grpc_status]):
                status = HealthStatus.DEGRADED
            else:
                status = HealthStatus.UNHEALTHY
                
            self.last_check_time = datetime.now()
            self.last_status = status
            
            return status, details
            
        except Exception as e:
            logger.error(f"服务健康检查失败: {e}")
            self.last_error = str(e)
            return HealthStatus.UNHEALTHY, {"error": str(e)}
            
    async def _check_http_service(self, url: str, timeout: int = 5) -> Dict[str, Any]:
        """检查HTTP服务"""
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=aiohttp.ClientTimeout(total=timeout)) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "url": url,
                            "status": "healthy",
                            "reachable": True,
                            "response": data
                        }
                    else:
                        return {
                            "url": url,
                            "status": "unhealthy",
                            "reachable": True,
                            "http_status": response.status
                        }
        except asyncio.TimeoutError:
            return {
                "url": url,
                "status": "unhealthy",
                "reachable": False,
                "error": "timeout"
            }
        except Exception as e:
            return {
                "url": url,
                "status": "unhealthy",
                "reachable": False,
                "error": str(e)
            }
            
    async def _check_port(self, host: str, port: int, timeout: int = 2) -> bool:
        """检查端口是否开放"""
        try:
            _, writer = await asyncio.wait_for(
                asyncio.open_connection(host, port),
                timeout=timeout
            )
            writer.close()
            await writer.wait_closed()
            return True
        except:
            return False


class DeviceHealthChecker(HealthChecker):
    """设备健康检查（需要上电）"""
    
    def __init__(self, config: Dict[str, Any], hardware_service_url: str):
        super().__init__(config)
        self.hardware_service_url = hardware_service_url
        
    async def check_lidar_and_ptz(self) -> Tuple[HealthStatus, Dict[str, Any]]:
        """检查激光雷达和云台（深度检查，需要先上电）"""
        details = {
            "lidar": {"status": "unknown"},
            "ptz": {"status": "unknown"},
            "power_on_required": True
        }
        
        try:
            # 1. 先上电激光雷达和云台
            logger.info("开始设备深度健康检查，正在上电...")
            power_on_results = await self._power_on_devices()
            details["power_on_results"] = power_on_results
            
            if not all([power_on_results.get("lidar", {}).get("success"), 
                       power_on_results.get("ptz", {}).get("success")]):
                logger.error("设备上电失败")
                return HealthStatus.UNHEALTHY, details
            
            # 2. 等待设备启动
            lidar_delay = self.config.get('hardware', {}).get('io_control', {}).get('devices', {}).get('lidar', {}).get('startup_delay', 30)
            ptz_delay = self.config.get('hardware', {}).get('io_control', {}).get('devices', {}).get('ptz', {}).get('startup_delay', 5)
            
            logger.info(f"等待设备启动: 激光雷达 {lidar_delay}秒, 云台 {ptz_delay}秒")
            await asyncio.sleep(max(lidar_delay, ptz_delay))
            
            # 3. 检查激光雷达
            lidar_status = await self._check_lidar_status()
            details["lidar"] = lidar_status
            
            # 4. 检查云台
            ptz_status = await self._check_ptz_status()
            details["ptz"] = ptz_status
            
            # 5. 关闭设备电源（安全措施）
            logger.info("健康检查完成，正在断电...")
            power_off_results = await self._power_off_devices()
            details["power_off_results"] = power_off_results
            
            # 判断整体状态
            if lidar_status.get("status") == "healthy" and ptz_status.get("status") == "healthy":
                status = HealthStatus.HEALTHY
            elif lidar_status.get("status") == "healthy" or ptz_status.get("status") == "healthy":
                status = HealthStatus.DEGRADED
            else:
                status = HealthStatus.UNHEALTHY
                
            return status, details
            
        except Exception as e:
            logger.error(f"设备健康检查失败: {e}")
            # 确保设备断电
            await self._power_off_devices()
            return HealthStatus.UNHEALTHY, {"error": str(e), "details": details}
            
    async def _power_on_devices(self) -> Dict[str, Any]:
        """上电设备"""
        results = {}
        try:
            async with aiohttp.ClientSession() as session:
                # 上电激光雷达
                lidar_data = {"device": "lidar", "status": 1}
                async with session.post(
                    f"{self.hardware_service_url}/api/device/power",
                    json=lidar_data
                ) as response:
                    results["lidar"] = await response.json()
                    
                # 上电云台
                ptz_data = {"device": "ptz", "status": 1}
                async with session.post(
                    f"{self.hardware_service_url}/api/device/power",
                    json=ptz_data
                ) as response:
                    results["ptz"] = await response.json()
                    
        except Exception as e:
            logger.error(f"设备上电失败: {e}")
            results["error"] = str(e)
            
        return results
        
    async def _power_off_devices(self) -> Dict[str, Any]:
        """断电设备"""
        results = {}
        try:
            async with aiohttp.ClientSession() as session:
                # 断电激光雷达
                lidar_data = {"device": "lidar", "status": 0}
                async with session.post(
                    f"{self.hardware_service_url}/api/device/power",
                    json=lidar_data
                ) as response:
                    results["lidar"] = await response.json()
                    
                # 断电云台
                ptz_data = {"device": "ptz", "status": 0}
                async with session.post(
                    f"{self.hardware_service_url}/api/device/power",
                    json=ptz_data
                ) as response:
                    results["ptz"] = await response.json()
                    
        except Exception as e:
            logger.error(f"设备断电失败: {e}")
            results["error"] = str(e)
            
        return results
        
    async def _check_lidar_status(self) -> Dict[str, Any]:
        """检查激光雷达状态"""
        try:
            lidar_ip = self.config.get('lidar', {}).get('sdk_config', {}).get('ip_address', '*************')
            
            # 尝试连接激光雷达管理端口
            management_port = self.config.get('lidar', {}).get('sdk_config', {}).get('management_port', 58000)
            
            connected = await self._check_tcp_connection(lidar_ip, management_port)
            
            return {
                "ip": lidar_ip,
                "management_port": management_port,
                "connected": connected,
                "status": "healthy" if connected else "unhealthy"
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
            
    async def _check_ptz_status(self) -> Dict[str, Any]:
        """检查云台状态"""
        try:
            # 查询云台角度
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.hardware_service_url}/api/ptz/status"
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return {
                            "status": "healthy",
                            "response": data
                        }
                    else:
                        return {
                            "status": "unhealthy",
                            "http_status": response.status
                        }
                        
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e)
            }
            
    async def _check_tcp_connection(self, host: str, port: int, timeout: int = 5) -> bool:
        """检查TCP连接"""
        try:
            _, writer = await asyncio.wait_for(
                asyncio.open_connection(host, port),
                timeout=timeout
            )
            writer.close()
            await writer.wait_closed()
            return True
        except:
            return False


class HealthCheckService:
    """健康检查服务"""
    
    def __init__(self, config_path: str = None):
        # 加载配置
        if config_path and os.path.exists(config_path):
            with open(config_path, 'r') as f:
                self.config = yaml.safe_load(f)
        else:
            # 使用默认配置路径
            default_config_path = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                'config', 'config.yaml'
            )
            if os.path.exists(default_config_path):
                with open(default_config_path, 'r') as f:
                    self.config = yaml.safe_load(f)
            else:
                raise FileNotFoundError(f"配置文件未找到: {default_config_path}")
                
        # 初始化检查器
        self.network_checker = NetworkHealthChecker(self.config)
        self.service_checker = ServiceHealthChecker(self.config)
        
        hardware_service_url = self.config.get('hardware', {}).get('service', {}).get('base_url', 'http://localhost:7080')
        self.device_checker = DeviceHealthChecker(self.config, hardware_service_url)
        
        # 健康检查结果缓存
        self.last_basic_check = None
        self.last_deep_check = None
        self.basic_check_interval = 30  # 基础检查间隔（秒）
        
        # 运行状态
        self.running = False
        self.basic_check_task = None
        
    async def start(self):
        """启动健康检查服务"""
        self.running = True
        # 启动基础健康检查定时任务
        self.basic_check_task = asyncio.create_task(self._basic_check_loop())
        logger.info("健康检查服务已启动")
        
    async def stop(self):
        """停止健康检查服务"""
        self.running = False
        if self.basic_check_task:
            self.basic_check_task.cancel()
            try:
                await self.basic_check_task
            except asyncio.CancelledError:
                pass
        logger.info("健康检查服务已停止")
        
    async def _basic_check_loop(self):
        """基础健康检查循环"""
        while self.running:
            try:
                await self.perform_basic_check()
                await asyncio.sleep(self.basic_check_interval)
            except Exception as e:
                logger.error(f"基础健康检查循环出错: {e}")
                await asyncio.sleep(self.basic_check_interval)
                
    async def perform_basic_check(self) -> Dict[str, Any]:
        """执行基础健康检查"""
        logger.info("执行基础健康检查...")
        
        result = {
            "check_type": "basic",
            "timestamp": datetime.now().isoformat(),
            "components": {}
        }
        
        # 网络检查
        network_status, network_details = await self.network_checker.check()
        result["components"]["network"] = {
            "status": network_status.value,
            "details": network_details
        }
        
        # 服务检查
        service_status, service_details = await self.service_checker.check()
        result["components"]["services"] = {
            "status": service_status.value,
            "details": service_details
        }
        
        # 计算整体状态
        if network_status == HealthStatus.HEALTHY and service_status == HealthStatus.HEALTHY:
            result["overall_status"] = HealthStatus.HEALTHY.value
        elif network_status == HealthStatus.UNHEALTHY or service_status == HealthStatus.UNHEALTHY:
            result["overall_status"] = HealthStatus.UNHEALTHY.value
        else:
            result["overall_status"] = HealthStatus.DEGRADED.value
            
        self.last_basic_check = result
        logger.info(f"基础健康检查完成: {result['overall_status']}")
        
        return result
        
    async def perform_deep_check(self) -> Dict[str, Any]:
        """执行深度健康检查（包含设备检查）"""
        logger.info("执行深度健康检查...")
        
        # 先执行基础检查
        result = await self.perform_basic_check()
        result["check_type"] = "deep"
        
        # 执行设备检查
        device_status, device_details = await self.device_checker.check_lidar_and_ptz()
        result["components"]["devices"] = {
            "status": device_status.value,
            "details": device_details
        }
        
        # 重新计算整体状态
        all_statuses = [
            HealthStatus[result["components"]["network"]["status"].upper()],
            HealthStatus[result["components"]["services"]["status"].upper()],
            device_status
        ]
        
        if all(s == HealthStatus.HEALTHY for s in all_statuses):
            result["overall_status"] = HealthStatus.HEALTHY.value
        elif any(s == HealthStatus.UNHEALTHY for s in all_statuses):
            result["overall_status"] = HealthStatus.UNHEALTHY.value
        else:
            result["overall_status"] = HealthStatus.DEGRADED.value
            
        self.last_deep_check = result
        logger.info(f"深度健康检查完成: {result['overall_status']}")
        
        return result
        
    def get_last_check_result(self, check_type: str = "basic") -> Optional[Dict[str, Any]]:
        """获取最后一次检查结果"""
        if check_type == "basic":
            return self.last_basic_check
        elif check_type == "deep":
            return self.last_deep_check
        else:
            return None


# 单例实例
_health_service = None


def get_health_service(config_path: str = None) -> HealthCheckService:
    """获取健康检查服务单例"""
    global _health_service
    if _health_service is None:
        _health_service = HealthCheckService(config_path)
    return _health_service


async def main():
    """测试主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    service = get_health_service()
    
    # 测试基础健康检查
    print("\n=== 基础健康检查 ===")
    basic_result = await service.perform_basic_check()
    print(f"整体状态: {basic_result['overall_status']}")
    print(f"网络状态: {basic_result['components']['network']['status']}")
    print(f"服务状态: {basic_result['components']['services']['status']}")
    
    # 测试深度健康检查
    print("\n=== 深度健康检查 ===")
    print("注意：深度检查会上电设备，请确保安全")
    confirm = input("是否继续深度检查？(y/n): ")
    if confirm.lower() == 'y':
        deep_result = await service.perform_deep_check()
        print(f"整体状态: {deep_result['overall_status']}")
        print(f"设备状态: {deep_result['components']['devices']['status']}")


if __name__ == "__main__":
    asyncio.run(main())