"""
硬件协调助手
优化硬件控制与LiDAR模块的协调机制
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from enum import Enum
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class DeviceType(Enum):
    """设备类型"""
    LIDAR = "lidar"
    PTZ = "ptz"

class DeviceState(Enum):
    """设备状态"""
    OFF = "off"
    STARTING = "starting"
    READY = "ready"
    BUSY = "busy"
    ERROR = "error"

@dataclass
class DeviceStatus:
    """设备状态信息"""
    device_type: DeviceType
    state: DeviceState
    last_update: datetime
    error_message: Optional[str] = None
    
class CoordinationConfig:
    """协调配置"""
    LIDAR_STARTUP_TIME = 30  # 激光雷达启动时间（秒）
    PTZ_STARTUP_TIME = 5     # 云台启动时间（秒）
    PTZ_MOVE_TIMEOUT = 10    # PTZ移动超时时间（秒）
    PTZ_ANGLE_TOLERANCE = 1.0  # PTZ角度容差（度）
    MAX_RETRIES = 3          # 最大重试次数
    RETRY_DELAY = 2          # 重试延迟（秒）

class SmartCoordinator:
    """智能协调器"""
    
    def __init__(self, hardware_client, config: Optional[CoordinationConfig] = None):
        self.hardware_client = hardware_client
        self.config = config or CoordinationConfig()
        self.device_states: Dict[DeviceType, DeviceStatus] = {}
        self._coordination_lock = asyncio.Lock()
    
    async def startup_devices(self, devices: list[DeviceType]) -> Dict[str, Any]:
        """智能启动设备序列"""
        async with self._coordination_lock:
            results = {}
            
            # 并行启动所有设备
            startup_tasks = []
            for device in devices:
                task = asyncio.create_task(self._startup_single_device(device))
                startup_tasks.append((device, task))
            
            # 等待所有设备启动完成
            for device, task in startup_tasks:
                try:
                    result = await task
                    results[device.value] = result
                except Exception as e:
                    logger.error(f"Failed to start {device.value}: {e}")
                    results[device.value] = {"success": False, "error": str(e)}
            
            return results
    
    async def _startup_single_device(self, device: DeviceType) -> Dict[str, Any]:
        """启动单个设备"""
        try:
            # 更新设备状态
            self._update_device_state(device, DeviceState.STARTING)
            
            # 发送电源开启命令
            if device == DeviceType.LIDAR:
                await self.hardware_client.post("/api/io/control", 
                                              {"do_index": 1, "status": 1})
                await asyncio.sleep(self.config.LIDAR_STARTUP_TIME)
            elif device == DeviceType.PTZ:
                await self.hardware_client.post("/api/io/control",
                                              {"do_index": 2, "status": 1})
                await asyncio.sleep(self.config.PTZ_STARTUP_TIME)
            
            # 验证设备就绪
            if await self._verify_device_ready(device):
                self._update_device_state(device, DeviceState.READY)
                return {"success": True, "startup_time": datetime.now()}
            else:
                self._update_device_state(device, DeviceState.ERROR, "Startup verification failed")
                return {"success": False, "error": "Device not ready after startup"}
                
        except Exception as e:
            self._update_device_state(device, DeviceState.ERROR, str(e))
            return {"success": False, "error": str(e)}
    
    async def _verify_device_ready(self, device: DeviceType) -> bool:
        """验证设备就绪状态"""
        if device == DeviceType.LIDAR:
            # Ping测试激光雷达
            proc = await asyncio.create_subprocess_exec(
                'ping', '-c', '1', '-W', '1', '*************',
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await proc.communicate()
            return proc.returncode == 0
            
        elif device == DeviceType.PTZ:
            # PTZ运动测试
            try:
                await self.hardware_client.post("/api/ptz/angle", 
                                              {"ptz_addr": 30, "axis": "pan", "angle": 5.0})
                await asyncio.sleep(2)
                await self.hardware_client.post("/api/ptz/angle",
                                              {"ptz_addr": 30, "axis": "pan", "angle": 0.0})
                return True
            except Exception:
                return False
        
        return False
    
    async def coordinate_scan_position(self, pan: float, tilt: float, 
                                     stabilize_time: float = 2.0) -> Dict[str, Any]:
        """协调扫描位置设置"""
        async with self._coordination_lock:
            try:
                # 检查PTZ设备状态
                if DeviceType.PTZ not in self.device_states or \
                   self.device_states[DeviceType.PTZ].state != DeviceState.READY:
                    return {"success": False, "error": "PTZ not ready"}
                
                # 设置PTZ位置
                self._update_device_state(DeviceType.PTZ, DeviceState.BUSY)
                
                # 移动到目标位置
                await self.hardware_client.post("/api/ptz/angle",
                                              {"ptz_addr": 30, "axis": "pan", "angle": pan})
                await self.hardware_client.post("/api/ptz/angle", 
                                              {"ptz_addr": 30, "axis": "tilt", "angle": tilt})
                
                # 等待稳定
                await asyncio.sleep(stabilize_time)
                
                # 验证位置精度
                if await self._verify_ptz_position(pan, tilt):
                    self._update_device_state(DeviceType.PTZ, DeviceState.READY)
                    return {
                        "success": True,
                        "position": {"pan": pan, "tilt": tilt},
                        "stabilize_time": stabilize_time
                    }
                else:
                    self._update_device_state(DeviceType.PTZ, DeviceState.ERROR, "Position verification failed")
                    return {"success": False, "error": "PTZ position verification failed"}
                    
            except Exception as e:
                self._update_device_state(DeviceType.PTZ, DeviceState.ERROR, str(e))
                return {"success": False, "error": str(e)}
    
    async def _verify_ptz_position(self, target_pan: float, target_tilt: float) -> bool:
        """验证PTZ位置精度"""
        try:
            pan_response = await self.hardware_client.get("/api/ptz/angle?ptz_addr=30&axis=pan")
            tilt_response = await self.hardware_client.get("/api/ptz/angle?ptz_addr=30&axis=tilt")
            
            actual_pan = pan_response.get("angle", 0)
            actual_tilt = tilt_response.get("angle", 0)
            
            pan_error = abs(actual_pan - target_pan)
            tilt_error = abs(actual_tilt - target_tilt)
            
            return (pan_error <= self.config.PTZ_ANGLE_TOLERANCE and 
                    tilt_error <= self.config.PTZ_ANGLE_TOLERANCE)
        except Exception:
            return False
    
    async def safe_shutdown(self) -> Dict[str, Any]:
        """安全关闭所有设备"""
        async with self._coordination_lock:
            results = {}
            
            # 1. 停止所有运动
            try:
                await self.hardware_client.post("/api/ptz/stop", {"ptz_addr": 30, "type": "all"})
                results["ptz_stop"] = {"success": True}
            except Exception as e:
                results["ptz_stop"] = {"success": False, "error": str(e)}
            
            # 2. PTZ归零
            try:
                await self.hardware_client.post("/api/ptz/angle", 
                                              {"ptz_addr": 30, "axis": "pan", "angle": 0.0})
                await self.hardware_client.post("/api/ptz/angle",
                                              {"ptz_addr": 30, "axis": "tilt", "angle": 0.0})
                await asyncio.sleep(3)  # 等待归零完成
                
                if await self._verify_ptz_position(0.0, 0.0):
                    results["ptz_home"] = {"success": True}
                else:
                    results["ptz_home"] = {"success": False, "error": "Home position verification failed"}
            except Exception as e:
                results["ptz_home"] = {"success": False, "error": str(e)}
            
            # 3. 断电所有设备
            try:
                await self.hardware_client.post("/api/io/all", {"status": 0})
                results["power_off"] = {"success": True}
                
                # 更新所有设备状态
                for device in DeviceType:
                    self._update_device_state(device, DeviceState.OFF)
                    
            except Exception as e:
                results["power_off"] = {"success": False, "error": str(e)}
            
            return results
    
    def _update_device_state(self, device: DeviceType, state: DeviceState, 
                           error_msg: Optional[str] = None):
        """更新设备状态"""
        self.device_states[device] = DeviceStatus(
            device_type=device,
            state=state,
            last_update=datetime.now(),
            error_message=error_msg
        )
        logger.info(f"{device.value} state changed to {state.value}")
    
    def get_coordination_status(self) -> Dict[str, Any]:
        """获取协调状态"""
        return {
            "timestamp": datetime.now().isoformat(),
            "devices": {
                device.value: {
                    "state": status.state.value,
                    "last_update": status.last_update.isoformat(),
                    "error_message": status.error_message
                } for device, status in self.device_states.items()
            },
            "coordination_active": len(self.device_states) > 0
        }