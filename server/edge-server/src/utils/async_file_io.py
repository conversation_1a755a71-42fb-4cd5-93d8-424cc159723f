"""
异步文件I/O工具
提供高性能的文件操作功能
"""

import os
import asyncio
import aiofiles
import aiofiles.os
import json
import yaml
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, AsyncIterator
import hashlib
import shutil
from datetime import datetime

logger = logging.getLogger(__name__)

class AsyncFileIO:
    """异步文件I/O工具类"""
    
    @staticmethod
    async def read_text(file_path: Union[str, Path], encoding: str = 'utf-8') -> str:
        """异步读取文本文件"""
        async with aiofiles.open(file_path, mode='r', encoding=encoding) as f:
            return await f.read()
            
    @staticmethod
    async def write_text(file_path: Union[str, Path], content: str, 
                        encoding: str = 'utf-8') -> None:
        """异步写入文本文件"""
        # 确保目录存在
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        async with aiofiles.open(file_path, mode='w', encoding=encoding) as f:
            await f.write(content)
            
    @staticmethod
    async def read_bytes(file_path: Union[str, Path]) -> bytes:
        """异步读取二进制文件"""
        async with aiofiles.open(file_path, mode='rb') as f:
            return await f.read()
            
    @staticmethod
    async def write_bytes(file_path: Union[str, Path], content: bytes) -> None:
        """异步写入二进制文件"""
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        async with aiofiles.open(file_path, mode='wb') as f:
            await f.write(content)
            
    @staticmethod
    async def read_json(file_path: Union[str, Path]) -> Dict[str, Any]:
        """异步读取JSON文件"""
        content = await AsyncFileIO.read_text(file_path)
        return json.loads(content)
        
    @staticmethod
    async def write_json(file_path: Union[str, Path], data: Dict[str, Any], 
                        indent: int = 2) -> None:
        """异步写入JSON文件"""
        content = json.dumps(data, indent=indent, ensure_ascii=False)
        await AsyncFileIO.write_text(file_path, content)
        
    @staticmethod
    async def read_yaml(file_path: Union[str, Path]) -> Dict[str, Any]:
        """异步读取YAML文件"""
        content = await AsyncFileIO.read_text(file_path)
        return yaml.safe_load(content)
        
    @staticmethod
    async def write_yaml(file_path: Union[str, Path], data: Dict[str, Any]) -> None:
        """异步写入YAML文件"""
        content = yaml.dump(data, default_flow_style=False, allow_unicode=True)
        await AsyncFileIO.write_text(file_path, content)
        
    @staticmethod
    async def append_text(file_path: Union[str, Path], content: str,
                         encoding: str = 'utf-8') -> None:
        """异步追加文本到文件"""
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        async with aiofiles.open(file_path, mode='a', encoding=encoding) as f:
            await f.write(content)
            
    @staticmethod
    async def exists(path: Union[str, Path]) -> bool:
        """异步检查文件或目录是否存在"""
        return await aiofiles.os.path.exists(str(path))
        
    @staticmethod
    async def is_file(path: Union[str, Path]) -> bool:
        """异步检查是否为文件"""
        return await aiofiles.os.path.isfile(str(path))
        
    @staticmethod
    async def is_dir(path: Union[str, Path]) -> bool:
        """异步检查是否为目录"""
        return await aiofiles.os.path.isdir(str(path))
        
    @staticmethod
    async def get_size(file_path: Union[str, Path]) -> int:
        """异步获取文件大小"""
        stat = await aiofiles.os.stat(str(file_path))
        return stat.st_size
        
    @staticmethod
    async def list_dir(dir_path: Union[str, Path]) -> List[str]:
        """异步列出目录内容"""
        return await aiofiles.os.listdir(str(dir_path))
        
    @staticmethod
    async def makedirs(dir_path: Union[str, Path], exist_ok: bool = True) -> None:
        """异步创建目录"""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, os.makedirs, str(dir_path), exist_ok)
        
    @staticmethod
    async def remove(file_path: Union[str, Path]) -> None:
        """异步删除文件"""
        await aiofiles.os.remove(str(file_path))
        
    @staticmethod
    async def rmtree(dir_path: Union[str, Path]) -> None:
        """异步删除目录树"""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, shutil.rmtree, str(dir_path))
        
    @staticmethod
    async def copy_file(src: Union[str, Path], dst: Union[str, Path]) -> None:
        """异步复制文件"""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, shutil.copy2, str(src), str(dst))
        
    @staticmethod
    async def move_file(src: Union[str, Path], dst: Union[str, Path]) -> None:
        """异步移动文件"""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, shutil.move, str(src), str(dst))
        
    @staticmethod
    async def read_lines(file_path: Union[str, Path], 
                        encoding: str = 'utf-8') -> List[str]:
        """异步读取文件所有行"""
        async with aiofiles.open(file_path, mode='r', encoding=encoding) as f:
            return await f.readlines()
            
    @staticmethod
    async def iter_lines(file_path: Union[str, Path], 
                        encoding: str = 'utf-8') -> AsyncIterator[str]:
        """异步迭代文件行"""
        async with aiofiles.open(file_path, mode='r', encoding=encoding) as f:
            async for line in f:
                yield line.rstrip('\n')
                
    @staticmethod
    async def read_chunks(file_path: Union[str, Path], 
                         chunk_size: int = 8192) -> AsyncIterator[bytes]:
        """异步读取文件块"""
        async with aiofiles.open(file_path, mode='rb') as f:
            while True:
                chunk = await f.read(chunk_size)
                if not chunk:
                    break
                yield chunk
                
    @staticmethod
    async def calculate_md5(file_path: Union[str, Path]) -> str:
        """异步计算文件MD5"""
        md5_hash = hashlib.md5()
        
        async for chunk in AsyncFileIO.read_chunks(file_path):
            md5_hash.update(chunk)
            
        return md5_hash.hexdigest()
        
    @staticmethod
    async def calculate_sha256(file_path: Union[str, Path]) -> str:
        """异步计算文件SHA256"""
        sha256_hash = hashlib.sha256()
        
        async for chunk in AsyncFileIO.read_chunks(file_path):
            sha256_hash.update(chunk)
            
        return sha256_hash.hexdigest()
        
    @staticmethod
    async def get_file_info(file_path: Union[str, Path]) -> Dict[str, Any]:
        """异步获取文件信息"""
        file_path = Path(file_path)
        stat = await aiofiles.os.stat(str(file_path))
        
        return {
            'name': file_path.name,
            'path': str(file_path.absolute()),
            'size': stat.st_size,
            'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'is_file': await AsyncFileIO.is_file(file_path),
            'is_dir': await AsyncFileIO.is_dir(file_path)
        }
        
    @staticmethod
    async def find_files(dir_path: Union[str, Path], pattern: str = '*',
                        recursive: bool = True) -> List[Path]:
        """异步查找文件"""
        dir_path = Path(dir_path)
        loop = asyncio.get_event_loop()
        
        def _find_files():
            if recursive:
                return list(dir_path.rglob(pattern))
            else:
                return list(dir_path.glob(pattern))
                
        return await loop.run_in_executor(None, _find_files)
        
    @staticmethod
    async def safe_write(file_path: Union[str, Path], content: Union[str, bytes],
                        encoding: Optional[str] = 'utf-8') -> None:
        """安全写入文件（先写临时文件，再原子替换）"""
        file_path = Path(file_path)
        temp_path = file_path.with_suffix('.tmp')
        
        try:
            # 写入临时文件
            if isinstance(content, str):
                await AsyncFileIO.write_text(temp_path, content, encoding)
            else:
                await AsyncFileIO.write_bytes(temp_path, content)
                
            # 原子替换
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, temp_path.replace, file_path)
            
        except Exception:
            # 清理临时文件
            if await AsyncFileIO.exists(temp_path):
                await AsyncFileIO.remove(temp_path)
            raise


class AsyncFileWatcher:
    """异步文件监视器"""
    
    def __init__(self, path: Union[str, Path], interval: float = 1.0):
        self.path = Path(path)
        self.interval = interval
        self._running = False
        self._last_modified = None
        self._callbacks = []
        
    def add_callback(self, callback):
        """添加回调函数"""
        self._callbacks.append(callback)
        
    async def start(self):
        """开始监视"""
        self._running = True
        self._last_modified = await self._get_modified_time()
        
        while self._running:
            await asyncio.sleep(self.interval)
            
            current_modified = await self._get_modified_time()
            if current_modified and current_modified != self._last_modified:
                self._last_modified = current_modified
                await self._notify_callbacks()
                
    async def stop(self):
        """停止监视"""
        self._running = False
        
    async def _get_modified_time(self) -> Optional[float]:
        """获取修改时间"""
        try:
            stat = await aiofiles.os.stat(str(self.path))
            return stat.st_mtime
        except:
            return None
            
    async def _notify_callbacks(self):
        """通知回调"""
        for callback in self._callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(self.path)
                else:
                    callback(self.path)
            except Exception as e:
                logger.error(f"Callback error: {e}")


class AsyncLogWriter:
    """异步日志写入器"""
    
    def __init__(self, log_path: Union[str, Path], 
                 max_size: int = 10 * 1024 * 1024,  # 10MB
                 max_files: int = 5):
        self.log_path = Path(log_path)
        self.max_size = max_size
        self.max_files = max_files
        self._queue = asyncio.Queue()
        self._running = False
        self._writer_task = None
        
    async def start(self):
        """启动日志写入器"""
        self._running = True
        self._writer_task = asyncio.create_task(self._writer_loop())
        
    async def stop(self):
        """停止日志写入器"""
        self._running = False
        if self._writer_task:
            await self._writer_task
            
    async def write(self, message: str):
        """写入日志消息"""
        timestamp = datetime.now().isoformat()
        await self._queue.put(f"[{timestamp}] {message}\n")
        
    async def _writer_loop(self):
        """写入器主循环"""
        while self._running or not self._queue.empty():
            try:
                # 批量获取消息
                messages = []
                
                # 获取第一条消息（带超时）
                try:
                    message = await asyncio.wait_for(self._queue.get(), timeout=1.0)
                    messages.append(message)
                except asyncio.TimeoutError:
                    continue
                    
                # 批量获取更多消息（非阻塞）
                while not self._queue.empty() and len(messages) < 100:
                    try:
                        message = self._queue.get_nowait()
                        messages.append(message)
                    except asyncio.QueueEmpty:
                        break
                        
                # 写入文件
                if messages:
                    await self._write_messages(messages)
                    
                    # 检查文件大小
                    if await AsyncFileIO.exists(self.log_path):
                        size = await AsyncFileIO.get_size(self.log_path)
                        if size > self.max_size:
                            await self._rotate_logs()
                            
            except Exception as e:
                logger.error(f"Log writer error: {e}")
                
    async def _write_messages(self, messages: List[str]):
        """写入消息到文件"""
        content = ''.join(messages)
        await AsyncFileIO.append_text(self.log_path, content)
        
    async def _rotate_logs(self):
        """轮转日志文件"""
        # 删除最老的文件
        for i in range(self.max_files - 1, 0, -1):
            old_path = self.log_path.with_suffix(f'.{i}')
            new_path = self.log_path.with_suffix(f'.{i+1}')
            
            if await AsyncFileIO.exists(old_path):
                if i == self.max_files - 1:
                    await AsyncFileIO.remove(old_path)
                else:
                    await AsyncFileIO.move_file(old_path, new_path)
                    
        # 轮转当前文件
        if await AsyncFileIO.exists(self.log_path):
            await AsyncFileIO.move_file(
                self.log_path,
                self.log_path.with_suffix('.1')
            )