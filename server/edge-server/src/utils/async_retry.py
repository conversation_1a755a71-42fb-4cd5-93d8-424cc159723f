"""
异步重试和错误处理工具
提供灵活的重试策略和错误处理机制
"""

import asyncio
import logging
import functools
from typing import Type, Tuple, Union, Callable, Any, Optional, Dict, List
from datetime import datetime, timedelta
import random
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class RetryStrategy(Enum):
    """重试策略"""
    FIXED = "fixed"              # 固定间隔
    LINEAR = "linear"            # 线性递增
    EXPONENTIAL = "exponential"  # 指数退避
    RANDOM = "random"           # 随机间隔
    FIBONACCI = "fibonacci"     # 斐波那契数列

@dataclass
class RetryConfig:
    """重试配置"""
    max_attempts: int = 3
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL
    base_delay: float = 1.0  # 秒
    max_delay: float = 60.0  # 秒
    jitter: bool = True      # 是否添加随机抖动
    exceptions: Tuple[Type[Exception], ...] = (Exception,)  # 需要重试的异常类型
    on_retry: Optional[Callable] = None  # 重试时的回调
    on_failure: Optional[Callable] = None  # 最终失败时的回调

class AsyncRetry:
    """异步重试装饰器"""
    
    def __init__(self, config: Optional[RetryConfig] = None, **kwargs):
        self.config = config or RetryConfig(**kwargs)
        
    def __call__(self, func: Callable) -> Callable:
        """装饰器实现"""
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            return await self.execute(func, *args, **kwargs)
        return wrapper
        
    async def execute(self, func: Callable, *args, **kwargs) -> Any:
        """执行带重试的函数"""
        last_exception = None
        
        for attempt in range(1, self.config.max_attempts + 1):
            try:
                # 执行函数
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    # 在线程池中执行同步函数
                    loop = asyncio.get_event_loop()
                    result = await loop.run_in_executor(None, func, *args, **kwargs)
                    
                return result
                
            except self.config.exceptions as e:
                last_exception = e
                
                # 如果是最后一次尝试，不再重试
                if attempt == self.config.max_attempts:
                    break
                    
                # 计算延迟时间
                delay = self._calculate_delay(attempt)
                
                # 记录日志
                logger.warning(
                    f"Retry {attempt}/{self.config.max_attempts} for {func.__name__} "
                    f"after {delay:.2f}s due to: {e}"
                )
                
                # 调用重试回调
                if self.config.on_retry:
                    await self._call_callback(
                        self.config.on_retry,
                        func, attempt, delay, e, args, kwargs
                    )
                    
                # 等待
                await asyncio.sleep(delay)
                
        # 所有重试都失败
        logger.error(
            f"All {self.config.max_attempts} attempts failed for {func.__name__}: "
            f"{last_exception}"
        )
        
        # 调用失败回调
        if self.config.on_failure:
            await self._call_callback(
                self.config.on_failure,
                func, self.config.max_attempts, 0, last_exception, args, kwargs
            )
            
        raise last_exception
        
    def _calculate_delay(self, attempt: int) -> float:
        """计算重试延迟"""
        if self.config.strategy == RetryStrategy.FIXED:
            delay = self.config.base_delay
        elif self.config.strategy == RetryStrategy.LINEAR:
            delay = self.config.base_delay * attempt
        elif self.config.strategy == RetryStrategy.EXPONENTIAL:
            delay = self.config.base_delay * (2 ** (attempt - 1))
        elif self.config.strategy == RetryStrategy.RANDOM:
            delay = random.uniform(self.config.base_delay, self.config.max_delay)
        elif self.config.strategy == RetryStrategy.FIBONACCI:
            delay = self._fibonacci(attempt) * self.config.base_delay
        else:
            delay = self.config.base_delay
            
        # 限制最大延迟
        delay = min(delay, self.config.max_delay)
        
        # 添加抖动
        if self.config.jitter:
            delay = delay * (0.5 + random.random())
            
        return delay
        
    def _fibonacci(self, n: int) -> int:
        """计算斐波那契数"""
        if n <= 2:
            return 1
        a, b = 1, 1
        for _ in range(n - 2):
            a, b = b, a + b
        return b
        
    async def _call_callback(self, callback: Callable, *args):
        """调用回调函数"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(*args)
            else:
                callback(*args)
        except Exception as e:
            logger.error(f"Callback error: {e}")


# 便捷函数
def async_retry(max_attempts: int = 3, 
                delay: float = 1.0,
                exceptions: Tuple[Type[Exception], ...] = (Exception,),
                strategy: RetryStrategy = RetryStrategy.EXPONENTIAL):
    """简化的重试装饰器"""
    return AsyncRetry(
        config=RetryConfig(
            max_attempts=max_attempts,
            base_delay=delay,
            exceptions=exceptions,
            strategy=strategy
        )
    )


class CircuitBreaker:
    """异步断路器"""
    
    def __init__(self, 
                 failure_threshold: int = 5,
                 recovery_timeout: float = 60.0,
                 expected_exception: Type[Exception] = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "closed"  # closed, open, half-open
        self._lock = asyncio.Lock()
        
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """通过断路器调用函数"""
        async with self._lock:
            # 检查断路器状态
            if self.state == "open":
                if (datetime.now() - self.last_failure_time).total_seconds() > self.recovery_timeout:
                    self.state = "half-open"
                    logger.info(f"Circuit breaker half-open for {func.__name__}")
                else:
                    raise Exception(f"Circuit breaker is open for {func.__name__}")
                    
        try:
            # 执行函数
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(None, func, *args, **kwargs)
                
            # 成功，重置计数器
            async with self._lock:
                if self.state == "half-open":
                    self.state = "closed"
                    logger.info(f"Circuit breaker closed for {func.__name__}")
                self.failure_count = 0
                
            return result
            
        except self.expected_exception as e:
            async with self._lock:
                self.failure_count += 1
                self.last_failure_time = datetime.now()
                
                if self.failure_count >= self.failure_threshold:
                    self.state = "open"
                    logger.error(
                        f"Circuit breaker opened for {func.__name__} "
                        f"after {self.failure_count} failures"
                    )
                    
            raise


class RateLimiter:
    """异步限流器"""
    
    def __init__(self, rate: int, period: float = 1.0):
        """
        :param rate: 允许的请求数
        :param period: 时间窗口（秒）
        """
        self.rate = rate
        self.period = period
        self.semaphore = asyncio.Semaphore(rate)
        self.request_times: List[float] = []
        self._lock = asyncio.Lock()
        
    async def acquire(self):
        """获取许可"""
        async with self._lock:
            now = asyncio.get_event_loop().time()
            
            # 清理过期的请求时间
            self.request_times = [
                t for t in self.request_times 
                if now - t < self.period
            ]
            
            # 检查是否超过限制
            if len(self.request_times) >= self.rate:
                # 计算需要等待的时间
                oldest = self.request_times[0]
                wait_time = self.period - (now - oldest)
                if wait_time > 0:
                    await asyncio.sleep(wait_time)
                    
            # 记录请求时间
            self.request_times.append(now)
            
    async def __aenter__(self):
        await self.acquire()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        pass


class BulkheadPool:
    """隔离池（舱壁模式）"""
    
    def __init__(self, pool_size: int = 10, queue_size: int = 100):
        self.pool_size = pool_size
        self.queue_size = queue_size
        self.semaphore = asyncio.Semaphore(pool_size)
        self.queue = asyncio.Queue(maxsize=queue_size)
        self.active_count = 0
        self._lock = asyncio.Lock()
        
    async def execute(self, func: Callable, *args, **kwargs) -> Any:
        """在隔离池中执行函数"""
        # 检查队列是否已满
        if self.queue.full():
            raise Exception("Bulkhead queue is full")
            
        # 加入队列
        await self.queue.put((func, args, kwargs))
        
        try:
            # 获取执行许可
            async with self.semaphore:
                async with self._lock:
                    self.active_count += 1
                    
                try:
                    # 从队列取出并执行
                    func, args, kwargs = await self.queue.get()
                    
                    if asyncio.iscoroutinefunction(func):
                        result = await func(*args, **kwargs)
                    else:
                        loop = asyncio.get_event_loop()
                        result = await loop.run_in_executor(None, func, *args, **kwargs)
                        
                    return result
                    
                finally:
                    async with self._lock:
                        self.active_count -= 1
                        
        except Exception as e:
            logger.error(f"Bulkhead execution error: {e}")
            raise


class AsyncErrorHandler:
    """统一的异步错误处理器"""
    
    def __init__(self):
        self.handlers: Dict[Type[Exception], List[Callable]] = {}
        self.default_handler: Optional[Callable] = None
        
    def register(self, exception_type: Type[Exception], handler: Callable):
        """注册错误处理器"""
        if exception_type not in self.handlers:
            self.handlers[exception_type] = []
        self.handlers[exception_type].append(handler)
        
    def set_default(self, handler: Callable):
        """设置默认处理器"""
        self.default_handler = handler
        
    async def handle(self, exception: Exception, context: Dict[str, Any] = None):
        """处理异常"""
        context = context or {}
        handled = False
        
        # 查找匹配的处理器
        for exc_type, handlers in self.handlers.items():
            if isinstance(exception, exc_type):
                for handler in handlers:
                    try:
                        if asyncio.iscoroutinefunction(handler):
                            await handler(exception, context)
                        else:
                            handler(exception, context)
                        handled = True
                    except Exception as e:
                        logger.error(f"Error handler failed: {e}")
                        
        # 使用默认处理器
        if not handled and self.default_handler:
            try:
                if asyncio.iscoroutinefunction(self.default_handler):
                    await self.default_handler(exception, context)
                else:
                    self.default_handler(exception, context)
            except Exception as e:
                logger.error(f"Default error handler failed: {e}")
                
        # 记录未处理的异常
        if not handled:
            logger.error(f"Unhandled exception: {exception}", exc_info=True)


# 全局错误处理器实例
error_handler = AsyncErrorHandler()


def resilient_task(retry_config: Optional[RetryConfig] = None,
                  circuit_breaker: Optional[CircuitBreaker] = None,
                  rate_limiter: Optional[RateLimiter] = None):
    """组合装饰器：重试+断路器+限流"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # 限流
            if rate_limiter:
                await rate_limiter.acquire()
                
            # 断路器
            if circuit_breaker:
                executor = lambda: func(*args, **kwargs)
            else:
                executor = func
                
            # 重试
            if retry_config:
                retry = AsyncRetry(config=retry_config)
                if circuit_breaker:
                    result = await retry.execute(
                        circuit_breaker.call, executor
                    )
                else:
                    result = await retry.execute(executor, *args, **kwargs)
            else:
                if circuit_breaker:
                    result = await circuit_breaker.call(executor)
                else:
                    result = await executor(*args, **kwargs)
                    
            return result
            
        return wrapper
    return decorator