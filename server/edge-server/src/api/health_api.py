#!/usr/bin/env python3
"""
健康检查API端点
提供RESTful API接口用于健康状态查询
"""

from aiohttp import web
import logging
from typing import Dict, Any
import json
from datetime import datetime

from health_check import get_health_service, HealthStatus

logger = logging.getLogger(__name__)


class HealthAPI:
    """健康检查API处理器"""
    
    def __init__(self):
        self.health_service = get_health_service()
        self.routes = web.RouteTableDef()
        self._setup_routes()
        
    def _setup_routes(self):
        """设置路由"""
        
        @self.routes.get('/health')
        async def health_check(request):
            """基础健康检查端点"""
            try:
                # 获取最后一次基础检查结果
                last_result = self.health_service.get_last_check_result("basic")
                
                if last_result:
                    # 如果有缓存结果，检查是否过期（超过60秒）
                    last_check_time = datetime.fromisoformat(last_result['timestamp'])
                    time_diff = (datetime.now() - last_check_time).total_seconds()
                    
                    if time_diff > 60:
                        # 重新执行检查
                        result = await self.health_service.perform_basic_check()
                    else:
                        result = last_result
                else:
                    # 没有缓存结果，执行新检查
                    result = await self.health_service.perform_basic_check()
                    
                # 根据整体状态返回适当的HTTP状态码
                if result['overall_status'] == HealthStatus.HEALTHY.value:
                    status_code = 200
                elif result['overall_status'] == HealthStatus.DEGRADED.value:
                    status_code = 200  # 降级状态仍返回200，但在响应体中标明
                else:
                    status_code = 503  # Service Unavailable
                    
                return web.json_response(result, status=status_code)
                
            except Exception as e:
                logger.error(f"健康检查失败: {e}")
                return web.json_response({
                    "overall_status": "error",
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }, status=500)
                
        @self.routes.get('/health/basic')
        async def basic_health_check(request):
            """基础健康检查端点（强制刷新）"""
            try:
                result = await self.health_service.perform_basic_check()
                
                if result['overall_status'] == HealthStatus.HEALTHY.value:
                    status_code = 200
                else:
                    status_code = 503
                    
                return web.json_response(result, status=status_code)
                
            except Exception as e:
                logger.error(f"基础健康检查失败: {e}")
                return web.json_response({
                    "overall_status": "error",
                    "error": str(e)
                }, status=500)
                
        @self.routes.get('/health/deep')
        async def deep_health_check(request):
            """深度健康检查端点（需要权限）"""
            try:
                # 检查是否有授权（简单的token检查）
                auth_header = request.headers.get('Authorization', '')
                if not auth_header.startswith('Bearer '):
                    return web.json_response({
                        "error": "Unauthorized. Deep health check requires authentication."
                    }, status=401)
                    
                # 执行深度检查
                result = await self.health_service.perform_deep_check()
                
                if result['overall_status'] == HealthStatus.HEALTHY.value:
                    status_code = 200
                else:
                    status_code = 503
                    
                return web.json_response(result, status=status_code)
                
            except Exception as e:
                logger.error(f"深度健康检查失败: {e}")
                return web.json_response({
                    "overall_status": "error",
                    "error": str(e)
                }, status=500)
                
        @self.routes.get('/health/status')
        async def health_status(request):
            """获取健康状态摘要"""
            try:
                basic_result = self.health_service.get_last_check_result("basic")
                deep_result = self.health_service.get_last_check_result("deep")
                
                status = {
                    "service": "edge-server",
                    "timestamp": datetime.now().isoformat(),
                    "checks": {}
                }
                
                if basic_result:
                    status["checks"]["basic"] = {
                        "status": basic_result['overall_status'],
                        "last_check": basic_result['timestamp'],
                        "components": {
                            name: comp['status'] 
                            for name, comp in basic_result['components'].items()
                        }
                    }
                    
                if deep_result:
                    status["checks"]["deep"] = {
                        "status": deep_result['overall_status'],
                        "last_check": deep_result['timestamp'],
                        "components": {
                            name: comp['status'] 
                            for name, comp in deep_result['components'].items()
                        }
                    }
                    
                return web.json_response(status)
                
            except Exception as e:
                logger.error(f"获取健康状态失败: {e}")
                return web.json_response({
                    "error": str(e)
                }, status=500)
                
        @self.routes.get('/health/components/{component}')
        async def component_health(request):
            """获取特定组件的健康状态"""
            try:
                component = request.match_info['component']
                valid_components = ['network', 'services', 'devices']
                
                if component not in valid_components:
                    return web.json_response({
                        "error": f"Invalid component. Valid components: {valid_components}"
                    }, status=400)
                    
                # 对于设备组件，需要深度检查结果
                if component == 'devices':
                    result = self.health_service.get_last_check_result("deep")
                    if not result:
                        return web.json_response({
                            "error": "No deep health check data available. Run deep health check first."
                        }, status=404)
                else:
                    result = self.health_service.get_last_check_result("basic")
                    if not result:
                        # 执行基础检查
                        result = await self.health_service.perform_basic_check()
                        
                component_data = result['components'].get(component)
                if component_data:
                    return web.json_response({
                        "component": component,
                        "status": component_data['status'],
                        "details": component_data['details'],
                        "timestamp": result['timestamp']
                    })
                else:
                    return web.json_response({
                        "error": f"Component '{component}' not found in health check results"
                    }, status=404)
                    
            except Exception as e:
                logger.error(f"获取组件健康状态失败: {e}")
                return web.json_response({
                    "error": str(e)
                }, status=500)
                
        @self.routes.post('/health/deep/trigger')
        async def trigger_deep_check(request):
            """触发深度健康检查（用于任务执行前）"""
            try:
                # 检查授权
                auth_header = request.headers.get('Authorization', '')
                if not auth_header.startswith('Bearer '):
                    return web.json_response({
                        "error": "Unauthorized"
                    }, status=401)
                    
                # 检查是否有正在执行的任务（可选）
                data = await request.json()
                task_id = data.get('task_id')
                task_type = data.get('task_type')
                
                logger.info(f"任务 {task_id} ({task_type}) 触发深度健康检查")
                
                # 执行深度检查
                result = await self.health_service.perform_deep_check()
                
                # 返回结果和建议
                response = {
                    "task_id": task_id,
                    "health_check_result": result,
                    "recommendation": self._get_recommendation(result)
                }
                
                return web.json_response(response)
                
            except Exception as e:
                logger.error(f"触发深度检查失败: {e}")
                return web.json_response({
                    "error": str(e)
                }, status=500)
                
    def _get_recommendation(self, health_result: Dict[str, Any]) -> Dict[str, Any]:
        """根据健康检查结果给出建议"""
        overall_status = health_result.get('overall_status', 'unknown')
        
        if overall_status == HealthStatus.HEALTHY.value:
            return {
                "action": "proceed",
                "message": "系统健康，可以执行任务"
            }
        elif overall_status == HealthStatus.DEGRADED.value:
            # 分析具体问题
            issues = []
            components = health_result.get('components', {})
            
            for comp_name, comp_data in components.items():
                if comp_data['status'] != HealthStatus.HEALTHY.value:
                    issues.append(f"{comp_name}: {comp_data['status']}")
                    
            return {
                "action": "proceed_with_caution",
                "message": "系统部分降级，建议谨慎执行",
                "issues": issues
            }
        else:
            return {
                "action": "abort",
                "message": "系统不健康，建议中止任务执行"
            }
            
    def get_routes(self):
        """获取路由表"""
        return self.routes


def create_health_api_app():
    """创建健康检查API应用"""
    app = web.Application()
    health_api = HealthAPI()
    app.add_routes(health_api.get_routes())
    return app


if __name__ == "__main__":
    # 测试运行
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    app = create_health_api_app()
    web.run_app(app, host='0.0.0.0', port=8080)