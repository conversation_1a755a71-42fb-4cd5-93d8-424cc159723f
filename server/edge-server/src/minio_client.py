#!/usr/bin/env python3
"""
边缘服务器MinIO客户端服务
负责数据自动同步到中央服务器
"""
import os
import sys
import time
import json
import logging
import threading
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional

sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config.config_loader import ConfigLoader
from src.modules.task_bus.src.task_bus import TaskStatus
from minio_batch_uploader import get_batch_uploader, UploadPriority

try:
    from minio import Minio
    from minio.error import S3Error
except ImportError:
    print("请安装minio: pip install minio")
    sys.exit(1)

logger = logging.getLogger(__name__)

class MinioClientService:
    """MinIO客户端服务"""
    
    def __init__(self):
        self.config = ConfigLoader().config
        self.minio_config = self.config.get('minio', {})
        self.edge_id = self.config['system']['edge_id']
        
        # 初始化MinIO客户端
        self.client = Minio(
            self.minio_config.get('endpoint', 'localhost:9000'),
            access_key=self.minio_config.get('access_key', 'minioadmin'),
            secret_key=self.minio_config.get('secret_key', 'minioadmin'),
            secure=self.minio_config.get('secure', False)
        )
        
        self.bucket_name = self.minio_config.get('bucket_name', 'sensor-data')
        self.cache_dir = Path(self.minio_config.get('cache_dir', '/data/minio_cache'))
        self.cache_dir.mkdir(exist_ok=True)
        
        # 使用新的批量上传器
        self.batch_uploader = get_batch_uploader()
        
        # 保留原有上传队列用于兼容
        self.upload_queue = []
        self.upload_thread = None
        self.running = False
        
        # 是否使用批量上传
        self.use_batch_upload = self.minio_config.get('upload', {}).get('use_batch', True)
        
    def start(self):
        """启动服务"""
        logger.info("启动MinIO客户端服务")
        
        # 确保bucket存在
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                logger.info(f"创建bucket: {self.bucket_name}")
        except Exception as e:
            logger.error(f"MinIO连接失败: {e}")
            return False
        
        self.running = True
        
        if self.use_batch_upload:
            # 启动批量上传服务
            if not self.batch_uploader.start():
                logger.error("批量上传服务启动失败")
                return False
            logger.info("使用批量上传模式")
        else:
            # 启动原有上传线程
            self.upload_thread = threading.Thread(target=self._upload_worker)
            self.upload_thread.daemon = True
            self.upload_thread.start()
            logger.info("使用传统上传模式")
        
        return True
    
    def stop(self):
        """停止服务"""
        logger.info("停止MinIO客户端服务")
        self.running = False
        
        if self.use_batch_upload:
            self.batch_uploader.stop()
        elif self.upload_thread:
            self.upload_thread.join(timeout=5)
    
    def upload_task_data(self, task_id: str, data_type: str, file_path: str, 
                        metadata: Optional[Dict] = None, priority: str = "normal"):
        """上传任务数据"""
        # 构建对象路径
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        object_name = f"{self.edge_id}/tasks/{task_id}/{data_type}/{timestamp}_{Path(file_path).name}"
        
        if self.use_batch_upload:
            # 使用批量上传
            priority_enum = UploadPriority.HIGH if priority == "high" else \
                          UploadPriority.LOW if priority == "low" else \
                          UploadPriority.NORMAL
            
            task_metadata = metadata or {}
            task_metadata.update({
                'task-id': task_id,
                'data-type': data_type,
                'edge-id': self.edge_id
            })
            
            self.batch_uploader.add_upload_task(
                file_path=file_path,
                object_name=object_name,
                metadata=task_metadata,
                priority=priority_enum
            )
        else:
            # 使用原有上传方式
            upload_item = {
                'object_name': object_name,
                'file_path': file_path,
                'metadata': metadata or {},
                'retry_count': 0,
                'max_retries': 3
            }
            self.upload_queue.append(upload_item)
        
        logger.info(f"添加到上传队列: {object_name}")
    
    def upload_sensor_data(self, sensor_type: str, file_path: str, 
                          metadata: Optional[Dict] = None, priority: str = "normal"):
        """上传传感器数据"""
        # 构建对象路径
        date_str = datetime.now().strftime('%Y%m%d')
        timestamp = datetime.now().strftime('%H%M%S')
        filename = Path(file_path).name
        object_name = f"{self.edge_id}/{sensor_type}/{date_str}/{timestamp}_{filename}"
        
        # 添加元数据
        if metadata is None:
            metadata = {}
        metadata.update({
            'edge-id': self.edge_id,
            'sensor-type': sensor_type,
            'upload-time': datetime.now().isoformat(),
            'original-path': str(file_path)
        })
        
        if self.use_batch_upload:
            # 使用批量上传
            priority_enum = UploadPriority.HIGH if priority == "high" else \
                          UploadPriority.LOW if priority == "low" else \
                          UploadPriority.NORMAL
            
            self.batch_uploader.add_upload_task(
                file_path=file_path,
                object_name=object_name,
                metadata=metadata,
                priority=priority_enum
            )
        else:
            # 使用原有上传方式
            upload_item = {
                'object_name': object_name,
                'file_path': file_path,
                'metadata': metadata,
                'retry_count': 0,
                'max_retries': 3
            }
            self.upload_queue.append(upload_item)
        
        logger.info(f"添加传感器数据到上传队列: {object_name}")
    
    def _upload_worker(self):
        """上传工作线程"""
        while self.running:
            if not self.upload_queue:
                time.sleep(1)
                continue
            
            # 获取待上传项
            upload_item = self.upload_queue.pop(0)
            
            try:
                # 检查文件是否存在
                if not os.path.exists(upload_item['file_path']):
                    logger.error(f"文件不存在: {upload_item['file_path']}")
                    continue
                
                # 上传文件
                logger.info(f"开始上传: {upload_item['object_name']}")
                start_time = time.time()
                
                self.client.fput_object(
                    self.bucket_name,
                    upload_item['object_name'],
                    upload_item['file_path'],
                    metadata=upload_item['metadata']
                )
                
                upload_time = time.time() - start_time
                file_size = os.path.getsize(upload_item['file_path'])
                speed = file_size / upload_time / 1024 / 1024  # MB/s
                
                logger.info(f"上传成功: {upload_item['object_name']} "
                          f"({file_size/1024/1024:.2f}MB, {upload_time:.2f}s, {speed:.2f}MB/s)")
                
                # 上传成功后可以删除本地文件（可选）
                # os.remove(upload_item['file_path'])
                
            except Exception as e:
                logger.error(f"上传失败: {upload_item['object_name']}, 错误: {e}")
                
                # 重试逻辑
                upload_item['retry_count'] += 1
                if upload_item['retry_count'] < upload_item['max_retries']:
                    # 重新加入队列末尾
                    self.upload_queue.append(upload_item)
                    logger.info(f"将重试上传 ({upload_item['retry_count']}/{upload_item['max_retries']})")
                else:
                    logger.error(f"上传失败次数过多，放弃: {upload_item['object_name']}")
                    # 保存失败记录
                    self._save_failed_upload(upload_item)
    
    def _save_failed_upload(self, upload_item: Dict):
        """保存上传失败的记录"""
        failed_file = self.cache_dir / "failed_uploads.json"
        
        failed_list = []
        if failed_file.exists():
            with open(failed_file) as f:
                failed_list = json.load(f)
        
        failed_list.append({
            'timestamp': datetime.now().isoformat(),
            'upload_item': upload_item
        })
        
        with open(failed_file, 'w') as f:
            json.dump(failed_list, f, indent=2)
    
    def retry_failed_uploads(self):
        """重试失败的上传"""
        if self.use_batch_upload:
            self.batch_uploader.retry_failed_uploads()
        else:
            failed_file = self.cache_dir / "failed_uploads.json"
            if not failed_file.exists():
                return
            
            with open(failed_file) as f:
                failed_list = json.load(f)
            
            logger.info(f"重试 {len(failed_list)} 个失败的上传")
            
            for item in failed_list:
                upload_item = item['upload_item']
                upload_item['retry_count'] = 0  # 重置重试计数
                self.upload_queue.append(upload_item)
            
            # 清空失败记录
            failed_file.unlink()
    
    def get_upload_status(self) -> Dict:
        """获取上传状态"""
        if self.use_batch_upload:
            batch_status = self.batch_uploader.get_upload_status()
            return {
                'mode': 'batch',
                'is_running': self.running,
                'edge_id': self.edge_id,
                'bucket': self.bucket_name,
                'endpoint': self.minio_config.get('endpoint'),
                'batch_status': batch_status
            }
        else:
            return {
                'mode': 'simple',
                'queue_size': len(self.upload_queue),
                'is_running': self.running,
                'edge_id': self.edge_id,
                'bucket': self.bucket_name,
                'endpoint': self.minio_config.get('endpoint')
            }


# 单例实例
minio_client_service = None

def get_minio_client() -> MinioClientService:
    """获取MinIO客户端实例"""
    global minio_client_service
    if minio_client_service is None:
        minio_client_service = MinioClientService()
    return minio_client_service


if __name__ == "__main__":
    # 测试
    logging.basicConfig(level=logging.INFO)
    
    client = get_minio_client()
    if client.start():
        print("MinIO客户端服务启动成功")
        
        # 测试上传
        test_file = "/data/temp/test.txt"
        os.makedirs("/data/temp", exist_ok=True)
        with open(test_file, 'w') as f:
            f.write("测试数据\n" * 100)
        
        client.upload_sensor_data("test", test_file)
        
        # 等待上传完成
        time.sleep(5)
        
        status = client.get_upload_status()
        print(f"上传状态: {status}")
        
        client.stop()
    else:
        print("MinIO客户端服务启动失败")