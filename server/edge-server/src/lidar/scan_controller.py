"""
@file 激光雷达控制器模块
@module controllers.lidar_controller
@description 集成华为激光雷达SDK，实现数据采集和控制
@dependencies asyncio, numpy, pathlib
@created 2025-01-23
@status stable
"""

import asyncio
import numpy as np
from typing import Optional, Callable, List, Dict, Any
from datetime import datetime
from pathlib import Path
import json
from collections import deque
import time

# 确保导入路径正确
import sys
lidar_sdk_path = Path(__file__).parent.parent / "sdk/huawei_lidar"
if lidar_sdk_path.exists():
    sys.path.insert(0, str(lidar_sdk_path))

try:
    from huawei_lidar import (
        LidarClient, LidarStream, LidarScanner,
        LidarConfig, EchoMode,
        LidarError, LidarConnectionError, LidarTimeoutError
    )
    LIDAR_SDK_AVAILABLE = True
except ImportError as e:
    LIDAR_SDK_AVAILABLE = False
    LidarError = Exception
    LidarConnectionError = Exception
    LidarTimeoutError = Exception

from .logger import get_logger
from .config import LidarConfig as EdgeLidarConfig

# 为了兼容性，创建LidarControlError别名
LidarControlError = LidarError


class PointCloudFrame:
    """点云帧数据"""
    
    def __init__(self, points: np.ndarray, frame_id: int, timestamp: datetime):
        """
        初始化点云帧
        
        Args:
            points: 点云数据 (N x 4) [x, y, z, intensity]
            frame_id: 帧序号
            timestamp: 时间戳
        """
        self.points = points
        self.frame_id = frame_id
        self.timestamp = timestamp
        self.point_count = len(points) if points is not None else 0
    
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "frame_id": self.frame_id,
            "timestamp": self.timestamp.isoformat(),
            "point_count": self.point_count,
            "has_data": self.points is not None
        }


class LidarController:
    """激光雷达控制器"""
    
    def __init__(self, config: EdgeLidarConfig):
        """
        初始化激光雷达控制器
        
        Args:
            config: 边缘服务器雷达配置
        """
        self.config = config
        self.logger = get_logger("lidar_controller")
        
        if not LIDAR_SDK_AVAILABLE:
            self.logger.error("华为激光雷达SDK未安装或导入失败")
            
        self._client: Optional[LidarClient] = None
        self._stream: Optional[LidarStream] = None
        self._scanner: Optional[LidarScanner] = None
        
        self._is_streaming = False
        self._frame_count = 0
        self._frame_callback: Optional[Callable] = None
        self._buffer: deque = deque(maxlen=self.config.buffer_size)
        
        # 数据统计
        self._stats = {
            "total_frames": 0,
            "total_points": 0,
            "start_time": None,
            "last_frame_time": None,
            "fps": 0.0
        }
    
    def _create_sdk_config(self) -> 'LidarConfig':
        """创建SDK配置"""
        return LidarConfig(
            ip_address=self.config.host,
            local_ip="0.0.0.0",  # 监听所有接口
            local_port=self.config.port,
            frame_rate=self.config.frame_rate,
            buffer_size=self.config.buffer_size,
            timeout=float(self.config.timeout)
        )
    
    async def connect(self):
        """连接激光雷达"""
        if not LIDAR_SDK_AVAILABLE:
            raise LidarError("激光雷达SDK不可用")
            
        try:
            self.logger.info(f"连接激光雷达: {self.config.host}:{self.config.port}")
            
            # 创建客户端
            sdk_config = self._create_sdk_config()
            self._client = LidarClient(sdk_config)
            
            # 建立连接
            self._client.connect()
            
            # 创建数据流
            self._stream = self._client.stream()
            
            # 创建扫描器
            self._scanner = self._client.scanner()
            
            self.logger.info("激光雷达连接成功")
            
        except Exception as e:
            self.logger.error(f"激光雷达连接失败: {e}")
            raise LidarConnectionError(f"连接失败: {e}")
    
    async def disconnect(self):
        """断开连接"""
        if self._is_streaming:
            await self.stop_streaming()
            
        if self._client:
            try:
                self._client.disconnect()
                self._client = None
                self._stream = None
                self._scanner = None
                self.logger.info("激光雷达连接已断开")
            except Exception as e:
                self.logger.error(f"断开连接时出错: {e}")
    
    async def start_streaming(self, callback: Optional[Callable] = None):
        """
        开始数据流采集
        
        Args:
            callback: 数据回调函数
        """
        if not self._stream:
            raise LidarError("未连接到激光雷达")
            
        if self._is_streaming:
            self.logger.warning("数据流已在运行")
            return
            
        self.logger.info(f"开始数据流采集 (频率: {self.config.frame_rate}Hz)")
        
        # 设置回调
        self._frame_callback = callback
        
        # 重置统计
        self._stats["start_time"] = datetime.utcnow()
        self._stats["total_frames"] = 0
        self._stats["total_points"] = 0
        
        # 启动数据流
        def stream_callback(points: np.ndarray):
            """内部流回调"""
            asyncio.create_task(self._handle_frame(points))
        
        self._stream.start(stream_callback)
        self._is_streaming = True
        
        # 启动FPS计算任务
        asyncio.create_task(self._update_fps())
    
    async def stop_streaming(self):
        """停止数据流采集"""
        if not self._is_streaming:
            return
            
        if self._stream:
            self._stream.stop()
            
        self._is_streaming = False
        self.logger.info("数据流采集已停止")
        
        # 输出统计信息
        self._log_statistics()
    
    async def _handle_frame(self, points: np.ndarray):
        """处理点云帧"""
        self._frame_count += 1
        timestamp = datetime.utcnow()
        
        # 创建帧对象
        frame = PointCloudFrame(points, self._frame_count, timestamp)
        
        # 更新统计
        self._stats["total_frames"] += 1
        self._stats["total_points"] += frame.point_count
        self._stats["last_frame_time"] = timestamp
        
        # 添加到缓冲区（deque自动管理大小）
        self._buffer.append(frame)
        
        # 调用回调
        if self._frame_callback:
            try:
                await self._frame_callback(frame)
            except Exception as e:
                self.logger.error(f"帧回调错误: {e}")
        
        # 定期日志
        if self._frame_count % 100 == 0:
            self.logger.debug(f"已接收 {self._frame_count} 帧, "
                            f"最新帧包含 {frame.point_count} 个点")
    
    async def _update_fps(self):
        """更新FPS统计"""
        while self._is_streaming:
            await asyncio.sleep(1.0)
            
            if self._stats["last_frame_time"] and self._stats["start_time"]:
                duration = (self._stats["last_frame_time"] - self._stats["start_time"]).total_seconds()
                if duration > 0:
                    self._stats["fps"] = self._stats["total_frames"] / duration
    
    def _log_statistics(self):
        """记录统计信息"""
        if self._stats["start_time"] and self._stats["last_frame_time"]:
            duration = (self._stats["last_frame_time"] - self._stats["start_time"]).total_seconds()
            
            self.logger.info(
                f"数据采集统计:\n"
                f"  总帧数: {self._stats['total_frames']}\n"
                f"  总点数: {self._stats['total_points']:,}\n"
                f"  持续时间: {duration:.1f}秒\n"
                f"  平均FPS: {self._stats['fps']:.1f}\n"
                f"  平均点数/帧: {self._stats['total_points'] / max(1, self._stats['total_frames']):.0f}"
            )
    
    async def capture_single_frame(self) -> Optional[PointCloudFrame]:
        """
        捕获单帧数据
        
        Returns:
            点云帧或None
        """
        if not self._scanner:
            raise LidarError("未连接到激光雷达")
            
        try:
            self.logger.debug("捕获单帧数据")
            points = self._scanner.scan()
            
            if points is not None:
                self._frame_count += 1
                frame = PointCloudFrame(points, self._frame_count, datetime.utcnow())
                return frame
            else:
                self.logger.warning("未获取到点云数据")
                return None
                
        except Exception as e:
            self.logger.error(f"捕获单帧失败: {e}")
            return None
    
    async def save_pcd(self, frame: PointCloudFrame, filepath: Path):
        """
        保存点云为PCD格式
        
        Args:
            frame: 点云帧
            filepath: 保存路径
        """
        if frame.points is None:
            self.logger.warning("空帧，无法保存")
            return
            
        try:
            # 确保目录存在
            filepath.parent.mkdir(parents=True, exist_ok=True)
            
            # PCD文件头
            header = f"""# .PCD v0.7 - Point Cloud Data file format
VERSION 0.7
FIELDS x y z intensity
SIZE 4 4 4 4
TYPE F F F F
COUNT 1 1 1 1
WIDTH {frame.point_count}
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS {frame.point_count}
DATA ascii
"""
            
            # 写入文件
            with open(filepath, 'w') as f:
                f.write(header)
                for point in frame.points:
                    f.write(f"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {point[3]:.0f}\n")
            
            self.logger.debug(f"保存PCD文件: {filepath} ({frame.point_count} 点)")
            
        except Exception as e:
            self.logger.error(f"保存PCD文件失败: {e}")
            raise
    
    async def save_frame_sequence(self, output_dir: Path, duration: float = 10.0):
        """
        保存帧序列
        
        Args:
            output_dir: 输出目录
            duration: 采集时长（秒）
        """
        self.logger.info(f"开始保存帧序列，时长: {duration}秒")
        
        # 创建输出目录
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        scan_dir = output_dir / f"scan_{timestamp}"
        scan_dir.mkdir(parents=True, exist_ok=True)
        
        # 元数据
        metadata = {
            "start_time": datetime.utcnow().isoformat(),
            "config": {
                "host": self.config.host,
                "frame_rate": self.config.frame_rate,
                "duration": duration
            },
            "frames": []
        }
        
        # 帧保存回调
        saved_frames = []
        
        async def save_callback(frame: PointCloudFrame):
            filename = f"frame_{frame.frame_id:06d}.pcd"
            filepath = scan_dir / filename
            await self.save_pcd(frame, filepath)
            
            # 更新元数据
            frame_info = frame.to_dict()
            frame_info["filename"] = filename
            metadata["frames"].append(frame_info)
            saved_frames.append(frame)
        
        # 开始采集
        await self.start_streaming(save_callback)
        
        # 等待指定时长
        await asyncio.sleep(duration)
        
        # 停止采集
        await self.stop_streaming()
        
        # 保存元数据
        metadata["end_time"] = datetime.utcnow().isoformat()
        metadata["total_frames"] = len(saved_frames)
        
        metadata_file = scan_dir / "metadata.json"
        with open(metadata_file, 'w') as f:
            json.dump(metadata, f, indent=2)
        
        self.logger.info(f"帧序列保存完成: {scan_dir} (共 {len(saved_frames)} 帧)")
        
        return scan_dir
    
    def get_status(self) -> Dict[str, Any]:
        """获取控制器状态"""
        return {
            "connected": self._client is not None and self._client.is_connected(),
            "streaming": self._is_streaming,
            "config": {
                "host": self.config.host,
                "port": self.config.port,
                "frame_rate": self.config.frame_rate
            },
            "statistics": self._stats,
            "buffer_size": len(self._buffer)
        }
    
    def get_buffer_frames(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取缓冲区中的帧信息"""
        frames = self._buffer[-limit:]
        return [frame.to_dict() for frame in frames]