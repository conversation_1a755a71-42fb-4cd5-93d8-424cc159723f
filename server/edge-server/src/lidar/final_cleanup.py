#!/usr/bin/env python3
"""
最终清理脚本 - 关闭所有IO口，云台归零
"""

import asyncio
import aiohttp
import subprocess
from datetime import datetime
from pathlib import Path
import json

# 硬件配置 - 使用新的硬件控制API地址
HARDWARE_API_URL = "http://localhost:7080"
IO_CHANNEL = "d4ad2070b92f0000"
PTZ_CHANNEL = "0000000000000000"


async def check_data_files():
    """检查已保存的数据文件"""
    print("\n=== 检查数据文件 ===")
    
    data_dirs = [
        "/home/<USER>/edge-server/data/lidar/real_scans",
        "/home/<USER>/edge-server/data/lidar/scans",
        "/home/<USER>/edge-server/data/lidar/system_test",
        "/home/<USER>/edge-server/data/lidar/test_scans"
    ]
    
    all_scans = []
    
    for base_dir in data_dirs:
        base_path = Path(base_dir)
        if base_path.exists():
            print(f"\n目录: {base_dir}")
            for scan_dir in sorted(base_path.iterdir()):
                if scan_dir.is_dir():
                    # 读取元数据
                    metadata_file = scan_dir / "metadata.json"
                    scan_info = {
                        "path": str(scan_dir),
                        "name": scan_dir.name,
                        "has_metadata": metadata_file.exists()
                    }
                    
                    if metadata_file.exists():
                        try:
                            with open(metadata_file) as f:
                                metadata = json.load(f)
                                scan_info["type"] = metadata.get("scan_type", "unknown")
                                scan_info["timestamp"] = metadata.get("timestamp", "")
                        except:
                            pass
                    
                    # 统计数据文件
                    pcd_files = list(scan_dir.glob("**/*.pcd"))
                    bin_files = list(scan_dir.glob("**/*.bin"))
                    json_files = list(scan_dir.glob("**/*.json"))
                    
                    scan_info["pcd_count"] = len(pcd_files)
                    scan_info["bin_count"] = len(bin_files)
                    scan_info["json_count"] = len(json_files)
                    
                    all_scans.append(scan_info)
                    
                    print(f"  - {scan_dir.name}")
                    print(f"    类型: {scan_info.get('type', 'unknown')}")
                    print(f"    PCD文件: {scan_info['pcd_count']}")
                    print(f"    BIN文件: {scan_info['bin_count']}")
                    print(f"    JSON文件: {scan_info['json_count']}")
    
    # 找出定点扫描和地形扫描
    point_scans = [s for s in all_scans if s.get("type") == "point"]
    terrain_scans = [s for s in all_scans if s.get("type") == "terrain"]
    
    print(f"\n扫描统计:")
    print(f"- 总扫描数: {len(all_scans)}")
    print(f"- 定点扫描: {len(point_scans)}")
    print(f"- 地形扫描: {len(terrain_scans)}")
    
    # 最新的扫描数据
    if all_scans:
        latest = max(all_scans, key=lambda x: x.get("timestamp", ""))
        print(f"\n最新扫描:")
        print(f"- 路径: {latest['path']}")
        print(f"- 类型: {latest.get('type', 'unknown')}")
        
    return all_scans


async def check_all_io_status():
    """检查所有IO口状态"""
    print("\n=== 检查IO口状态 ===")
    
    async with aiohttp.ClientSession() as session:
        print("当前IO口状态:")
        open_channels = []
        
        for do_num in range(1, 9):
            params = {
                "address": 1,
                "doNumber": do_num,
                "channelName": IO_CHANNEL
            }
            
            try:
                async with session.get(
                    f"{API_BASE_URL}/test/relay/doCacheSearch",
                    params=params,
                    timeout=5
                ) as resp:
                    status = await resp.text()
                    is_open = status.lower() == "true"
                    print(f"  DO{do_num}: {'开启' if is_open else '关闭'}")
                    if is_open:
                        open_channels.append(do_num)
            except Exception as e:
                print(f"  DO{do_num}: 查询失败 - {e}")
                
        return open_channels


async def close_all_io():
    """关闭所有IO口"""
    print("\n=== 关闭所有IO口 ===")
    
    async with aiohttp.ClientSession() as session:
        for do_num in range(1, 9):
            data = {
                "channelName": IO_CHANNEL,
                "f": False,  # 关闭
                "doNumber": do_num,
                "address": 1
            }
            
            try:
                async with session.post(
                    f"{API_BASE_URL}/test/relay/doControl",
                    json=data,
                    timeout=5
                ) as resp:
                    if resp.status == 200:
                        print(f"  DO{do_num}: ✓ 已关闭")
                    else:
                        print(f"  DO{do_num}: 关闭失败 (状态码: {resp.status})")
            except Exception as e:
                print(f"  DO{do_num}: 关闭失败 - {e}")
                
    await asyncio.sleep(2)


async def reset_ptz():
    """云台归零 - 增强版，确保归零完成后才断电"""
    print("\n=== 云台归零 ===")
    
    async with aiohttp.ClientSession() as session:
        # 先确保云台有电
        print("1. 临时打开云台电源...")
        data = {
            "channelName": IO_CHANNEL,
            "f": True,
            "doNumber": 2,  # 云台在DO2
            "address": 1
        }
        await session.post(f"{HARDWARE_API_URL}/api/io/control", json=data)
        await asyncio.sleep(3)
        
        # 设置速度
        print("2. 设置云台速度...")
        for direction in ["PAN", "TILT"]:
            speed_data = {
                "address": 30,
                "direction": direction,
                "speed": 3,
                "channelName": PTZ_CHANNEL
            }
            await session.post(f"{HARDWARE_API_URL}/api/ptz/speed", json=speed_data)
        
        # 多次尝试归零直到成功
        max_attempts = 3
        home_success = False
        
        for attempt in range(max_attempts):
            print(f"\n3. 归零尝试 {attempt + 1}/{max_attempts}...")
            
            # 发送归零命令
            for direction in ["PAN", "TILT"]:
                move_data = {
                    "address": 30,
                    "direction": direction,
                    "angle": 0,
                    "channelName": PTZ_CHANNEL
                }
                await session.post(f"{HARDWARE_API_URL}/api/ptz/angle", json=move_data)
                print(f"   {direction}: 归零命令已发送")
            
            # 等待归零完成
            print("   等待云台归零...")
            await asyncio.sleep(8)
            
            # 验证位置
            print("4. 验证云台位置...")
            pan_angle = None
            tilt_angle = None
            
            for direction in ["PAN", "TILT"]:
                params = {
                    "slaveAddress": 30,
                    "direction": direction,
                    "channelName": PTZ_CHANNEL
                }
                
                try:
                    async with session.get(
                        f"{API_BASE_URL}/test/ptz/getAngleValue",
                        params=params,
                        timeout=5
                    ) as resp:
                        angle_str = await resp.text()
                        angle = float(angle_str.strip())
                        print(f"   {direction}: {angle}°")
                        
                        if direction == "PAN":
                            pan_angle = angle
                        else:
                            tilt_angle = angle
                except:
                    print(f"   {direction}: 无法查询")
            
            # 检查是否真正归零
            if pan_angle is not None and tilt_angle is not None:
                if abs(pan_angle) <= 1.0 and abs(tilt_angle) <= 1.0:
                    print("   ✓ 云台已成功归零")
                    home_success = True
                    break
                else:
                    print(f"   ✗ 云台未完全归零，位置误差过大")
        
        if home_success:
            print("\n5. 云台已确认归零，现在关闭电源...")
            data["f"] = False
            await session.post(f"{HARDWARE_API_URL}/api/io/control", json=data)
            print("   ✓ 云台电源已关闭")
        else:
            print("\n✗ 警告: 云台未能成功归零，保持电源以便手动处理")
        
        return home_success


async def verify_shutdown():
    """验证关闭状态"""
    print("\n=== 验证最终状态 ===")
    
    # 检查激光雷达
    print("1. 激光雷达状态:")
    result = subprocess.run(
        ['ping', '-c', '1', '-W', '1', '192.168.1.201'],
        capture_output=True
    )
    if result.returncode == 0:
        print("   ✗ 激光雷达仍在线！")
    else:
        print("   ✓ 激光雷达已离线")
        
    # 检查所有IO口
    print("\n2. IO口最终状态:")
    open_channels = await check_all_io_status()
    
    if not open_channels:
        print("\n✓ 所有IO口已关闭")
    else:
        print(f"\n✗ 仍有 {len(open_channels)} 个IO口开启: {open_channels}")


async def main():
    """主程序"""
    print("激光雷达系统最终清理")
    print("=" * 60)
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查数据文件
    scans = await check_data_files()
    
    # 2. 检查当前IO状态
    open_channels = await check_all_io_status()
    
    if open_channels:
        print(f"\n发现 {len(open_channels)} 个IO口开启，需要关闭")
        
        # 3. 云台归零
        ptz_home_success = await reset_ptz()
        
        if ptz_home_success:
            # 4. 关闭所有IO口（只有在云台归零成功后）
            await close_all_io()
        else:
            print("\n由于云台未能归零，跳过IO口关闭")
            print("请手动处理云台位置后再关闭电源")
        
        # 5. 验证最终状态
        await verify_shutdown()
    else:
        print("\n所有IO口已经关闭")
        
    print("\n" + "=" * 60)
    print("清理完成！")
    print("\n系统状态:")
    print("- 所有IO口: 已关闭")
    print("- 云台位置: 已归零")
    print("- 激光雷达: 已断电")
    
    # 数据文件总结
    if scans:
        print(f"\n数据文件已保存:")
        print(f"- 总共 {len(scans)} 个扫描")
        
        # 找出有实际数据的扫描
        with_data = [s for s in scans if s.get("pcd_count", 0) > 0 or s.get("bin_count", 0) > 0]
        if with_data:
            print(f"- 包含点云数据的扫描: {len(with_data)} 个")
            for scan in with_data[:3]:  # 显示前3个
                print(f"  • {scan['path']}")


if __name__ == "__main__":
    asyncio.run(main())