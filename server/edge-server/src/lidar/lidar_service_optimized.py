"""
优化的激光雷达服务
实现模块分层、环形缓冲区、批量数据处理等优化
"""

import asyncio
import logging
import time
import numpy as np
from typing import Dict, Any, Optional, List, Tuple, Callable
from datetime import datetime
from collections import deque
from dataclasses import dataclass
from enum import Enum
import threading

logger = logging.getLogger(__name__)


class DataProcessingMode(Enum):
    """数据处理模式"""
    CONTINUOUS = "continuous"  # 连续流模式
    ACCUMULATE = "accumulate"  # 累积模式
    BATCH = "batch"           # 批量模式


@dataclass
class LidarFrame:
    """激光雷达数据帧"""
    timestamp: float
    point_cloud: np.ndarray  # N x 4 (x, y, z, intensity)
    pan_angle: float
    tilt_angle: float
    frame_id: int
    
    @property
    def point_count(self) -> int:
        return len(self.point_cloud)
    
    @property
    def size_bytes(self) -> int:
        return self.point_cloud.nbytes


class RingBuffer:
    """环形缓冲区实现"""
    
    def __init__(self, capacity: int, size_limit_mb: float = 100):
        """
        初始化环形缓冲区
        
        Args:
            capacity: 最大帧数
            size_limit_mb: 最大内存限制(MB)
        """
        self.capacity = capacity
        self.size_limit_bytes = size_limit_mb * 1024 * 1024
        self.buffer = deque(maxlen=capacity)
        self.total_size = 0
        self.lock = threading.Lock()
        self.stats = {
            'frames_added': 0,
            'frames_dropped': 0,
            'total_points': 0
        }
    
    def add(self, frame: LidarFrame) -> bool:
        """添加帧到缓冲区"""
        with self.lock:
            frame_size = frame.size_bytes
            
            # 检查大小限制
            while self.total_size + frame_size > self.size_limit_bytes and self.buffer:
                # 移除最旧的帧
                old_frame = self.buffer.popleft()
                self.total_size -= old_frame.size_bytes
                self.stats['frames_dropped'] += 1
            
            # 添加新帧
            self.buffer.append(frame)
            self.total_size += frame_size
            self.stats['frames_added'] += 1
            self.stats['total_points'] += frame.point_count
            
            return True
    
    def get_batch(self, count: int) -> List[LidarFrame]:
        """获取批量数据"""
        with self.lock:
            if count >= len(self.buffer):
                return list(self.buffer)
            return list(self.buffer)[-count:]
    
    def get_all(self) -> List[LidarFrame]:
        """获取所有数据"""
        with self.lock:
            return list(self.buffer)
    
    def clear(self):
        """清空缓冲区"""
        with self.lock:
            self.buffer.clear()
            self.total_size = 0
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        with self.lock:
            return {
                'current_frames': len(self.buffer),
                'capacity': self.capacity,
                'size_mb': self.total_size / (1024 * 1024),
                'frames_added': self.stats['frames_added'],
                'frames_dropped': self.stats['frames_dropped'],
                'total_points': self.stats['total_points']
            }


class DataProcessor:
    """数据处理层"""
    
    def __init__(self, processing_mode: DataProcessingMode = DataProcessingMode.CONTINUOUS):
        self.processing_mode = processing_mode
        self.processors: List[Callable] = []
        self.processed_count = 0
        self.processing_time_total = 0
    
    def add_processor(self, processor: Callable):
        """添加数据处理器"""
        self.processors.append(processor)
    
    async def process_frame(self, frame: LidarFrame) -> Optional[Dict]:
        """处理单帧数据"""
        start_time = time.time()
        
        try:
            result = {'frame_id': frame.frame_id, 'timestamp': frame.timestamp}
            
            # 应用所有处理器
            for processor in self.processors:
                if asyncio.iscoroutinefunction(processor):
                    proc_result = await processor(frame)
                else:
                    proc_result = processor(frame)
                
                if proc_result:
                    result.update(proc_result)
            
            # 更新统计
            self.processed_count += 1
            self.processing_time_total += time.time() - start_time
            
            return result
            
        except Exception as e:
            logger.error(f"Frame processing error: {e}")
            return None
    
    async def process_batch(self, frames: List[LidarFrame]) -> List[Dict]:
        """批量处理数据"""
        if self.processing_mode == DataProcessingMode.BATCH:
            # 批量处理优化
            tasks = [self.process_frame(frame) for frame in frames]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            return [r for r in results if r and not isinstance(r, Exception)]
        else:
            # 逐帧处理
            results = []
            for frame in frames:
                result = await self.process_frame(frame)
                if result:
                    results.append(result)
            return results
    
    def get_stats(self) -> Dict:
        """获取处理统计"""
        avg_time = 0
        if self.processed_count > 0:
            avg_time = self.processing_time_total / self.processed_count
        
        return {
            'processed_count': self.processed_count,
            'avg_processing_time_ms': avg_time * 1000,
            'processing_mode': self.processing_mode.value,
            'processor_count': len(self.processors)
        }


class OptimizedLidarService:
    """优化的激光雷达服务"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.lidar_ip = config.get('lidar_ip', '*************')
        self.data_port = config.get('data_port', 2368)
        
        # 硬件接口层
        self.hardware_interface = None
        
        # 数据处理层
        self.ring_buffer = RingBuffer(
            capacity=config.get('buffer_capacity', 1000),
            size_limit_mb=config.get('buffer_size_mb', 100)
        )
        self.data_processor = DataProcessor()
        
        # 业务逻辑层
        self.current_scan = None
        self.scan_stats = {
            'total_frames': 0,
            'total_points': 0,
            'start_time': None,
            'scan_positions': []
        }
        
        # 数据接收任务
        self.receiver_task = None
        self.running = False
        
        # 性能监控
        self.performance_stats = {
            'frame_rate': 0,
            'data_rate_mbps': 0,
            'cpu_usage': 0,
            'memory_usage_mb': 0
        }
        
    async def initialize(self):
        """初始化服务"""
        try:
            # 设置数据处理器
            self._setup_processors()
            
            # 初始化硬件接口
            # TODO: 实际硬件接口初始化
            
            logger.info("Optimized LiDAR service initialized")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize service: {e}")
            return False
    
    def _setup_processors(self):
        """设置数据处理器"""
        # 基础统计处理器
        def stats_processor(frame: LidarFrame) -> Dict:
            return {
                'point_count': frame.point_count,
                'min_z': np.min(frame.point_cloud[:, 2]),
                'max_z': np.max(frame.point_cloud[:, 2]),
                'mean_intensity': np.mean(frame.point_cloud[:, 3])
            }
        
        # 滤波处理器
        def filter_processor(frame: LidarFrame) -> Dict:
            # 移除离群点
            z_values = frame.point_cloud[:, 2]
            z_mean = np.mean(z_values)
            z_std = np.std(z_values)
            
            # 3-sigma滤波
            mask = np.abs(z_values - z_mean) < 3 * z_std
            filtered_count = np.sum(~mask)
            
            return {
                'filtered_points': filtered_count,
                'filter_ratio': filtered_count / frame.point_count
            }
        
        self.data_processor.add_processor(stats_processor)
        self.data_processor.add_processor(filter_processor)
    
    async def start_continuous_scan(self, duration: int = 60) -> Dict:
        """启动连续扫描"""
        try:
            self.current_scan = {
                'type': 'continuous',
                'start_time': time.time(),
                'duration': duration
            }
            
            # 设置处理模式
            self.data_processor.processing_mode = DataProcessingMode.CONTINUOUS
            
            # 启动数据接收
            self.running = True
            self.receiver_task = asyncio.create_task(self._receive_data_loop())
            
            # 等待扫描完成
            await asyncio.sleep(duration)
            
            # 停止接收
            self.running = False
            if self.receiver_task:
                await self.receiver_task
            
            # 获取结果
            frames = self.ring_buffer.get_all()
            
            return {
                'success': True,
                'frame_count': len(frames),
                'total_points': sum(f.point_count for f in frames),
                'buffer_stats': self.ring_buffer.get_stats(),
                'processor_stats': self.data_processor.get_stats()
            }
            
        except Exception as e:
            logger.error(f"Continuous scan failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def start_position_scan(self, positions: List[Tuple[float, float]], 
                                 duration_per_position: int = 30) -> Dict:
        """启动多位置扫描"""
        try:
            self.current_scan = {
                'type': 'position',
                'positions': positions,
                'start_time': time.time()
            }
            
            # 设置处理模式
            self.data_processor.processing_mode = DataProcessingMode.ACCUMULATE
            
            results = []
            
            for i, (pan, tilt) in enumerate(positions):
                logger.info(f"Scanning position {i+1}/{len(positions)}: pan={pan}, tilt={tilt}")
                
                # 移动到位置
                # TODO: 调用PTZ控制
                await asyncio.sleep(2)  # 等待移动
                
                # 清空缓冲区
                self.ring_buffer.clear()
                
                # 在该位置扫描
                self.running = True
                self.receiver_task = asyncio.create_task(self._receive_data_loop())
                
                await asyncio.sleep(duration_per_position)
                
                self.running = False
                if self.receiver_task:
                    await self.receiver_task
                
                # 处理该位置的数据
                frames = self.ring_buffer.get_all()
                position_result = await self._process_position_data(pan, tilt, frames)
                results.append(position_result)
            
            return {
                'success': True,
                'position_count': len(positions),
                'results': results,
                'total_frames': sum(r['frame_count'] for r in results),
                'total_points': sum(r['total_points'] for r in results)
            }
            
        except Exception as e:
            logger.error(f"Position scan failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _receive_data_loop(self):
        """数据接收循环"""
        frame_id = 0
        last_stats_time = time.time()
        frames_since_stats = 0
        bytes_since_stats = 0
        
        while self.running:
            try:
                # 模拟数据接收
                # TODO: 实际的UDP数据接收
                await asyncio.sleep(0.1)  # 10Hz
                
                # 生成模拟数据帧
                frame = self._generate_mock_frame(frame_id)
                frame_id += 1
                
                # 添加到缓冲区
                self.ring_buffer.add(frame)
                
                # 更新统计
                frames_since_stats += 1
                bytes_since_stats += frame.size_bytes
                
                # 每秒更新性能统计
                current_time = time.time()
                if current_time - last_stats_time >= 1.0:
                    self.performance_stats['frame_rate'] = frames_since_stats
                    self.performance_stats['data_rate_mbps'] = (bytes_since_stats / (1024 * 1024)) * 8
                    
                    frames_since_stats = 0
                    bytes_since_stats = 0
                    last_stats_time = current_time
                    
            except Exception as e:
                logger.error(f"Data receive error: {e}")
                await asyncio.sleep(0.1)
    
    def _generate_mock_frame(self, frame_id: int) -> LidarFrame:
        """生成模拟数据帧（测试用）"""
        # 生成随机点云数据
        num_points = np.random.randint(1000, 5000)
        points = np.random.randn(num_points, 4)
        points[:, 3] = np.random.uniform(0, 255, num_points)  # 强度值
        
        return LidarFrame(
            timestamp=time.time(),
            point_cloud=points,
            pan_angle=0,
            tilt_angle=0,
            frame_id=frame_id
        )
    
    async def _process_position_data(self, pan: float, tilt: float, 
                                   frames: List[LidarFrame]) -> Dict:
        """处理单个位置的数据"""
        if not frames:
            return {
                'position': {'pan': pan, 'tilt': tilt},
                'frame_count': 0,
                'total_points': 0
            }
        
        # 批量处理
        processed_results = await self.data_processor.process_batch(frames)
        
        # 聚合结果
        total_points = sum(f.point_count for f in frames)
        avg_intensity = np.mean([r.get('mean_intensity', 0) for r in processed_results])
        
        return {
            'position': {'pan': pan, 'tilt': tilt},
            'frame_count': len(frames),
            'total_points': total_points,
            'avg_intensity': avg_intensity,
            'processing_stats': self.data_processor.get_stats()
        }
    
    def get_status(self) -> Dict:
        """获取服务状态"""
        return {
            'running': self.running,
            'current_scan': self.current_scan,
            'buffer_stats': self.ring_buffer.get_stats(),
            'processor_stats': self.data_processor.get_stats(),
            'performance_stats': self.performance_stats
        }
    
    async def stop(self):
        """停止服务"""
        self.running = False
        
        if self.receiver_task:
            self.receiver_task.cancel()
            try:
                await self.receiver_task
            except asyncio.CancelledError:
                pass
        
        # 清理资源
        self.ring_buffer.clear()
        
        logger.info("Optimized LiDAR service stopped")