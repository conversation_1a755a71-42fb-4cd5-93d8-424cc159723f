# 激光雷达模块

## 功能概述
集成华为激光雷达SDK，实现点云数据采集和云台控制的边缘计算模块。

## 主要功能
- **定点扫描**：云台定位到指定角度进行数据采集
- **地形扫描**：多层俯仰角度的360度全景扫描
- **数据采集模式**：
  - 实时流式（10Hz）
  - 长时间累积

## 核心组件
- `lidar_controller.py` - 激光雷达SDK封装
- `scan_service.py` - 业务逻辑实现
- `config.py` - 配置管理
- `emergency_shutdown.py` - 紧急关闭

## 硬件配置
- 激光雷达：DO1，IP: *************
- 云台：DO2（注意：不是DO3）

## 使用示例
```python
from lidar import ScanService, LidarConfig
from hardware import HardwareManagerConfig

# 初始化服务
lidar_config = LidarConfig()
hardware_config = HardwareManagerConfig()
service = ScanService(lidar_config, hardware_config)

# 执行定点扫描
result = await service.execute_point_scan(
    task_id="test_001",
    pan_angle=45.0,
    tilt_angle=30.0,
    duration=10.0
)
```

## 注意事项
1. 任务结束必须验证云台归零后才能断电
2. 激光雷达不能长时间工作，需要及时断电
3. 异常情况下执行 `emergency_shutdown.py`

## 详细文档
参见 [BUSINESS_LOGIC_IMPLEMENTATION.md](./BUSINESS_LOGIC_IMPLEMENTATION.md)