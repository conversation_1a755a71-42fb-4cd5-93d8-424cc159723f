"""
扫描服务适配器
提供向后兼容的接口，内部调用hardware模块的集成扫描服务
"""

from typing import Dict, Any, Tuple
import sys
from pathlib import Path

# 导入hardware模块的集成扫描服务
from ..hardware.integrated_scan_service import IntegratedScanService, ScanMode, DataAcquisitionMode


class ScanService:
    """扫描服务适配器 - 保持原有接口"""
    
    def __init__(self, lidar_config=None, hardware_config=None):
        """
        初始化扫描服务
        
        Args:
            lidar_config: 激光雷达配置（用于兼容）
            hardware_config: 硬件配置
        """
        # 构建硬件配置
        if hardware_config is None:
            hardware_config = {
                'base_url': 'http://localhost:7080',
                'channel_id': 'd4ad2070b92f0000',
                'lidar_ip': '*************',
                'lidar_port': 5000
            }
            
        # 如果提供了lidar_config，从中提取IP和端口
        if lidar_config:
            hardware_config['lidar_ip'] = getattr(lidar_config, 'ip', '*************')
            hardware_config['lidar_port'] = getattr(lidar_config, 'port', 5000)
            
        # 创建集成扫描服务实例
        self._integrated_service = IntegratedScanService(lidar_config, hardware_config)
        
    async def initialize(self):
        """初始化服务"""
        return await self._integrated_service.initialize()
        
    async def shutdown(self):
        """关闭服务"""
        self._integrated_service.shutdown()
        
    async def execute_point_scan(
        self,
        task_id: str,
        pan_angle: float,
        tilt_angle: float,
        duration: float,
        data_mode: str = DataAcquisitionMode.CONTINUOUS
    ) -> Dict[str, Any]:
        """执行定点扫描"""
        return await self._integrated_service.execute_point_scan(
            task_id, pan_angle, tilt_angle, duration, data_mode
        )
        
    async def execute_terrain_scan(
        self,
        task_id: str,
        pan_range: Tuple[float, float] = (0, 360),
        tilt_range: Tuple[float, float] = (0, 60),
        pan_step: float = 30,
        tilt_step: float = 10,
        scan_duration_per_position: float = 30,
        data_mode: str = DataAcquisitionMode.CONTINUOUS
    ) -> Dict[str, Any]:
        """执行地形扫描"""
        return await self._integrated_service.execute_terrain_scan(
            task_id, pan_range, tilt_range, pan_step, tilt_step,
            scan_duration_per_position, data_mode
        )
        
    async def stop_current_scan(self):
        """停止当前扫描"""
        return await self._integrated_service.stop_current_scan()
        
    def get_scan_status(self) -> Dict[str, Any]:
        """获取扫描状态"""
        return self._integrated_service.get_scan_status()


# 导出兼容的类和常量
__all__ = ['ScanService', 'ScanMode', 'DataAcquisitionMode']