# LiDAR业务服务层API文档

## 概述

LiDAR业务服务层提供了高层自动化接口，简化了激光雷达的使用流程。它自动处理：
- 设备电源管理
- 设备状态验证
- 扫描流程控制
- 错误处理和重试
- 安全关闭流程

## 架构设计

```
lidar_module.py (任务总线接口)
    └── lidar_business_service.py (业务逻辑层)
            └── scan_service_adapter.py (扫描服务适配器)
                    └── integrated_scan_service.py (硬件集成服务)
```

## 新增任务接口

### 1. 自动化定点扫描 (automated_point_scan)

执行完整的自动化定点扫描流程。

**任务格式：**
```python
{
    "module": "lidar",
    "action": "automated_point_scan",
    "params": {
        "position": {
            "horizontal": 0,  # 水平角度 (-180 to 180)
            "vertical": 0     # 垂直角度 (-90 to 90)
        },
        "duration": 60,       # 扫描时长（秒）
        "data_mode": "continuous"  # 数据模式：continuous/accumulate
    }
}
```

**自动化流程：**
1. 打开LiDAR电源 (DO1)
2. 打开PTZ电源 (DO2)
3. 等待30秒设备启动
4. Ping测试LiDAR (*************)
5. PTZ运动测试
6. 执行定点扫描
7. 关闭设备电源
8. 验证设备已关闭

### 2. 自动化地形扫描 (automated_terrain_scan)

执行完整的自动化地形扫描流程。

**任务格式：**
```python
{
    "module": "lidar",
    "action": "automated_terrain_scan",
    "params": {
        "scan_area": {
            "h_start": -45,   # 水平起始角度
            "h_end": 45,      # 水平结束角度
            "v_start": -30,   # 垂直起始角度
            "v_end": 30       # 垂直结束角度
        },
        "h_step": 1,          # 水平步进角度
        "v_step": 1,          # 垂直步进角度
        "scan_duration_per_position": 30,  # 每个位置扫描时长
        "data_mode": "accumulate"  # 数据模式
    }
}
```

### 3. 获取业务服务状态 (get_business_status)

获取当前业务服务的状态信息。

**任务格式：**
```python
{
    "module": "lidar",
    "action": "get_business_status",
    "params": {}
}
```

**返回数据：**
```python
{
    "device_status": "ready",  # 设备状态
    "current_workflow": null,  # 当前工作流ID
    "lidar_ip": "*************",
    "scan_service_ready": true
}
```

## 设备状态说明

- `UNKNOWN`: 未知状态
- `OFF`: 设备已关闭
- `STARTING`: 设备正在启动
- `READY`: 设备就绪
- `SCANNING`: 正在扫描
- `ERROR`: 错误状态
- `SHUTTING_DOWN`: 正在关闭

## 错误处理

业务服务层实现了完善的错误处理：

1. **启动失败处理**：如果设备启动失败，自动关闭已开启的设备
2. **连接失败重试**：Ping测试失败时自动重试（默认5次）
3. **扫描异常处理**：扫描过程中出现异常时，自动执行安全关闭流程
4. **紧急关闭**：提供紧急关闭接口，确保设备安全

## 使用示例

### Python代码示例

```python
from task_bus import TaskBus

# 创建任务总线
bus = TaskBus()

# 执行自动化定点扫描
result = bus.submit_task(
    'scan_001',
    'lidar',
    'automated_point_scan',
    {
        'position': {'horizontal': 0, 'vertical': 0},
        'duration': 60,
        'data_mode': 'continuous'
    }
)

if result.success:
    print(f"扫描成功: {result.data}")
else:
    print(f"扫描失败: {result.error}")
```

### 命令行示例

```bash
# 自动化定点扫描
python -m edge_server.cli task submit \
    --module lidar \
    --action automated_point_scan \
    --params '{"position":{"horizontal":0,"vertical":0},"duration":60}'

# 自动化地形扫描
python -m edge_server.cli task submit \
    --module lidar \
    --action automated_terrain_scan \
    --params '{"scan_area":{"h_start":-45,"h_end":45,"v_start":-30,"v_end":30},"h_step":5,"v_step":5}'
```

## 配置参数

业务服务支持以下配置参数：

```python
{
    "lidar_ip": "*************",      # LiDAR IP地址
    "startup_wait_time": 30,          # 启动等待时间（秒）
    "ping_retries": 5,                # Ping重试次数
    "ping_timeout": 2,                # Ping超时时间（秒）
    "data_dir": "/home/<USER>/edge-server/data/lidar"  # 数据存储目录
}
```

## 注意事项

1. **电源管理**：自动化接口会自动管理设备电源，无需手动控制
2. **安全性**：扫描完成后会自动将PTZ归零并关闭所有设备
3. **并发限制**：同一时间只能执行一个自动化工作流
4. **超时处理**：默认任务超时时间为60秒，可根据扫描时长调整