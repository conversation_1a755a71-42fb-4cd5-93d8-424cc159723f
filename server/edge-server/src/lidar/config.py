"""
激光雷达配置模块
基于华为LiDAR SDK标准配置规范
"""

from dataclasses import dataclass
from enum import IntEnum
from typing import Optional


class EchoMode(IntEnum):
    """激光雷达回波模式（华为SDK标准）"""
    SINGLE_STRONGEST = 0x30        # 单回波最强
    DUAL_STRONGEST_FIRST = 0x41    # 双回波最强优先


@dataclass
class LidarConfig:
    """华为激光雷达SDK标准配置类"""
    
    # 网络配置（华为SDK标准参数）
    ip_address: str = "*************"      # 激光雷达IP地址
    local_ip: str = "*************"        # 本地绑定IP（边缘服务器IP）
    local_port: int = 38000                # 本地监听端口
    multicast_ip: str = "***********"     # 多播IP
    point_cloud_port: int = 2368           # 点云数据端口  
    management_port: int = 58000           # 管理端口（华为SDK标准）
    
    # SDK配置参数
    frame_rate: int = 10                   # 帧率（5, 10, 20 Hz）
    echo_mode: EchoMode = EchoMode.SINGLE_STRONGEST  # 回波模式
    buffer_size: int = 10                  # SDK缓冲区大小
    timeout: float = 1.0                   # SDK超时时间（秒）
    
    def validate(self) -> None:
        """验证华为SDK配置参数"""
        # 验证IP地址格式
        for ip in [self.ip_address, self.local_ip, self.multicast_ip]:
            parts = ip.split('.')
            if len(parts) != 4:
                raise ValueError(f"无效的IP地址: {ip}")
            for part in parts:
                try:
                    num = int(part)
                    if num < 0 or num > 255:
                        raise ValueError(f"无效的IP地址: {ip}")
                except ValueError:
                    raise ValueError(f"无效的IP地址: {ip}")
        
        # 验证端口范围
        for port in [self.local_port, self.point_cloud_port, self.management_port]:
            if port < 1 or port > 65535:
                raise ValueError(f"无效的端口号: {port}")
        
        # 验证帧率（华为SDK支持的帧率）
        if self.frame_rate not in [5, 10, 20]:
            raise ValueError(f"无效的帧率: {self.frame_rate}. 必须是5, 10, 或20")
        
        # 验证缓冲区大小
        if self.buffer_size < 1 or self.buffer_size > 100:
            raise ValueError(f"无效的缓冲区大小: {self.buffer_size}. 必须在1-100之间")
        
        # 验证超时时间
        if self.timeout <= 0:
            raise ValueError(f"无效的超时时间: {self.timeout}. 必须大于0")


@dataclass  
class BusinessConfig:
    """业务层配置（边缘服务器特定）"""
    
    data_dir: str = "/data/lidar"
    point_scan_duration: int = 60          # 定点扫描默认时长（秒）
    terrain_scan_duration: int = 300       # 地形扫描默认时长（秒） 
    points_threshold: int = 50000          # 点数阈值
    resolution: float = 1.0                # 扫描分辨率
    horizontal_range: tuple = (-45, 45)    # 水平扫描范围（度）
    vertical_range: tuple = (-30, 30)      # 垂直扫描范围（度）