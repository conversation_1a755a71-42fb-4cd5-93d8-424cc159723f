"""
激光雷达硬件配置
根据实际测试发现的正确配置
注意：主配置已迁移到 /config/config.yaml，此文件保留作为代码中的常量定义
"""

# IO控制配置常量
IO_CHANNEL = "d4ad2070b92f0000"  # IO通道（*************服务器）
LIDAR_DO_PORT = 1  # 激光雷达在DO1口
PTZ_DO_PORT = 2    # 云台在DO2口

# 云台控制配置常量
PTZ_CHANNEL = "0000000000000000"  # 云台通道（主云台）
PTZ_ADDRESS = 30     # 云台485地址码
PTZ_BAUD_RATE = 9600 # 云台485波特率

# 网络配置常量
LIDAR_IP = "*************"          # 激光雷达IP地址
SERIAL_SERVER_IP = "*************"  # 串口服务器IP

# 设备启动等待时间常量
LIDAR_STARTUP_TIME = 10  # 激光雷达启动时间（秒）
PTZ_STARTUP_TIME = 3     # 云台启动时间（秒）

# 云台控制参数常量
PTZ_DEFAULT_SPEED = 10.0     # 默认速度（度/秒）
PTZ_ANGLE_TOLERANCE = 1      # 角度容差（度）

# 云台角度范围常量
PTZ_PAN_MIN = 0              # 水平最小角度
PTZ_PAN_MAX = 359            # 水平最大角度（0-359度）
PTZ_TILT_MIN = -60           # 俯仰最小角度
PTZ_TILT_MAX = 60            # 俯仰最大角度（-60到60度）

# 云台速度常量（基于硬件速度表）
PTZ_SPEED_PRESETS = {
    "slow": 3.0,             # 慢速
    "normal": 10.0,          # 正常速度
    "fast": 15.0,            # 快速
    "max_pan": 19.1,         # 水平最大速度（度/秒）
    "max_tilt": 10.0         # 俯仰最大速度（度/秒）
}

# 硬件速度表长度
PTZ_PAN_SPEED_TABLE_SIZE = 64    # 水平速度表：64个速度值（0-63索引）
PTZ_TILT_SPEED_TABLE_SIZE = 64   # 俯仰速度表：64个速度值（0-63索引）

def get_hardware_config():
    """获取硬件管理器配置"""
    return {
        "io_channel": IO_CHANNEL,
        "ptz_channel": PTZ_CHANNEL,
        "lidar_do": LIDAR_DO_PORT,
        "ptz_do": PTZ_DO_PORT,
        "ptz_address": PTZ_ADDRESS,
        "ptz_baud_rate": PTZ_BAUD_RATE,
        "lidar_ip": LIDAR_IP,
        "serial_server_ip": SERIAL_SERVER_IP,
        "retry_count": 3,
        "timeout": 10,
        "ptz_default_speed": PTZ_DEFAULT_SPEED,
        "ptz_angle_tolerance": PTZ_ANGLE_TOLERANCE,
        "ptz_angle_range": {
            "pan": {"min": PTZ_PAN_MIN, "max": PTZ_PAN_MAX},
            "tilt": {"min": PTZ_TILT_MIN, "max": PTZ_TILT_MAX}
        },
        "ptz_speed_presets": PTZ_SPEED_PRESETS,
        "ptz_speed_table_size": {
            "pan": PTZ_PAN_SPEED_TABLE_SIZE,
            "tilt": PTZ_TILT_SPEED_TABLE_SIZE
        }
    }