#!/usr/bin/env python3
"""
紧急关闭脚本 - 确保所有设备安全关闭
"""

import asyncio
import aiohttp
import subprocess

# 使用新的硬件控制API地址
HARDWARE_API_URL = "http://localhost:7080"
IO_CHANNEL = "d4ad2070b92f0000"
PTZ_CHANNEL = "0000000000000000"


async def emergency_shutdown():
    """紧急关闭所有设备 - 增强版，确保云台归零"""
    print("执行紧急关闭...")
    
    async with aiohttp.ClientSession() as session:
        # 1. 云台归零（带验证）
        print("\n1. 云台归零（带验证）")
        
        # 设置速度
        for direction in ["PAN", "TILT"]:
            data = {
                "address": 30,
                "direction": direction,
                "speed": 3,
                "channelName": PTZ_CHANNEL
            }
            await session.post(f"{HARDWARE_API_URL}/api/ptz/speed", json=data)
        
        # 尝试归零并验证
        home_success = False
        for attempt in range(2):  # 紧急情况下只尝试2次
            # 发送归零命令
            for direction in ["PAN", "TILT"]:
                data = {
                    "address": 30,
                    "direction": direction,
                    "angle": 0,
                    "channelName": PTZ_CHANNEL
                }
                await session.post(f"{HARDWARE_API_URL}/api/ptz/angle", json=data)
            
            print(f"  归零尝试 {attempt + 1}/2, 等待完成...")
            await asyncio.sleep(8)
            
            # 验证位置
            pan_ok = False
            tilt_ok = False
            
            for direction in ["PAN", "TILT"]:
                params = {
                    "slaveAddress": 30,
                    "direction": direction,
                    "channelName": PTZ_CHANNEL
                }
                try:
                    async with session.get(
                        f"{API_BASE_URL}/test/ptz/getAngleValue",
                        params=params,
                        timeout=3
                    ) as resp:
                        angle = float(await resp.text())
                        if abs(angle) <= 1.0:
                            if direction == "PAN":
                                pan_ok = True
                            else:
                                tilt_ok = True
                except:
                    pass
            
            if pan_ok and tilt_ok:
                print("  ✓ 云台已成功归零")
                home_success = True
                break
        
        # 2. 关闭所有IO口（根据云台归零结果决定）
        if home_success:
            print("\n2. 云台已归零，关闭所有IO口")
        else:
            print("\n2. 警告: 云台未归零，但仍将关闭所有IO口（紧急情况）")
        for do_num in range(1, 9):
            data = {
                "channelName": IO_CHANNEL,
                "f": False,
                "doNumber": do_num,
                "address": 1
            }
            
            try:
                await session.post(
                    f"{API_BASE_URL}/test/relay/doControl",
                    json=data,
                    timeout=5
                )
                print(f"  DO{do_num}: 已关闭")
            except:
                pass
                
        # 3. 最终检查
        print("\n3. 最终状态检查")
        await asyncio.sleep(3)
        
        # 检查激光雷达
        result = subprocess.run(
            ['ping', '-c', '1', '-W', '1', '*************'],
            capture_output=True
        )
        if result.returncode == 0:
            print("  ✗ 警告：激光雷达仍在线")
        else:
            print("  ✓ 激光雷达已离线")
            
        # 检查IO口状态
        print("\n4. IO口最终状态")
        all_closed = True
        for do_num in range(1, 9):
            params = {
                "address": 1,
                "doNumber": do_num,
                "channelName": IO_CHANNEL
            }
            
            try:
                async with session.get(
                    f"{API_BASE_URL}/test/relay/doCacheSearch",
                    params=params,
                    timeout=5
                ) as resp:
                    status = await resp.text()
                    is_open = status.lower() == "true"
                    if is_open:
                        print(f"  DO{do_num}: ✗ 仍然开启")
                        all_closed = False
                    else:
                        print(f"  DO{do_num}: ✓ 已关闭")
            except:
                print(f"  DO{do_num}: 无法查询")
                
        if all_closed:
            print("\n✓ 所有设备已安全关闭")
        else:
            print("\n✗ 部分设备可能未正确关闭")
            
        # 查询云台最终角度
        print("\n5. 云台最终位置")
        for direction in ["PAN", "TILT"]:
            params = {
                "slaveAddress": 30,
                "direction": direction,
                "channelName": PTZ_CHANNEL
            }
            
            try:
                async with session.get(
                    f"{API_BASE_URL}/test/ptz/getAngleValue",
                    params=params,
                    timeout=5
                ) as resp:
                    angle = await resp.text()
                    print(f"  {direction}: {angle}°")
            except:
                print(f"  {direction}: 无法查询")


async def main():
    await emergency_shutdown()
    print("\n紧急关闭完成")


if __name__ == "__main__":
    asyncio.run(main())