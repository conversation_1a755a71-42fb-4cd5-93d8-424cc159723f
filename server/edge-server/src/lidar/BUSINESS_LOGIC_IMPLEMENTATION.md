# 激光雷达业务逻辑实现文档

## 更新日期：2025-07-07

## 一、硬件配置（已验证）

### 1.1 正确的硬件连接

```
激光雷达：DO1 (通道: d4ad2070b92f0000)
云台：DO2 (通道: d4ad2070b92f0000) 
云台控制通道：0000000000000000
激光雷达IP：*************
```

### 1.2 重要修正

- ❌ 错误：云台在DO3
- ✅ 正确：云台在DO2

## 二、业务逻辑实现

### 2.1 定点扫描 (Point Scan)

**功能描述**：云台移动到指定角度，进行定点数据采集（10Hz）

**实现流程**：
1. **设备上电**
   - 激光雷达上电 (DO1)
   - 云台上电 (DO2)
   - 等待10秒设备初始化

2. **设备状态检查**
   - Ping激光雷达 (*************)
   - 查询云台状态

3. **云台定位**
   - 设置云台速度 (默认3)
   - 移动到指定位置 (水平角、俯仰角)
   - 等待云台到位
   - 验证实际位置（容差±3°）

4. **激光雷达数据采集**
   - 使用SDK连接激光雷达
   - 10Hz实时流式采集
   - 数据保存为PCD格式
   - 记录元数据

5. **云台归零**
   - 移动到(0°, 0°)

6. **设备断电**
   - 激光雷达断电
   - 云台断电
   - 确认设备离线

### 2.2 地形扫描 (Terrain Scan)

**功能描述**：多层俯仰角度的360度全景扫描

**实现流程**：
1. **设备上电** (同定点扫描)

2. **扫描路径计算**
   - 水平范围：0-360°，步进30°
   - 俯仰范围：0-60°，步进10°
   - 蛇形扫描路径优化

3. **逐位置扫描**
   ```
   第0层(0°俯仰): 0°→30°→60°→...→330°
   第1层(10°俯仰): 330°→300°→...→0° (反向)
   第2层(20°俯仰): 0°→30°→...→330°
   ...
   ```

4. **每个位置**
   - 云台移动到位置
   - 等待稳定
   - 采集数据（可配置时长）
   - 保存到位置子目录

5. **设备归零和断电**

## 三、数据采集模式

### 3.1 实时流式模式 (REALTIME)
- 用于定点扫描
- 10Hz连续采集
- 每帧独立保存

### 3.2 长时间累积模式 (ACCUMULATE)
- 用于地形扫描
- 累积多帧数据
- 生成高密度点云

## 四、数据存储结构

```
/data/lidar/scans/
├── point_scan_[timestamp]/
│   ├── metadata.json
│   ├── frame_000001.pcd
│   ├── frame_000002.pcd
│   └── ...
└── terrain_scan_[timestamp]/
    ├── metadata.json
    ├── pos_000_pan0_tilt0/
    │   └── accumulated.pcd
    ├── pos_001_pan30_tilt0/
    └── ...
```

## 五、已验证功能

### 5.1 硬件控制 ✅
- IO控制正常（DO1激光雷达，DO2云台）
- 云台移动命令正常
- 设备上下电正常

### 5.2 数据采集 ✅
- SDK连接成功
- 实时数据流正常（约46,080点/帧）
- 数据保存正常

### 5.3 业务流程 ✅
- 定点扫描完整流程
- 地形扫描逻辑实现
- 安全机制（自动断电）

## 六、注意事项

1. **云台角度问题**
   - 初始状态可能显示-100°
   - 需要发送归零命令初始化
   - 实际移动功能正常

2. **设备保护**
   - 激光雷达不能长时间工作
   - 任务完成必须断电
   - 异常情况执行安全关闭

3. **网络要求**
   - 边缘服务器需要访问192.168.1.x网段
   - API服务器在192.168.30.253:5000

## 七、测试命令

### 快速验证
```bash
python3 complete_business_test.py
```

### 完整测试
```bash
python3 business_logic_test.py
```

### 真实数据采集
```bash
python3 real_scan_test.py
```

## 八、PTZ归零安全机制

### 8.1 增强型归零功能
- **验证机制**：发送归零命令后查询实际位置
- **多次重试**：最多尝试3次，直到误差在1°以内
- **条件断电**：只有归零成功才断电，失败时保持电源

### 8.2 使用方式
```python
# 带验证的归零
success = await ptz_controller.move_home(verify=True, max_attempts=3)
if success:
    # 可以安全断电
    await hardware_manager.power_off_all_devices()
else:
    # 保持电源，记录警告
    logger.error("云台未能归零")
```

### 8.3 安全保障
1. **扫描服务**：任务结束必须验证归零
2. **紧急关闭**：也会尝试归零但减少等待时间
3. **元数据记录**：归零失败会记录到警告信息

## 九、后续优化

1. 云台角度读取优化
2. 数据压缩和传输
3. MinIO集成
4. GRPC接口开发
5. 串口直连模式