"""
激光雷达任务总线模块
实现TaskBusModule接口，集成到任务总线
"""

import os
import time
import logging
import asyncio
from typing import Dict, Any, List
from datetime import datetime
from pathlib import Path

from src.modules.task_bus.src.base_module import TaskBusModule, TaskResult, ModuleStatus
from src.modules.task_bus.src.message_broker import Topics

# 导入现有的激光雷达功能
from ..scan_service_adapter import ScanService, DataAcquisitionMode
from .lidar_business_service import LidarBusinessService, DeviceStatus

class LidarModule(TaskBusModule):
    """激光雷达模块"""
    
    def __init__(self, config: Dict = None):
        super().__init__("lidar")
        self.config = config or {}
        
        # 激光雷达配置
        self.lidar_ip = self.config.get('lidar_ip', '*************')
        self.lidar_port = self.config.get('lidar_port', 2368)
        self.data_dir = self.config.get('data_dir', '/data/lidar')
        
        # 内部组件
        self.lidar_sdk = None
        self.scan_service = None
        self.business_service = None
        
        # 当前扫描任务
        self.current_scan_task = None
        
    def initialize(self) -> bool:
        """初始化模块"""
        try:
            # 初始化扫描服务
            self.scan_service = ScanService()
            
            # 初始化业务服务（注意：此时self.bus可能还未设置）
            self.business_service = LidarBusinessService(task_bus=None, config=self.config)
            
            # 初始化服务（异步方法）
            await self.scan_service.initialize()
            await self.business_service.initialize()
            
            # 创建数据目录
            os.makedirs(self.data_dir, exist_ok=True)
            
            self.logger.info("Lidar module initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize lidar module: {e}")
            self.status = ModuleStatus.ERROR
            return False
            
    def get_capabilities(self) -> Dict[str, Any]:
        """获取模块能力"""
        return {
            "actions": [
                "start_point_scan",
                "start_terrain_scan",
                "stop_scan",
                "get_scan_status",
                "test_connection",
                "calibrate",
                "automated_point_scan",
                "automated_terrain_scan",
                "get_business_status"
            ],
            "scan_modes": {
                "point_scan": {
                    "description": "定点扫描，10Hz实时数据流",
                    "data_rate": "10Hz",
                    "output_format": "pcd"
                },
                "terrain_scan": {
                    "description": "地形扫描，累积模式生成高密度点云",
                    "scan_pattern": "spiral",
                    "output_format": "pcd"
                }
            },
            "features": {
                "real_time_streaming": True,
                "data_accumulation": True,
                "auto_upload": True,
                "metadata_tracking": True
            }
        }
        
    def handle_task(self, task: Dict[str, Any]) -> TaskResult:
        """处理任务"""
        action = task.get('action')
        params = task.get('params', {})
        task_id = task.get('task_id')
        
        try:
            self.status = ModuleStatus.BUSY
            self.current_task = task_id
            
            if action == "start_point_scan":
                return self._handle_point_scan(task_id, params)
            elif action == "start_terrain_scan":
                return self._handle_terrain_scan(task_id, params)
            elif action == "stop_scan":
                return self._handle_stop_scan()
            elif action == "get_scan_status":
                return self._handle_get_status()
            elif action == "test_connection":
                return self._handle_test_connection()
            elif action == "calibrate":
                return self._handle_calibrate()
            elif action == "automated_point_scan":
                return self._handle_automated_point_scan(task_id, params)
            elif action == "automated_terrain_scan":
                return self._handle_automated_terrain_scan(task_id, params)
            elif action == "get_business_status":
                return self._handle_get_business_status()
            elif action == "emergency_stop":
                return self._handle_emergency_stop()
            else:
                return TaskResult(False, error=f"Unknown action: {action}")
                
        except Exception as e:
            self.logger.error(f"Task execution failed: {e}")
            return TaskResult(False, error=str(e))
        finally:
            self.status = ModuleStatus.IDLE
            self.current_task = None
            
    def _handle_point_scan(self, task_id: str, params: Dict) -> TaskResult:
        """处理定点扫描"""
        duration = params.get('duration', 60)  # 默认60秒
        position = params.get('position', {})
        horizontal = position.get('horizontal', 0)
        vertical = position.get('vertical', 0)
        data_mode = params.get('data_mode', 'continuous')
        
        try:
            # 记录扫描任务
            self.current_scan_task = {
                'task_id': task_id,
                'type': 'point_scan',
                'start_time': datetime.now()
            }
            
            # 开始扫描
            self.logger.info(f"Starting point scan at H:{horizontal}, V:{vertical} for {duration}s")
            
            # 使用异步接口执行定点扫描
            result = await self.scan_service.execute_point_scan(
                task_id=task_id,
                pan_angle=horizontal,
                tilt_angle=vertical,
                duration=duration,
                data_mode=data_mode
            )
            
            # 获取输出目录和文件信息
            output_dir = result.get('output_dir', '')
            scan_files = result.get('scan_files', [])
            frame_count = result.get('frame_count', 0)
            
            # 生成元数据
            metadata = {
                'task_id': task_id,
                'scan_type': 'point_scan',
                'position': position,
                'duration': duration,
                'frame_count': frame_count,
                'start_time': self.current_scan_task['start_time'].isoformat(),
                'end_time': datetime.now().isoformat(),
                'scan_result': result
            }
            
            # 如果有输出目录，保存元数据
            if output_dir and os.path.exists(output_dir):
                metadata_file = os.path.join(output_dir, 'metadata.json')
                with open(metadata_file, 'w') as f:
                    import json
                    json.dump(metadata, f, indent=2)
                
                # 触发数据上传
                if self.bus:
                    self.bus.submit_task(
                        'upload',
                        'minio',
                        'upload_directory',
                        {
                            'dir_path': output_dir,
                            'data_type': 'lidar-point-scan',
                            'source_task_id': task_id,
                            'pattern': '*.pcd',
                            'metadata': metadata
                        }
                    )
                
            self.current_scan_task = None
            
            return TaskResult(True, data={
                'output_dir': output_dir,
                'frame_count': frame_count,
                'files': scan_files[:10],  # 返回前10个文件作为示例
                'scan_result': result
            })
            
        except Exception as e:
            self.current_scan_task = None
            return TaskResult(False, error=str(e))
            
    def _handle_terrain_scan(self, task_id: str, params: Dict) -> TaskResult:
        """处理地形扫描"""
        scan_area = params.get('scan_area', {})
        h_start = scan_area.get('h_start', -45)
        h_end = scan_area.get('h_end', 45)
        v_start = scan_area.get('v_start', -30)
        v_end = scan_area.get('v_end', 30)
        h_step = params.get('h_step', 1)  # 水平步进角度
        v_step = params.get('v_step', 1)  # 垂直步进角度
        scan_duration_per_position = params.get('scan_duration_per_position', 30)  # 每个位置扫描时长
        data_mode = params.get('data_mode', 'accumulate')
        
        try:
            # 记录扫描任务
            self.current_scan_task = {
                'task_id': task_id,
                'type': 'terrain_scan',
                'start_time': datetime.now()
            }
            
            # 使用业务逻辑执行地形扫描
            self.logger.info(f"Starting terrain scan: H[{h_start},{h_end}] V[{v_start},{v_end}]")
            
            # 使用异步接口执行地形扫描
            result = await
                self.scan_service.execute_terrain_scan(
                    task_id=task_id,
                    pan_range=(h_start, h_end),
                    tilt_range=(v_start, v_end),
                    pan_step=h_step,
                    tilt_step=v_step,
                    scan_duration_per_position=scan_duration_per_position,
                    data_mode=data_mode
                )
            )
            
            # 获取输出信息
            output_file = result.get('output_file', '')
            total_points = result.get('total_points', 0)
            scan_positions = result.get('scan_positions', 0)
            output_dir = result.get('output_dir', '')
            
            # 生成元数据
            metadata = {
                'task_id': task_id,
                'scan_type': 'terrain_scan',
                'scan_area': scan_area,
                'h_step': h_step,
                'v_step': v_step,
                'scan_duration_per_position': scan_duration_per_position,
                'total_positions': scan_positions,
                'total_points': total_points,
                'start_time': self.current_scan_task['start_time'].isoformat(),
                'end_time': datetime.now().isoformat(),
                'scan_result': result
            }
            
            # 如果有输出目录，保存元数据
            if output_dir and os.path.exists(output_dir):
                metadata_file = os.path.join(output_dir, 'metadata.json')
                with open(metadata_file, 'w') as f:
                    import json
                    json.dump(metadata, f, indent=2)
                
                # 触发数据上传
                if self.bus:
                    self.bus.submit_task(
                        'upload',
                        'minio',
                        'upload_directory',
                        {
                            'dir_path': output_dir,
                            'data_type': 'lidar-terrain-scan',
                            'source_task_id': task_id,
                            'metadata': metadata
                        }
                    )
                
            self.current_scan_task = None
            
            # 返回PTZ到安全位置
            if self.bus:
                self.bus.submit_task(
                    'control',
                    'hardware',
                    'ptz_goto',
                    {'horizontal': 0, 'vertical': 0}
                )
                
            return TaskResult(True, data={
                'output_file': output_file,
                'total_points': total_points,
                'scan_positions': scan_positions,
                'scan_result': result
            })
            
        except Exception as e:
            self.current_scan_task = None
            return TaskResult(False, error=str(e))
            
    def _handle_stop_scan(self) -> TaskResult:
        """停止扫描"""
        try:
            if self.scan_service:
                # 使用异步接口停止当前扫描
                await self.scan_service.stop_current_scan()
                
            if self.current_scan_task:
                self.logger.info(f"Stopped scan task: {self.current_scan_task['task_id']}")
                self.current_scan_task = None
                
            return TaskResult(True, data={"status": "scan_stopped"})
            
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    def _handle_get_status(self) -> TaskResult:
        """获取扫描状态"""
        try:
            # 获取扫描服务状态
            scan_status = self.scan_service.get_scan_status() if self.scan_service else {}
            
            status = {
                'module_status': self.status.value,
                'is_scanning': self.current_scan_task is not None,
                'scan_service_status': scan_status
            }
            
            if self.current_scan_task:
                status['current_scan'] = {
                    'task_id': self.current_scan_task['task_id'],
                    'type': self.current_scan_task['type'],
                    'start_time': self.current_scan_task['start_time'].isoformat()
                }
                
            return TaskResult(True, data=status)
            
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    def _handle_test_connection(self) -> TaskResult:
        """测试连接"""
        try:
            # 获取扫描服务状态以测试连接
            if self.scan_service:
                status = self.scan_service.get_scan_status()
                return TaskResult(True, data={
                    "connected": True,
                    "ip": self.lidar_ip,
                    "port": self.lidar_port,
                    "service_status": status
                })
            else:
                return TaskResult(False, error="Scan service not initialized")
                
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    def _handle_calibrate(self) -> TaskResult:
        """执行标定"""
        try:
            # 这里可以添加激光雷达标定逻辑
            self.logger.info("Performing lidar calibration...")
            
            # 模拟标定过程
            time.sleep(5)
            
            return TaskResult(True, data={
                "calibration": "completed",
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    def set_bus(self, bus):
        """设置任务总线引用"""
        super().set_bus(bus)
        # 更新业务服务的任务总线引用
        if self.business_service:
            self.business_service.task_bus = bus
            
    def _handle_automated_point_scan(self, task_id: str, params: Dict) -> TaskResult:
        """处理自动化定点扫描"""
        try:
            # 使用业务服务执行自动化流程
            result = await
                self.business_service.execute_automated_scan('point_scan', params)
            )
            
            if result['success']:
                return TaskResult(True, data=result)
            else:
                return TaskResult(False, error=result.get('error', 'Automated scan failed'))
                
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    def _handle_automated_terrain_scan(self, task_id: str, params: Dict) -> TaskResult:
        """处理自动化地形扫描"""
        try:
            # 使用业务服务执行自动化流程
            result = await
                self.business_service.execute_automated_scan('terrain_scan', params)
            )
            
            if result['success']:
                return TaskResult(True, data=result)
            else:
                return TaskResult(False, error=result.get('error', 'Automated scan failed'))
                
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    def _handle_get_business_status(self) -> TaskResult:
        """获取业务服务状态"""
        try:
            status = self.business_service.get_status() if self.business_service else {}
            return TaskResult(True, data=status)
        except Exception as e:
            return TaskResult(False, error=str(e))
    
    def _handle_emergency_stop(self) -> TaskResult:
        """处理紧急停止请求
        
        执行紧急停止流程：
        1. 立即停止所有扫描任务
        2. 将云台归零
        3. 关闭所有设备电源
        """
        try:
            self.logger.warning("执行紧急停止...")
            
            results = {
                "scan_stopped": False,
                "ptz_homed": False,
                "devices_powered_off": False,
                "timestamp": datetime.now().isoformat()
            }
            
            # 1. 停止当前扫描
            if self.current_scan_task:
                stop_result = self._handle_stop_scan()
                results["scan_stopped"] = stop_result.success
                if not stop_result.success:
                    self.logger.error(f"停止扫描失败: {stop_result.error}")
            else:
                results["scan_stopped"] = True
                
            # 2. 执行紧急关闭（包含PTZ归零和设备断电）
            if self.business_service:
                try:
                    # 使用业务服务的紧急关闭功能
                    asyncio.run(self.business_service.emergency_shutdown())
                    results["ptz_homed"] = True
                    results["devices_powered_off"] = True
                    self.logger.info("紧急关闭完成")
                except Exception as e:
                    self.logger.error(f"紧急关闭失败: {e}")
                    # 尝试直接执行emergency_shutdown.py脚本
                    try:
                        import subprocess
                        script_path = os.path.join(os.path.dirname(__file__), '..', 'emergency_shutdown.py')
                        subprocess.run(['python3', script_path], check=True)
                        results["devices_powered_off"] = True
                        self.logger.info("使用备用脚本完成紧急关闭")
                    except Exception as e2:
                        self.logger.error(f"备用紧急关闭也失败: {e2}")
                        
            return TaskResult(
                success=True,
                data=results
            )
            
        except Exception as e:
            self.logger.error(f"紧急停止失败: {e}")
            return TaskResult(False, error=str(e))
            
    def shutdown(self):
        """关闭模块"""
        # 停止任何正在进行的扫描
        if self.current_scan_task:
            self._handle_stop_scan()
            
        # 关闭业务服务
        if self.business_service:
            await self.business_service.shutdown()
            
        # 关闭扫描服务
        if self.scan_service:
            await self.scan_service.shutdown()
            
        # 请求关闭激光雷达电源
        if self.bus:
            self.bus.submit_task(
                'control',
                'hardware',
                'power_off',
                {'device': 'lidar'}
            )
            
        self.logger.info("Lidar module shutdown")