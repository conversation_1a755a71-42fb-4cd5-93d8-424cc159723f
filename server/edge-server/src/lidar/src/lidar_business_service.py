"""
LiDAR业务服务层
提供高层自动化流程接口，处理电源管理、设备检测和扫描流程
"""

import asyncio
import logging
import time
import subprocess
from typing import Dict, Any, Optional, Tuple
from datetime import datetime
from enum import Enum

from src.modules.task_bus.src.base_module import TaskResult
from ..scan_service_adapter import ScanService, DataAcquisitionMode


class DeviceStatus(Enum):
    """设备状态枚举"""
    UNKNOWN = "unknown"
    OFF = "off"
    STARTING = "starting"
    READY = "ready"
    SCANNING = "scanning"
    ERROR = "error"
    SHUTTING_DOWN = "shutting_down"


class LidarBusinessService:
    """LiDAR业务服务 - 提供高层自动化接口"""
    
    def __init__(self, task_bus=None, config: Dict = None):
        """
        初始化业务服务
        
        Args:
            task_bus: 任务总线实例，用于发送硬件控制命令
            config: 配置字典
        """
        self.logger = logging.getLogger(__name__)
        self.task_bus = task_bus
        self.config = config or {}
        
        # 设备配置
        self.lidar_ip = self.config.get('lidar_ip', '*************')
        self.startup_wait_time = self.config.get('startup_wait_time', 30)
        self.ping_retries = self.config.get('ping_retries', 5)
        self.ping_timeout = self.config.get('ping_timeout', 2)
        
        # 内部状态
        self.device_status = DeviceStatus.UNKNOWN
        self.scan_service = None
        self.current_workflow = None
        
    async def initialize(self):
        """初始化业务服务"""
        try:
            # 创建扫描服务
            self.scan_service = ScanService()
            await self.scan_service.initialize()
            
            # 检查设备初始状态
            await self._check_device_status()
            
            self.logger.info("LiDAR business service initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize business service: {e}")
            self.device_status = DeviceStatus.ERROR
            return False
            
    async def execute_automated_scan(self, scan_type: str, params: Dict) -> Dict[str, Any]:
        """
        执行自动化扫描流程
        
        Args:
            scan_type: 扫描类型 ('point_scan' 或 'terrain_scan')
            params: 扫描参数
            
        Returns:
            包含扫描结果的字典
        """
        workflow_id = f"workflow_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.current_workflow = workflow_id
        
        try:
            # 0. 设置业务活跃状态（防止保护机制断电）
            self.logger.info(f"[{workflow_id}] Setting business active")
            await self._send_task('hardware', 'set_business_active', {
                'active': True, 
                'business_type': f'lidar_{scan_type}'
            })
            
            # 1. 启动设备
            self.logger.info(f"[{workflow_id}] Starting automated workflow")
            startup_result = await self._startup_devices()
            if not startup_result['success']:
                # 启动失败时也要清除业务活跃状态
                await self._send_task('hardware', 'set_business_active', {
                    'active': False, 
                    'business_type': f'lidar_{scan_type}'
                })
                return {
                    'success': False,
                    'workflow_id': workflow_id,
                    'error': f"Device startup failed: {startup_result.get('error')}"
                }
                
            # 2. 验证设备就绪
            verify_result = await self._verify_devices_ready()
            if not verify_result['success']:
                # 如果验证失败，关闭设备
                await self._shutdown_devices()
                return {
                    'success': False,
                    'workflow_id': workflow_id,
                    'error': f"Device verification failed: {verify_result.get('error')}"
                }
                
            # 3. 执行扫描
            self.logger.info(f"[{workflow_id}] Executing {scan_type}")
            scan_result = await self._execute_scan(scan_type, params)
            
            # 4. 关闭设备（无论扫描是否成功）
            shutdown_result = await self._shutdown_devices()
            
            # 5. 验证设备已关闭
            await self._verify_devices_shutdown()
            
            # 6. 清除业务活跃状态
            await self._send_task('hardware', 'set_business_active', {
                'active': False, 
                'business_type': f'lidar_{scan_type}'
            })
            
            return {
                'success': scan_result['success'],
                'workflow_id': workflow_id,
                'scan_result': scan_result,
                'startup_duration': startup_result.get('duration'),
                'shutdown_duration': shutdown_result.get('duration')
            }
            
        except Exception as e:
            self.logger.error(f"[{workflow_id}] Workflow failed: {e}")
            # 确保在异常情况下也关闭设备
            await self._emergency_shutdown()
            # 清除业务活跃状态
            await self._send_task('hardware', 'set_business_active', {
                'active': False, 
                'business_type': f'lidar_{scan_type}'
            })
            return {
                'success': False,
                'workflow_id': workflow_id,
                'error': str(e)
            }
        finally:
            self.current_workflow = None
            
    async def _startup_devices(self) -> Dict[str, Any]:
        """启动设备流程"""
        start_time = time.time()
        
        try:
            self.device_status = DeviceStatus.STARTING
            
            # 1. 打开LiDAR电源 (DO1)
            self.logger.info("Powering on LiDAR (DO1)")
            lidar_result = await self._send_task('hardware', 'power_on', {'device': 'lidar'})
            if not lidar_result.success:
                return {'success': False, 'error': f"Failed to power on LiDAR: {lidar_result.error}"}
                
            # 2. 打开PTZ电源 (DO2)
            self.logger.info("Powering on PTZ (DO2)")
            ptz_result = await self._send_task('hardware', 'power_on', {'device': 'ptz'})
            if not ptz_result.success:
                # 如果PTZ启动失败，关闭LiDAR
                await self._send_task('hardware', 'power_off', {'device': 'lidar'})
                return {'success': False, 'error': f"Failed to power on PTZ: {ptz_result.error}"}
                
            # 3. 等待设备启动
            self.logger.info(f"Waiting {self.startup_wait_time}s for devices to start")
            await asyncio.sleep(self.startup_wait_time)
            
            duration = time.time() - start_time
            self.logger.info(f"Device startup completed in {duration:.1f}s")
            
            return {'success': True, 'duration': duration}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
            
    async def _verify_devices_ready(self) -> Dict[str, Any]:
        """验证设备就绪"""
        try:
            # 1. Ping测试LiDAR
            self.logger.info(f"Testing LiDAR connectivity at {self.lidar_ip}")
            ping_result = await self._ping_device(self.lidar_ip)
            if not ping_result['success']:
                return {'success': False, 'error': f"LiDAR ping failed: {ping_result.get('error')}"}
                
            # 2. PTZ旋转测试
            self.logger.info("Testing PTZ movement")
            ptz_test_result = await self._test_ptz_movement()
            if not ptz_test_result['success']:
                return {'success': False, 'error': f"PTZ test failed: {ptz_test_result.get('error')}"}
                
            self.device_status = DeviceStatus.READY
            self.logger.info("All devices verified ready")
            
            return {'success': True}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
            
    async def _ping_device(self, ip: str) -> Dict[str, Any]:
        """Ping设备测试连接"""
        for attempt in range(self.ping_retries):
            try:
                # 使用subprocess执行ping命令
                result = subprocess.run(
                    ['ping', '-c', '1', '-W', str(self.ping_timeout), ip],
                    capture_output=True,
                    text=True
                )
                
                if result.returncode == 0:
                    self.logger.info(f"Ping {ip} successful on attempt {attempt + 1}")
                    return {'success': True, 'attempts': attempt + 1}
                    
                self.logger.warning(f"Ping {ip} failed on attempt {attempt + 1}")
                
            except Exception as e:
                self.logger.error(f"Ping error: {e}")
                
            if attempt < self.ping_retries - 1:
                await asyncio.sleep(2)
                
        return {'success': False, 'error': f"Failed to ping {ip} after {self.ping_retries} attempts"}
        
    async def _test_ptz_movement(self) -> Dict[str, Any]:
        """测试云台运动"""
        try:
            # 1. 获取当前位置
            pos_result = await self._send_task('hardware', 'ptz_get_position', {})
            if not pos_result.success:
                return {'success': False, 'error': "Failed to get PTZ position"}
                
            original_pos = pos_result.data
            
            # 2. 移动到测试位置
            test_positions = [
                {'horizontal': 10, 'vertical': 5},
                {'horizontal': -10, 'vertical': -5},
                {'horizontal': 0, 'vertical': 0}
            ]
            
            for pos in test_positions:
                move_result = await self._send_task('hardware', 'ptz_goto', pos)
                if not move_result.success:
                    return {'success': False, 'error': f"PTZ movement failed: {move_result.error}"}
                await asyncio.sleep(2)  # 等待移动完成
                
            self.logger.info("PTZ movement test successful")
            return {'success': True}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
            
    async def _execute_scan(self, scan_type: str, params: Dict) -> Dict[str, Any]:
        """执行扫描"""
        try:
            self.device_status = DeviceStatus.SCANNING
            
            if scan_type == 'point_scan':
                result = await self.scan_service.execute_point_scan(
                    task_id=params.get('task_id', 'manual_scan'),
                    pan_angle=params.get('position', {}).get('horizontal', 0),
                    tilt_angle=params.get('position', {}).get('vertical', 0),
                    duration=params.get('duration', 60),
                    data_mode=params.get('data_mode', 'continuous')
                )
            elif scan_type == 'terrain_scan':
                scan_area = params.get('scan_area', {})
                result = await self.scan_service.execute_terrain_scan(
                    task_id=params.get('task_id', 'manual_scan'),
                    pan_range=(scan_area.get('h_start', -45), scan_area.get('h_end', 45)),
                    tilt_range=(scan_area.get('v_start', -30), scan_area.get('v_end', 30)),
                    pan_step=params.get('h_step', 1),
                    tilt_step=params.get('v_step', 1),
                    scan_duration_per_position=params.get('scan_duration_per_position', 30),
                    data_mode=params.get('data_mode', 'accumulate')
                )
            else:
                return {'success': False, 'error': f"Unknown scan type: {scan_type}"}
                
            self.device_status = DeviceStatus.READY
            return {'success': True, 'data': result}
            
        except Exception as e:
            self.device_status = DeviceStatus.ERROR
            return {'success': False, 'error': str(e)}
            
    async def _shutdown_devices(self) -> Dict[str, Any]:
        """关闭设备流程"""
        start_time = time.time()
        
        try:
            self.device_status = DeviceStatus.SHUTTING_DOWN
            
            # 1. 停止任何正在进行的扫描
            if self.scan_service:
                await self.scan_service.stop_current_scan()
                
            # 2. PTZ归零
            self.logger.info("Returning PTZ to home position")
            await self._send_task('hardware', 'ptz_home', {})
            await asyncio.sleep(3)  # 等待归零完成
            
            # 3. 关闭PTZ电源 (DO2)
            self.logger.info("Powering off PTZ (DO2)")
            ptz_result = await self._send_task('hardware', 'power_off', {'device': 'ptz'})
            
            # 4. 关闭LiDAR电源 (DO1)
            self.logger.info("Powering off LiDAR (DO1)")
            lidar_result = await self._send_task('hardware', 'power_off', {'device': 'lidar'})
            
            duration = time.time() - start_time
            self.logger.info(f"Device shutdown completed in {duration:.1f}s")
            
            self.device_status = DeviceStatus.OFF
            
            return {
                'success': lidar_result.success and ptz_result.success,
                'duration': duration
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
            
    async def _verify_devices_shutdown(self) -> Dict[str, Any]:
        """验证设备已关闭"""
        try:
            # 获取电源状态
            status_result = await self._send_task('hardware', 'get_power_status', {})
            if status_result.success:
                power_status = status_result.data
                lidar_off = power_status.get('lidar') == 'off'
                ptz_off = power_status.get('ptz') == 'off'
                
                if lidar_off and ptz_off:
                    self.logger.info("Verified: All devices are powered off")
                    return {'success': True}
                else:
                    self.logger.warning(f"Some devices still on: LiDAR={power_status.get('lidar')}, PTZ={power_status.get('ptz')}")
                    return {'success': False, 'error': 'Some devices still powered on'}
            else:
                return {'success': False, 'error': 'Failed to get power status'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
            
    async def _emergency_shutdown(self):
        """紧急关闭流程"""
        self.logger.warning("Executing emergency shutdown")
        try:
            # 发送紧急关闭命令
            await self._send_task('hardware', 'emergency_shutdown', {})
        except Exception as e:
            self.logger.error(f"Emergency shutdown failed: {e}")
            
    async def _check_device_status(self):
        """检查设备当前状态"""
        try:
            # 获取电源状态
            status_result = await self._send_task('hardware', 'get_power_status', {})
            if status_result.success:
                power_status = status_result.data
                lidar_on = power_status.get('lidar') == 'on'
                ptz_on = power_status.get('ptz') == 'on'
                
                if lidar_on and ptz_on:
                    # 如果设备已开启，进一步检查连接性
                    ping_result = await self._ping_device(self.lidar_ip)
                    if ping_result['success']:
                        self.device_status = DeviceStatus.READY
                    else:
                        self.device_status = DeviceStatus.ERROR
                elif not lidar_on and not ptz_on:
                    self.device_status = DeviceStatus.OFF
                else:
                    self.device_status = DeviceStatus.UNKNOWN
            else:
                self.device_status = DeviceStatus.UNKNOWN
                
        except Exception as e:
            self.logger.error(f"Failed to check device status: {e}")
            self.device_status = DeviceStatus.UNKNOWN
            
    async def _send_task(self, module: str, action: str, params: Dict) -> TaskResult:
        """发送任务到任务总线"""
        if not self.task_bus:
            # 如果没有任务总线，返回错误
            return TaskResult(False, error="Task bus not available")
            
        try:
            # 提交任务
            task_id = f"{module}_{action}_{int(time.time() * 1000)}"
            result = self.task_bus.submit_task(
                task_id,
                module,
                action,
                params
            )
            
            # 如果结果是Future，等待它完成
            if hasattr(result, '__await__'):
                result = await result
                
            return result
            
        except asyncio.TimeoutError:
            return TaskResult(False, error="Task timeout")
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    def get_status(self) -> Dict[str, Any]:
        """获取业务服务状态"""
        return {
            'device_status': self.device_status.value,
            'current_workflow': self.current_workflow,
            'lidar_ip': self.lidar_ip,
            'scan_service_ready': self.scan_service is not None
        }
        
    async def shutdown(self):
        """关闭业务服务"""
        if self.device_status in [DeviceStatus.READY, DeviceStatus.SCANNING]:
            await self._shutdown_devices()
            
        if self.scan_service:
            await self.scan_service.shutdown()