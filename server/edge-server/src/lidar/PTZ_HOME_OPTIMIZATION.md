# PTZ归零优化说明

## 优化背景
根据项目要求，必须确保云台归零到0度位置后才能关闭电源，以保护设备安全。

## 优化内容

### 1. 增强型PTZ归零功能 (enhanced_ptz_controller.py)
- **新增参数**：
  - `verify`: 是否验证归零位置（默认True）
  - `max_attempts`: 最大尝试次数（默认3次）
  
- **验证逻辑**：
  1. 发送归零命令（0°, 0°）
  2. 等待动作完成
  3. 查询实际位置
  4. 检查位置误差是否在1°以内
  5. 如果误差过大，重新尝试

### 2. 硬件管理器优化 (unified_hardware_manager.py)
- **shutdown() 方法改进**：
  1. 先执行云台归零（带验证）
  2. 只有在归零成功后才断电
  3. 如果归零失败，跳过断电以便手动处理
  
- **power_off_all_devices() 方法增强**：
  - 新增 `verify_ptz_home` 参数
  - 断电前可选择再次验证云台位置

### 3. 扫描服务优化 (scan_service.py)
- **定点扫描和地形扫描**：
  - 使用增强型归零功能
  - 记录归零失败警告到元数据
  - 只在归零成功后才断电
  
- **安全关闭流程**：
  - 带验证的云台归零
  - 条件性断电

### 4. 清理脚本优化
- **final_cleanup.py**：
  - 多次尝试归零直到成功
  - 实时验证云台角度
  - 只在确认归零后才关闭电源
  
- **emergency_shutdown.py**：
  - 紧急情况下也尝试验证归零
  - 快速但安全的关闭流程

## 使用示例

### 基本使用
```python
# 普通归零（不验证）
await ptz_controller.move_home(verify=False)

# 增强归零（带验证）
success = await ptz_controller.move_home(verify=True, max_attempts=3)
if success:
    print("云台已成功归零")
    # 可以安全断电
else:
    print("云台未能归零，请手动处理")
```

### 完整关闭流程
```python
# 使用硬件管理器
await hardware_manager.shutdown()  # 自动处理归零验证和条件断电
```

## 安全保障

1. **多重验证**：
   - 发送命令后验证实际位置
   - 允许多次重试
   - 角度容差设为1°

2. **条件断电**：
   - 只有云台归零成功才断电
   - 失败时保持电源供手动处理

3. **日志记录**：
   - 详细记录每次尝试
   - 记录最终位置
   - 警告信息写入元数据

## 测试验证

运行测试脚本验证功能：
```bash
python /home/<USER>/edge-server/modules/lidar/test_ptz_home_verification.py
```

测试内容：
1. 普通归零 vs 增强归零
2. 多次尝试机制
3. 边界情况（大角度）
4. 完整关闭流程

## 注意事项

1. 归零等待时间可能较长（每次尝试8-10秒）
2. 紧急关闭时仍会尝试归零，但次数减少
3. 如果云台机械故障导致无法归零，系统会保持电源并记录警告

## 相关文件

- `/modules/hardware/enhanced_ptz_controller.py` - PTZ控制器（含增强归零）
- `/modules/hardware/unified_hardware_manager.py` - 统一硬件管理
- `/modules/lidar/scan_service.py` - 扫描服务
- `/modules/lidar/final_cleanup.py` - 最终清理脚本
- `/modules/lidar/emergency_shutdown.py` - 紧急关闭脚本