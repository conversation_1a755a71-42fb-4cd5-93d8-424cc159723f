"""
异步上传管理器
使用异步I/O优化文件上传性能
"""

import os
import asyncio
import aiofiles
import hashlib
import logging
from typing import Dict, Optional, Callable, List, Any
from pathlib import Path
from datetime import datetime
from minio import Minio
from minio.error import S3Error
import aiohttp
import concurrent.futures

from .config import MinIOConfig
from .data_compressor import DataCompressor

class AsyncUploadTask:
    """异步上传任务"""
    
    def __init__(self, file_path: str, object_name: str, metadata: Dict = None):
        self.id = hashlib.md5(f"{file_path}_{object_name}".encode()).hexdigest()
        self.file_path = file_path
        self.object_name = object_name
        self.metadata = metadata or {}
        self.status = "pending"
        self.progress = 0.0
        self.error = None
        self.retry_count = 0
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.compressed_path = None
        self.file_size = 0
        self._future: Optional[asyncio.Future] = None

class AsyncUploadManager:
    """异步上传管理器"""
    
    def __init__(self, config: MinIOConfig):
        self.config = config
        self.logger = logging.getLogger("MinIO.AsyncUploadManager")
        
        # MinIO客户端（同步版本用于兼容）
        self.client = Minio(
            self.config.endpoint,
            access_key=self.config.access_key,
            secret_key=self.config.secret_key,
            secure=self.config.secure
        )
        
        # 压缩器
        self.compressor = DataCompressor('gzip', self.config.compression_level)
        
        # 任务管理
        self.tasks: Dict[str, AsyncUploadTask] = {}
        self._lock = asyncio.Lock()
        
        # 用于CPU密集型操作的线程池
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.config.max_concurrent_uploads
        )
        
        # 并发控制
        self.upload_semaphore = asyncio.Semaphore(self.config.max_concurrent_uploads)
        
        # 运行状态
        self._running = True
        self._background_tasks: List[asyncio.Task] = []
        
    async def initialize(self):
        """初始化管理器"""
        # 确保存储桶存在
        await self._ensure_bucket()
        
        # 创建缓存目录
        os.makedirs(self.config.cache_dir, exist_ok=True)
        
        self.logger.info("Async upload manager initialized")
        
    async def shutdown(self):
        """关闭管理器"""
        self._running = False
        
        # 等待所有后台任务完成
        if self._background_tasks:
            await asyncio.gather(*self._background_tasks, return_exceptions=True)
            
        # 关闭线程池
        self.thread_pool.shutdown(wait=True)
        
        self.logger.info("Async upload manager shutdown")
        
    async def _ensure_bucket(self):
        """确保存储桶存在"""
        try:
            # 使用线程池执行同步操作
            loop = asyncio.get_event_loop()
            exists = await loop.run_in_executor(
                self.thread_pool,
                self.client.bucket_exists,
                self.config.bucket_name
            )
            
            if not exists:
                await loop.run_in_executor(
                    self.thread_pool,
                    self.client.make_bucket,
                    self.config.bucket_name
                )
                self.logger.info(f"Created bucket: {self.config.bucket_name}")
                
        except Exception as e:
            self.logger.error(f"Failed to ensure bucket: {e}")
            
    async def upload_file(self, file_path: str, data_type: str, task_id: str,
                         metadata: Dict = None, callback: Callable = None) -> str:
        """异步上传文件"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
            
        # 获取文件大小
        file_stat = await asyncio.get_event_loop().run_in_executor(
            None, os.stat, file_path
        )
        file_size = file_stat.st_size
        
        # 生成对象名称
        filename = os.path.basename(file_path)
        object_name = self.config.get_object_path(data_type, task_id, filename)
        
        # 添加元数据
        metadata = metadata or {}
        metadata.update({
            'edge_id': self.config.edge_id,
            'data_type': data_type,
            'task_id': task_id,
            'original_filename': filename,
            'upload_time': datetime.now().isoformat()
        })
        
        # 创建上传任务
        upload_task = AsyncUploadTask(file_path, object_name, metadata)
        upload_task.file_size = file_size
        
        async with self._lock:
            self.tasks[upload_task.id] = upload_task
            
        # 创建后台任务执行上传
        task = asyncio.create_task(
            self._upload_worker(upload_task, callback)
        )
        self._background_tasks.append(task)
        
        # 清理已完成的任务
        self._background_tasks = [
            t for t in self._background_tasks if not t.done()
        ]
        
        self.logger.info(f"Upload task created: {upload_task.id}")
        return upload_task.id
        
    async def _upload_worker(self, task: AsyncUploadTask, callback: Callable = None):
        """上传工作协程"""
        async with self.upload_semaphore:
            try:
                task.status = "uploading"
                task.started_at = datetime.now()
                
                # 压缩文件（如果启用）
                if self.config.compression_enabled:
                    compressed_path = await self._compress_file_async(task.file_path)
                    if compressed_path:
                        task.compressed_path = compressed_path
                        upload_path = compressed_path
                    else:
                        upload_path = task.file_path
                else:
                    upload_path = task.file_path
                    
                # 执行上传
                await self._do_upload(upload_path, task)
                
                # 更新状态
                task.status = "completed"
                task.completed_at = datetime.now()
                task.progress = 100.0
                
                # 调用回调
                if callback:
                    await self._call_callback(callback, task)
                    
                # 清理压缩文件
                if task.compressed_path and os.path.exists(task.compressed_path):
                    os.remove(task.compressed_path)
                    
                self.logger.info(f"Upload completed: {task.id}")
                
            except Exception as e:
                task.status = "failed"
                task.error = str(e)
                task.completed_at = datetime.now()
                
                self.logger.error(f"Upload failed: {task.id} - {e}")
                
                # 重试逻辑
                if task.retry_count < self.config.max_retries:
                    task.retry_count += 1
                    task.status = "pending"
                    await asyncio.sleep(2 ** task.retry_count)  # 指数退避
                    await self._upload_worker(task, callback)
                elif callback:
                    await self._call_callback(callback, task)
                    
    async def _compress_file_async(self, file_path: str) -> Optional[str]:
        """异步压缩文件"""
        try:
            # 使用线程池执行CPU密集型压缩操作
            loop = asyncio.get_event_loop()
            compressed_path = await loop.run_in_executor(
                self.thread_pool,
                self.compressor.compress_file,
                file_path,
                self.config.cache_dir
            )
            return compressed_path
        except Exception as e:
            self.logger.error(f"Compression failed: {e}")
            return None
            
    async def _do_upload(self, file_path: str, task: AsyncUploadTask):
        """执行实际的上传操作"""
        # 使用aiofiles读取文件
        async with aiofiles.open(file_path, 'rb') as f:
            data = await f.read()
            
        # 使用线程池执行MinIO上传
        loop = asyncio.get_event_loop()
        
        # 创建进度回调
        def progress_callback(bytes_transferred, total_bytes):
            if total_bytes > 0:
                task.progress = (bytes_transferred / total_bytes) * 100
                
        # 执行上传
        await loop.run_in_executor(
            self.thread_pool,
            self._upload_to_minio,
            data,
            task.object_name,
            task.metadata,
            progress_callback
        )
        
    def _upload_to_minio(self, data: bytes, object_name: str, 
                        metadata: Dict, progress_callback: Callable):
        """同步上传到MinIO（在线程池中执行）"""
        import io
        
        # 创建内存流
        data_stream = io.BytesIO(data)
        data_size = len(data)
        
        # 上传
        self.client.put_object(
            self.config.bucket_name,
            object_name,
            data_stream,
            data_size,
            metadata=metadata,
            progress=progress_callback
        )
        
    async def _call_callback(self, callback: Callable, task: AsyncUploadTask):
        """调用回调函数"""
        try:
            if asyncio.iscoroutinefunction(callback):
                await callback(task)
            else:
                # 在线程池中执行同步回调
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, callback, task)
        except Exception as e:
            self.logger.error(f"Callback error: {e}")
            
    async def upload_directory(self, dir_path: str, data_type: str, 
                             source_task_id: str, pattern: str = '*',
                             recursive: bool = True) -> List[str]:
        """异步上传目录"""
        if not os.path.isdir(dir_path):
            raise NotADirectoryError(f"Directory not found: {dir_path}")
            
        # 查找文件
        files = []
        path = Path(dir_path)
        
        if recursive:
            files = list(path.rglob(pattern))
        else:
            files = list(path.glob(pattern))
            
        # 过滤文件
        files = [f for f in files if f.is_file()]
        
        # 创建上传任务
        upload_task_ids = []
        for file_path in files:
            try:
                task_id = await self.upload_file(
                    str(file_path),
                    data_type,
                    source_task_id
                )
                upload_task_ids.append(task_id)
            except Exception as e:
                self.logger.error(f"Failed to create upload task for {file_path}: {e}")
                
        return upload_task_ids
        
    async def wait_for_task(self, upload_task_id: str, 
                           timeout: Optional[float] = None) -> Dict[str, Any]:
        """等待任务完成"""
        start_time = asyncio.get_event_loop().time()
        
        while True:
            task = self.tasks.get(upload_task_id)
            if not task:
                raise ValueError(f"Task not found: {upload_task_id}")
                
            if task.status in ["completed", "failed"]:
                return {
                    'status': task.status,
                    'object_name': task.object_name,
                    'file_size': task.file_size,
                    'error': task.error,
                    'progress': task.progress
                }
                
            # 检查超时
            if timeout and (asyncio.get_event_loop().time() - start_time) > timeout:
                raise asyncio.TimeoutError(f"Task {upload_task_id} timed out")
                
            # 短暂等待避免忙等
            await asyncio.sleep(0.1)
            
    async def get_task_status(self, upload_task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        task = self.tasks.get(upload_task_id)
        if not task:
            return None
            
        return {
            'id': task.id,
            'status': task.status,
            'progress': task.progress,
            'file_path': task.file_path,
            'object_name': task.object_name,
            'error': task.error,
            'created_at': task.created_at.isoformat(),
            'started_at': task.started_at.isoformat() if task.started_at else None,
            'completed_at': task.completed_at.isoformat() if task.completed_at else None
        }
        
    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        async with self._lock:
            total_tasks = len(self.tasks)
            pending = sum(1 for t in self.tasks.values() if t.status == "pending")
            uploading = sum(1 for t in self.tasks.values() if t.status == "uploading")
            completed = sum(1 for t in self.tasks.values() if t.status == "completed")
            failed = sum(1 for t in self.tasks.values() if t.status == "failed")
            
        return {
            'total_tasks': total_tasks,
            'pending': pending,
            'uploading': uploading,
            'completed': completed,
            'failed': failed,
            'success_rate': (completed / total_tasks * 100) if total_tasks > 0 else 0
        }
        
    async def cleanup_old_tasks(self, hours: int = 24):
        """清理旧任务"""
        cutoff_time = datetime.now().timestamp() - (hours * 3600)
        
        async with self._lock:
            old_tasks = []
            for task_id, task in self.tasks.items():
                if (task.completed_at and 
                    task.completed_at.timestamp() < cutoff_time):
                    old_tasks.append(task_id)
                    
            for task_id in old_tasks:
                del self.tasks[task_id]
                
        self.logger.info(f"Cleaned up {len(old_tasks)} old upload tasks")