"""
简化版任务总线实现
用于边缘服务器模块间通信
"""

import logging
import threading
import time
from typing import Dict, List, Any, Optional
from collections import defaultdict, deque
from enum import Enum


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskBusModule:
    """任务总线模块基类"""
    
    def __init__(self, name: str):
        self.name = name
        self.status = TaskStatus.PENDING
        self.logger = logging.getLogger(f"TaskBusModule.{name}")
    
    async def handle_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """处理任务 - 子类需要实现"""
        raise NotImplementedError("Subclasses must implement handle_task")
    
    def get_capabilities(self) -> Dict[str, Any]:
        """获取模块能力 - 子类可以重写"""
        return {"actions": [], "devices": {}}


class TaskBus:
    """简化版任务总线"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        self.modules: Dict[str, TaskBusModule] = {}
        self.task_queue = deque()
        self.active_tasks: Dict[str, Dict] = {}
        self.task_history: List[Dict] = []
        self.message_subscribers: Dict[str, List] = defaultdict(list)
        self.running = False
        self.logger = logging.getLogger("TaskBus")
        self._lock = threading.RLock()
        self._worker_thread = None
        
    def register_module(self, module: TaskBusModule) -> bool:
        """注册模块到任务总线"""
        try:
            with self._lock:
                if hasattr(module, 'name') and hasattr(module, 'handle_task'):
                    self.modules[module.name] = module
                    self.logger.info(f"Module {module.name} registered successfully")
                    return True
                else:
                    self.logger.error(f"Invalid module: missing name or handle_task method")
                    return False
        except Exception as e:
            self.logger.error(f"Failed to register module: {e}")
            return False
    
    def submit_task(self, task_type: str, target: str, action: str, params: Dict = None) -> str:
        """提交任务到总线"""
        import uuid
        
        task_id = str(uuid.uuid4())
        task = {
            'task_id': task_id,
            'task_type': task_type,
            'target': target,
            'action': action,
            'params': params or {},
            'status': TaskStatus.PENDING.value,
            'created_at': time.time(),
            'retry_count': 0
        }
        
        with self._lock:
            self.task_queue.append(task)
            self.logger.info(f"Task submitted: {task_id} -> {target}.{action}")
        
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务状态"""
        with self._lock:
            # 检查活跃任务
            if task_id in self.active_tasks:
                return self.active_tasks[task_id]
            
            # 检查历史任务
            for task in self.task_history:
                if task['task_id'] == task_id:
                    return task
            
            return None
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self._lock:
            # 从队列中移除
            for i, task in enumerate(self.task_queue):
                if task['task_id'] == task_id:
                    task['status'] = TaskStatus.CANCELLED.value
                    self.task_queue.remove(task)
                    self.task_history.append(task)
                    self.logger.info(f"Task cancelled: {task_id}")
                    return True
            
            # 标记活跃任务为取消
            if task_id in self.active_tasks:
                self.active_tasks[task_id]['status'] = TaskStatus.CANCELLED.value
                self.logger.info(f"Active task marked as cancelled: {task_id}")
                return True
        
        return False
    
    def get_registered_modules(self) -> List[Dict]:
        """获取已注册的模块列表"""
        with self._lock:
            modules = []
            for name, module in self.modules.items():
                modules.append({
                    'name': name,
                    'status': module.status.value if hasattr(module.status, 'value') else str(module.status),
                    'capabilities': module.get_capabilities() if hasattr(module, 'get_capabilities') else {}
                })
            return modules
    
    def get_module_list(self) -> List[Dict]:
        """获取模块列表（兼容性方法）"""
        return self.get_registered_modules()
    
    def get_active_tasks(self) -> List[Dict]:
        """获取活跃任务列表"""
        with self._lock:
            return list(self.active_tasks.values())
    
    def get_status(self) -> Dict:
        """获取总线状态"""
        with self._lock:
            return {
                'running': self.running,
                'modules_count': len(self.modules),
                'queue_size': len(self.task_queue),
                'active_tasks': len(self.active_tasks),
                'total_processed': len(self.task_history)
            }
    
    def publish_message(self, topic: str, data: Any) -> None:
        """发布消息"""
        if topic in self.message_subscribers:
            for callback in self.message_subscribers[topic]:
                try:
                    callback(data)
                except Exception as e:
                    self.logger.error(f"Message callback error: {e}")
    
    def subscribe_message(self, topic: str, callback) -> None:
        """订阅消息"""
        self.message_subscribers[topic].append(callback)
    
    def start(self) -> None:
        """启动任务总线"""
        if self.running:
            self.logger.warning("Task bus is already running")
            return
        
        self.running = True
        self._worker_thread = threading.Thread(target=self._worker_loop, daemon=True)
        self._worker_thread.start()
        self.logger.info("Task bus started")
    
    def stop(self) -> None:
        """停止任务总线"""
        self.running = False
        if self._worker_thread:
            self._worker_thread.join(timeout=5)
        self.logger.info("Task bus stopped")
    
    def _worker_loop(self) -> None:
        """工作线程循环"""
        while self.running:
            try:
                task = None
                with self._lock:
                    if self.task_queue:
                        task = self.task_queue.popleft()
                
                if task:
                    self._process_task(task)
                else:
                    time.sleep(0.1)  # 避免忙等待
                    
            except Exception as e:
                self.logger.error(f"Worker loop error: {e}")
                time.sleep(1)
    
    def _process_task(self, task: Dict) -> None:
        """处理单个任务"""
        task_id = task['task_id']
        target = task['target']
        
        try:
            # 移动到活跃任务
            with self._lock:
                task['status'] = TaskStatus.RUNNING.value
                task['started_at'] = time.time()
                self.active_tasks[task_id] = task
            
            # 查找目标模块
            if target not in self.modules:
                raise Exception(f"Target module not found: {target}")
            
            module = self.modules[target]
            
            # 执行任务（这里简化为同步调用）
            if hasattr(module, 'handle_task'):
                result = module.handle_task(task)
                task['result'] = result
                task['status'] = TaskStatus.COMPLETED.value
            else:
                raise Exception(f"Module {target} does not support handle_task")
            
        except Exception as e:
            task['status'] = TaskStatus.FAILED.value
            task['error'] = str(e)
            self.logger.error(f"Task {task_id} failed: {e}")
        
        finally:
            # 移动到历史记录
            with self._lock:
                task['completed_at'] = time.time()
                if task_id in self.active_tasks:
                    del self.active_tasks[task_id]
                self.task_history.append(task)
                
                # 限制历史记录大小
                if len(self.task_history) > 1000:
                    self.task_history = self.task_history[-500:]
