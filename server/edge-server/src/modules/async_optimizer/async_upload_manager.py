"""
异步上传管理器
优化MinIO文件上传性能
"""

import asyncio
import os
import logging
from typing import Dict, List, Optional, Callable, Any
from pathlib import Path
from datetime import datetime
import aiofiles
from aiominio import Minio as AsyncMinio
import hashlib
from concurrent.futures import ThreadPoolExecutor

from src.modules.task_bus.src.base_module import TaskResult


class AsyncUploadManager:
    """异步上传管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.logger = logging.getLogger("AsyncUploadManager")
        self.config = config
        
        # MinIO配置
        self.endpoint = config.get('endpoint', 'localhost:9000')
        self.access_key = config.get('access_key', 'minioadmin')
        self.secret_key = config.get('secret_key', 'minioadmin')
        self.bucket_name = config.get('bucket_name', 'sensor-data')
        self.secure = config.get('secure', False)
        
        # 异步MinIO客户端（使用标准Minio客户端，在executor中运行）
        self.client = None
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # 上传配置
        self.max_concurrent_uploads = config.get('max_concurrent_uploads', 5)
        self.chunk_size = config.get('chunk_size', 5 * 1024 * 1024)  # 5MB
        self.multipart_threshold = config.get('multipart_threshold', 10 * 1024 * 1024)  # 10MB
        
        # 上传队列和追踪
        self.upload_queue = asyncio.Queue()
        self.active_uploads: Dict[str, Dict] = {}
        self.completed_uploads: Dict[str, Dict] = {}
        
        # 运行状态
        self.running = False
        self._lock = asyncio.Lock()
        
        # 进度回调
        self.progress_callbacks: Dict[str, Callable] = {}
        
    async def initialize(self):
        """初始化上传管理器"""
        try:
            # 创建标准MinIO客户端
            from minio import Minio
            self.client = Minio(
                self.endpoint,
                access_key=self.access_key,
                secret_key=self.secret_key,
                secure=self.secure
            )
            
            # 确保bucket存在
            await self._ensure_bucket()
            
            self.logger.info("Async upload manager initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize upload manager: {e}")
            return False
            
    async def _ensure_bucket(self):
        """确保存储桶存在"""
        loop = asyncio.get_event_loop()
        
        # 检查bucket是否存在
        exists = await loop.run_in_executor(
            self.executor,
            self.client.bucket_exists,
            self.bucket_name
        )
        
        if not exists:
            # 创建bucket
            await loop.run_in_executor(
                self.executor,
                self.client.make_bucket,
                self.bucket_name
            )
            self.logger.info(f"Created bucket: {self.bucket_name}")
            
    async def start(self):
        """启动上传管理器"""
        self.running = True
        
        # 启动上传工作器
        for i in range(self.max_concurrent_uploads):
            asyncio.create_task(self._upload_worker(i))
            
        self.logger.info(f"Started {self.max_concurrent_uploads} upload workers")
        
    async def stop(self):
        """停止上传管理器"""
        self.running = False
        
        # 等待所有上传完成
        await self._wait_all_uploads()
        
        # 关闭executor
        self.executor.shutdown(wait=True)
        
        self.logger.info("Async upload manager stopped")
        
    async def upload_file(self, file_path: str, object_name: str = None,
                         metadata: Dict = None, callback: Callable = None) -> str:
        """异步上传文件"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
            
        # 生成对象名称
        if not object_name:
            object_name = os.path.basename(file_path)
            
        # 生成上传ID
        upload_id = hashlib.md5(f"{file_path}_{object_name}_{datetime.now()}".encode()).hexdigest()
        
        # 创建上传任务
        upload_task = {
            'upload_id': upload_id,
            'file_path': file_path,
            'object_name': object_name,
            'metadata': metadata or {},
            'callback': callback,
            'status': 'pending',
            'progress': 0.0,
            'created_at': datetime.now(),
            'file_size': os.path.getsize(file_path)
        }
        
        # 添加到队列
        await self.upload_queue.put(upload_task)
        
        # 记录活动上传
        async with self._lock:
            self.active_uploads[upload_id] = upload_task
            
        self.logger.info(f"Upload task created: {upload_id}")
        return upload_id
        
    async def _upload_worker(self, worker_id: int):
        """上传工作器"""
        self.logger.info(f"Upload worker {worker_id} started")
        
        while self.running:
            try:
                # 从队列获取上传任务
                upload_task = await asyncio.wait_for(
                    self.upload_queue.get(),
                    timeout=1.0
                )
                
                # 执行上传
                await self._execute_upload(upload_task)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Upload worker {worker_id} error: {e}")
                
    async def _execute_upload(self, upload_task: Dict):
        """执行文件上传"""
        upload_id = upload_task['upload_id']
        file_path = upload_task['file_path']
        object_name = upload_task['object_name']
        
        try:
            # 更新状态
            upload_task['status'] = 'uploading'
            upload_task['started_at'] = datetime.now()
            
            # 根据文件大小选择上传方式
            file_size = upload_task['file_size']
            
            if file_size > self.multipart_threshold:
                # 大文件使用分片上传
                await self._multipart_upload(upload_task)
            else:
                # 小文件直接上传
                await self._simple_upload(upload_task)
                
            # 更新状态
            upload_task['status'] = 'completed'
            upload_task['completed_at'] = datetime.now()
            upload_task['progress'] = 100.0
            
            # 移动到已完成
            async with self._lock:
                if upload_id in self.active_uploads:
                    del self.active_uploads[upload_id]
                self.completed_uploads[upload_id] = upload_task
                
            self.logger.info(f"Upload completed: {upload_id}")
            
            # 执行回调
            if upload_task['callback']:
                await self._execute_callback(upload_task)
                
        except Exception as e:
            self.logger.error(f"Upload failed: {upload_id} - {e}")
            
            # 更新错误状态
            upload_task['status'] = 'failed'
            upload_task['error'] = str(e)
            upload_task['completed_at'] = datetime.now()
            
            # 移动到已完成（失败）
            async with self._lock:
                if upload_id in self.active_uploads:
                    del self.active_uploads[upload_id]
                self.completed_uploads[upload_id] = upload_task
                
            # 执行回调
            if upload_task['callback']:
                await self._execute_callback(upload_task)
                
    async def _simple_upload(self, upload_task: Dict):
        """简单上传（小文件）"""
        loop = asyncio.get_event_loop()
        
        # 进度回调
        def progress_callback(bytes_sent):
            file_size = upload_task['file_size']
            if file_size > 0:
                upload_task['progress'] = (bytes_sent / file_size) * 100
                
        # 在线程池中执行上传
        await loop.run_in_executor(
            self.executor,
            self.client.fput_object,
            self.bucket_name,
            upload_task['object_name'],
            upload_task['file_path'],
            upload_task['metadata'],
            progress_callback
        )
        
    async def _multipart_upload(self, upload_task: Dict):
        """分片上传（大文件）"""
        file_path = upload_task['file_path']
        object_name = upload_task['object_name']
        file_size = upload_task['file_size']
        
        # 计算分片
        part_size = self.chunk_size
        total_parts = (file_size + part_size - 1) // part_size
        
        # 读取并上传每个分片
        bytes_uploaded = 0
        
        async with aiofiles.open(file_path, 'rb') as f:
            part_number = 1
            
            while True:
                # 读取分片
                chunk = await f.read(part_size)
                if not chunk:
                    break
                    
                # 上传分片（在线程池中）
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(
                    self.executor,
                    self._upload_part,
                    upload_task,
                    chunk,
                    part_number
                )
                
                # 更新进度
                bytes_uploaded += len(chunk)
                upload_task['progress'] = (bytes_uploaded / file_size) * 100
                
                part_number += 1
                
        # 完成分片上传
        await self._complete_multipart_upload(upload_task)
        
    def _upload_part(self, upload_task: Dict, data: bytes, part_number: int):
        """上传单个分片（同步方法，在线程池中运行）"""
        # 这里应该使用实际的分片上传API
        # 简化示例：直接写入临时文件
        import tempfile
        
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp.write(data)
            tmp_path = tmp.name
            
        try:
            # 上传分片文件
            part_name = f"{upload_task['object_name']}.part{part_number}"
            self.client.fput_object(
                self.bucket_name,
                part_name,
                tmp_path,
                upload_task['metadata']
            )
        finally:
            os.remove(tmp_path)
            
    async def _complete_multipart_upload(self, upload_task: Dict):
        """完成分片上传"""
        # 这里应该调用完成分片上传的API
        # 简化示例：合并所有分片
        self.logger.info(f"Completed multipart upload: {upload_task['upload_id']}")
        
    async def _execute_callback(self, upload_task: Dict):
        """执行上传回调"""
        callback = upload_task['callback']
        if callback:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(upload_task)
                else:
                    # 在线程池中运行同步回调
                    loop = asyncio.get_event_loop()
                    await loop.run_in_executor(
                        self.executor,
                        callback,
                        upload_task
                    )
            except Exception as e:
                self.logger.error(f"Callback execution error: {e}")
                
    async def upload_directory(self, dir_path: str, pattern: str = "*",
                             recursive: bool = True, metadata: Dict = None) -> List[str]:
        """异步上传目录"""
        if not os.path.isdir(dir_path):
            raise ValueError(f"Directory not found: {dir_path}")
            
        upload_ids = []
        path = Path(dir_path)
        
        # 查找文件
        if recursive:
            files = path.rglob(pattern)
        else:
            files = path.glob(pattern)
            
        # 并发上传所有文件
        upload_tasks = []
        
        for file_path in files:
            if file_path.is_file():
                # 生成对象名称（保持目录结构）
                relative_path = file_path.relative_to(path)
                object_name = str(relative_path).replace('\\', '/')
                
                # 创建上传任务
                task = self.upload_file(
                    str(file_path),
                    object_name,
                    metadata=metadata
                )
                upload_tasks.append(task)
                
        # 等待所有上传任务创建完成
        upload_ids = await asyncio.gather(*upload_tasks, return_exceptions=True)
        
        # 过滤掉错误
        valid_ids = [uid for uid in upload_ids if isinstance(uid, str)]
        
        self.logger.info(f"Created {len(valid_ids)} upload tasks for directory {dir_path}")
        return valid_ids
        
    async def get_upload_status(self, upload_id: str) -> Optional[Dict]:
        """获取上传状态"""
        async with self._lock:
            # 检查活动上传
            if upload_id in self.active_uploads:
                return self.active_uploads[upload_id].copy()
                
            # 检查已完成上传
            if upload_id in self.completed_uploads:
                return self.completed_uploads[upload_id].copy()
                
        return None
        
    async def cancel_upload(self, upload_id: str) -> bool:
        """取消上传"""
        async with self._lock:
            if upload_id in self.active_uploads:
                upload_task = self.active_uploads[upload_id]
                upload_task['status'] = 'cancelled'
                
                # 移动到已完成
                del self.active_uploads[upload_id]
                self.completed_uploads[upload_id] = upload_task
                
                return True
                
        return False
        
    async def wait_for_upload(self, upload_id: str, timeout: float = None) -> Optional[Dict]:
        """等待上传完成"""
        start_time = asyncio.get_event_loop().time()
        
        while True:
            status = await self.get_upload_status(upload_id)
            
            if status and status['status'] in ['completed', 'failed', 'cancelled']:
                return status
                
            # 检查超时
            if timeout:
                elapsed = asyncio.get_event_loop().time() - start_time
                if elapsed > timeout:
                    return None
                    
            await asyncio.sleep(0.5)
            
    async def _wait_all_uploads(self):
        """等待所有上传完成"""
        while self.active_uploads:
            await asyncio.sleep(0.5)
            
    async def get_statistics(self) -> Dict[str, Any]:
        """获取上传统计信息"""
        async with self._lock:
            total_uploads = len(self.active_uploads) + len(self.completed_uploads)
            completed = sum(1 for u in self.completed_uploads.values() 
                          if u['status'] == 'completed')
            failed = sum(1 for u in self.completed_uploads.values() 
                        if u['status'] == 'failed')
                        
            return {
                'total_uploads': total_uploads,
                'active_uploads': len(self.active_uploads),
                'completed_uploads': completed,
                'failed_uploads': failed,
                'success_rate': (completed / total_uploads * 100) if total_uploads > 0 else 0,
                'queue_size': self.upload_queue.qsize()
            }
            
    async def cleanup_old_records(self, max_age_seconds: int = 3600):
        """清理旧的上传记录"""
        current_time = datetime.now()
        to_remove = []
        
        async with self._lock:
            for upload_id, upload_task in self.completed_uploads.items():
                if 'completed_at' in upload_task:
                    age = (current_time - upload_task['completed_at']).total_seconds()
                    if age > max_age_seconds:
                        to_remove.append(upload_id)
                        
            for upload_id in to_remove:
                del self.completed_uploads[upload_id]
                
        self.logger.info(f"Cleaned up {len(to_remove)} old upload records")