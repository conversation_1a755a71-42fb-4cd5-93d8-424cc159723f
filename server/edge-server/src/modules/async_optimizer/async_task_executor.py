"""
异步任务执行器
优化任务总线的任务执行流程
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
import threading
from queue import Queue, Empty

from src.modules.task_bus.src.base_module import TaskResult, TaskStatus


class AsyncTaskExecutor:
    """异步任务执行器"""
    
    def __init__(self, max_workers: int = 10, max_async_tasks: int = 50):
        self.logger = logging.getLogger("AsyncTaskExecutor")
        self.max_workers = max_workers
        self.max_async_tasks = max_async_tasks
        
        # 线程池用于CPU密集型任务
        self.thread_pool = ThreadPoolExecutor(max_workers=max_workers)
        
        # 异步任务队列
        self.async_queue = asyncio.Queue(maxsize=max_async_tasks)
        
        # 任务跟踪
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.task_results: Dict[str, TaskResult] = {}
        
        # 运行状态
        self.running = False
        self._lock = asyncio.Lock()
        
        # 任务完成回调
        self.completion_callbacks: Dict[str, List[Callable]] = {}
        
    async def start(self):
        """启动执行器"""
        self.running = True
        self.logger.info("Async task executor started")
        
        # 启动任务处理器
        asyncio.create_task(self._task_processor())
        
    async def stop(self):
        """停止执行器"""
        self.running = False
        
        # 等待所有任务完成
        await self._wait_all_tasks()
        
        # 关闭线程池
        self.thread_pool.shutdown(wait=True)
        
        self.logger.info("Async task executor stopped")
        
    async def submit_task(self, task_id: str, task_func: Callable, 
                         args: tuple = (), kwargs: dict = None,
                         is_async: bool = True) -> str:
        """提交任务执行"""
        kwargs = kwargs or {}
        
        task_info = {
            'task_id': task_id,
            'func': task_func,
            'args': args,
            'kwargs': kwargs,
            'is_async': is_async,
            'submitted_at': datetime.now()
        }
        
        # 添加到队列
        await self.async_queue.put(task_info)
        
        self.logger.debug(f"Task {task_id} submitted for execution")
        return task_id
        
    async def _task_processor(self):
        """任务处理循环"""
        while self.running:
            try:
                # 从队列获取任务
                task_info = await asyncio.wait_for(
                    self.async_queue.get(),
                    timeout=1.0
                )
                
                # 执行任务
                asyncio.create_task(self._execute_task(task_info))
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Task processor error: {e}")
                
    async def _execute_task(self, task_info: Dict):
        """执行单个任务"""
        task_id = task_info['task_id']
        
        try:
            async with self._lock:
                # 创建任务
                if task_info['is_async']:
                    task = asyncio.create_task(
                        self._run_async_task(task_info)
                    )
                else:
                    task = asyncio.create_task(
                        self._run_sync_task(task_info)
                    )
                    
                self.running_tasks[task_id] = task
                
            # 等待任务完成
            result = await task
            
            # 保存结果
            async with self._lock:
                self.task_results[task_id] = result
                del self.running_tasks[task_id]
                
            # 执行回调
            await self._execute_callbacks(task_id, result)
            
        except Exception as e:
            self.logger.error(f"Task {task_id} execution failed: {e}")
            
            # 保存错误结果
            error_result = TaskResult(False, error=str(e))
            async with self._lock:
                self.task_results[task_id] = error_result
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]
                    
            await self._execute_callbacks(task_id, error_result)
            
    async def _run_async_task(self, task_info: Dict) -> TaskResult:
        """运行异步任务"""
        func = task_info['func']
        args = task_info['args']
        kwargs = task_info['kwargs']
        
        try:
            # 如果是异步函数，直接调用
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                # 如果是同步函数，在线程池中运行
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    self.thread_pool, func, *args
                )
                
            # 包装结果
            if isinstance(result, TaskResult):
                return result
            else:
                return TaskResult(True, data=result)
                
        except Exception as e:
            self.logger.error(f"Async task execution error: {e}")
            return TaskResult(False, error=str(e))
            
    async def _run_sync_task(self, task_info: Dict) -> TaskResult:
        """在线程池中运行同步任务"""
        func = task_info['func']
        args = task_info['args']
        kwargs = task_info['kwargs']
        
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.thread_pool,
                lambda: func(*args, **kwargs)
            )
            
            if isinstance(result, TaskResult):
                return result
            else:
                return TaskResult(True, data=result)
                
        except Exception as e:
            self.logger.error(f"Sync task execution error: {e}")
            return TaskResult(False, error=str(e))
            
    async def add_completion_callback(self, task_id: str, callback: Callable):
        """添加任务完成回调"""
        async with self._lock:
            if task_id not in self.completion_callbacks:
                self.completion_callbacks[task_id] = []
            self.completion_callbacks[task_id].append(callback)
            
    async def _execute_callbacks(self, task_id: str, result: TaskResult):
        """执行任务完成回调"""
        callbacks = self.completion_callbacks.get(task_id, [])
        
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(task_id, result)
                else:
                    # 在线程池中运行同步回调
                    await asyncio.get_event_loop().run_in_executor(
                        self.thread_pool,
                        callback,
                        task_id,
                        result
                    )
            except Exception as e:
                self.logger.error(f"Callback execution error: {e}")
                
        # 清理回调
        if task_id in self.completion_callbacks:
            del self.completion_callbacks[task_id]
            
    async def get_task_result(self, task_id: str, 
                            timeout: Optional[float] = None) -> Optional[TaskResult]:
        """获取任务结果"""
        # 如果结果已经存在，直接返回
        if task_id in self.task_results:
            return self.task_results[task_id]
            
        # 如果任务还在运行，等待完成
        if task_id in self.running_tasks:
            task = self.running_tasks[task_id]
            try:
                if timeout:
                    await asyncio.wait_for(task, timeout=timeout)
                else:
                    await task
                    
                return self.task_results.get(task_id)
                
            except asyncio.TimeoutError:
                self.logger.warning(f"Task {task_id} timeout")
                return None
                
        return None
        
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        async with self._lock:
            if task_id in self.running_tasks:
                task = self.running_tasks[task_id]
                task.cancel()
                
                # 等待任务取消
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                    
                # 清理
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]
                    
                # 添加取消结果
                self.task_results[task_id] = TaskResult(
                    False, 
                    error="Task cancelled"
                )
                
                return True
                
        return False
        
    async def _wait_all_tasks(self):
        """等待所有任务完成"""
        if self.running_tasks:
            await asyncio.gather(
                *self.running_tasks.values(),
                return_exceptions=True
            )
            
    async def get_running_tasks(self) -> List[str]:
        """获取正在运行的任务列表"""
        async with self._lock:
            return list(self.running_tasks.keys())
            
    async def get_statistics(self) -> Dict[str, Any]:
        """获取执行器统计信息"""
        async with self._lock:
            return {
                'running_tasks': len(self.running_tasks),
                'completed_tasks': len(self.task_results),
                'queue_size': self.async_queue.qsize(),
                'max_workers': self.max_workers,
                'max_async_tasks': self.max_async_tasks
            }
            
    def cleanup_old_results(self, max_age_seconds: int = 3600):
        """清理旧的任务结果"""
        current_time = datetime.now()
        to_remove = []
        
        for task_id, result in self.task_results.items():
            if hasattr(result, 'completed_at'):
                age = (current_time - result.completed_at).total_seconds()
                if age > max_age_seconds:
                    to_remove.append(task_id)
                    
        for task_id in to_remove:
            del self.task_results[task_id]
            
        self.logger.info(f"Cleaned up {len(to_remove)} old task results")