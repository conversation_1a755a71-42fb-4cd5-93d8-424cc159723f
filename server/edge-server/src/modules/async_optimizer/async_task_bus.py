"""
异步任务总线
集成异步优化功能的任务总线包装器
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime

from src.modules.task_bus.src.task_bus import TaskBus
from .async_task_executor import AsyncTaskExecutor
from .async_upload_manager import AsyncUploadManager
from .async_hardware_controller import AsyncHardwareController
from .async_data_collector import AsyncDataCollector


class AsyncTaskBus:
    """异步优化的任务总线"""
    
    def __init__(self, config: Dict[str, Any]):
        self.logger = logging.getLogger("AsyncTaskBus")
        self.config = config
        
        # 原始任务总线
        self.task_bus = TaskBus(config.get('task_bus', {}))
        
        # 异步优化组件
        self.async_executor = AsyncTaskExecutor(
            max_workers=config.get('max_workers', 10),
            max_async_tasks=config.get('max_async_tasks', 50)
        )
        
        self.async_upload_manager = None
        self.async_hardware_controller = None
        self.async_data_collector = None
        
        # 异步任务映射
        self.async_handlers: Dict[str, Callable] = {}
        
        # 运行状态
        self.running = False
        self._event_loop = None
        
    async def initialize(self):
        """初始化异步任务总线"""
        try:
            # 初始化异步组件
            await self.async_executor.start()
            
            # 初始化上传管理器
            if 'minio' in self.config:
                self.async_upload_manager = AsyncUploadManager(self.config['minio'])
                await self.async_upload_manager.initialize()
                await self.async_upload_manager.start()
                
            # 初始化硬件控制器
            if 'hardware' in self.config:
                self.async_hardware_controller = AsyncHardwareController(self.config['hardware'])
                await self.async_hardware_controller.initialize()
                await self.async_hardware_controller.start()
                
            # 初始化数据采集器
            if 'data_collector' in self.config:
                self.async_data_collector = AsyncDataCollector(self.config['data_collector'])
                await self.async_data_collector.initialize()
                await self.async_data_collector.start()
                
            # 注册异步处理器
            self._register_async_handlers()
            
            self.logger.info("Async task bus initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize async task bus: {e}")
            return False
            
    def start(self):
        """启动异步任务总线"""
        # 启动原始任务总线
        self.task_bus.start()
        
        # 创建异步事件循环
        self._event_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self._event_loop)
        
        # 初始化异步组件
        self._event_loop.run_until_complete(self.initialize())
        
        self.running = True
        self.logger.info("Async task bus started")
        
    def stop(self):
        """停止异步任务总线"""
        self.running = False
        
        # 停止异步组件
        if self._event_loop:
            self._event_loop.run_until_complete(self._shutdown_async_components())
            
        # 停止原始任务总线
        self.task_bus.stop()
        
        # 关闭事件循环
        if self._event_loop:
            self._event_loop.close()
            
        self.logger.info("Async task bus stopped")
        
    async def _shutdown_async_components(self):
        """关闭异步组件"""
        tasks = []
        
        if self.async_executor:
            tasks.append(self.async_executor.stop())
            
        if self.async_upload_manager:
            tasks.append(self.async_upload_manager.stop())
            
        if self.async_hardware_controller:
            tasks.append(self.async_hardware_controller.stop())
            
        if self.async_data_collector:
            tasks.append(self.async_data_collector.stop())
            
        await asyncio.gather(*tasks, return_exceptions=True)
        
    def _register_async_handlers(self):
        """注册异步处理器"""
        # MinIO上传操作
        self.async_handlers['minio.upload_file'] = self._handle_async_upload_file
        self.async_handlers['minio.upload_directory'] = self._handle_async_upload_directory
        
        # 硬件控制操作
        self.async_handlers['hardware.power_on'] = self._handle_async_power_on
        self.async_handlers['hardware.power_off'] = self._handle_async_power_off
        self.async_handlers['hardware.ptz_goto'] = self._handle_async_ptz_goto
        
        # 数据采集操作
        self.async_handlers['lidar.start_point_scan'] = self._handle_async_point_scan
        self.async_handlers['lidar.start_terrain_scan'] = self._handle_async_terrain_scan
        
    def submit_task(self, task_type: str, target: str, action: str,
                   params: Dict = None) -> str:
        """提交任务（支持异步优化）"""
        # 检查是否有异步处理器
        handler_key = f"{target}.{action}"
        
        if handler_key in self.async_handlers and self._event_loop:
            # 使用异步处理
            task_id = self._generate_task_id()
            
            # 在事件循环中执行异步任务
            future = asyncio.run_coroutine_threadsafe(
                self._execute_async_task(task_id, handler_key, params),
                self._event_loop
            )
            
            # 包装为同步返回
            return task_id
        else:
            # 使用原始任务总线
            return self.task_bus.submit_task(task_type, target, action, params)
            
    async def _execute_async_task(self, task_id: str, handler_key: str, params: Dict):
        """执行异步任务"""
        handler = self.async_handlers[handler_key]
        
        try:
            # 提交到异步执行器
            await self.async_executor.submit_task(
                task_id,
                handler,
                args=(params,),
                is_async=True
            )
            
            # 发布任务创建事件
            self.task_bus.publish_message('task_created', {
                'task_id': task_id,
                'handler': handler_key,
                'params': params,
                'async': True
            })
            
        except Exception as e:
            self.logger.error(f"Failed to execute async task {task_id}: {e}")
            
    async def _handle_async_upload_file(self, params: Dict) -> Dict[str, Any]:
        """异步处理文件上传"""
        if not self.async_upload_manager:
            return {'success': False, 'error': 'Upload manager not initialized'}
            
        file_path = params.get('file_path')
        object_name = params.get('object_name')
        metadata = params.get('metadata', {})
        
        try:
            upload_id = await self.async_upload_manager.upload_file(
                file_path,
                object_name,
                metadata
            )
            
            # 等待上传完成（可选）
            if params.get('wait_for_completion', False):
                result = await self.async_upload_manager.wait_for_upload(upload_id)
                return {
                    'success': result['status'] == 'completed',
                    'upload_id': upload_id,
                    'result': result
                }
            else:
                return {
                    'success': True,
                    'upload_id': upload_id
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
            
    async def _handle_async_upload_directory(self, params: Dict) -> Dict[str, Any]:
        """异步处理目录上传"""
        if not self.async_upload_manager:
            return {'success': False, 'error': 'Upload manager not initialized'}
            
        dir_path = params.get('dir_path')
        pattern = params.get('pattern', '*')
        recursive = params.get('recursive', True)
        metadata = params.get('metadata', {})
        
        try:
            upload_ids = await self.async_upload_manager.upload_directory(
                dir_path,
                pattern,
                recursive,
                metadata
            )
            
            return {
                'success': True,
                'upload_ids': upload_ids,
                'count': len(upload_ids)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
            
    async def _handle_async_power_on(self, params: Dict) -> Dict[str, Any]:
        """异步处理电源开启"""
        if not self.async_hardware_controller:
            return {'success': False, 'error': 'Hardware controller not initialized'}
            
        device = params.get('device')
        return await self.async_hardware_controller.power_on(device)
        
    async def _handle_async_power_off(self, params: Dict) -> Dict[str, Any]:
        """异步处理电源关闭"""
        if not self.async_hardware_controller:
            return {'success': False, 'error': 'Hardware controller not initialized'}
            
        device = params.get('device')
        return await self.async_hardware_controller.power_off(device)
        
    async def _handle_async_ptz_goto(self, params: Dict) -> Dict[str, Any]:
        """异步处理PTZ定位"""
        if not self.async_hardware_controller:
            return {'success': False, 'error': 'Hardware controller not initialized'}
            
        horizontal = params.get('horizontal', 0)
        vertical = params.get('vertical', 0)
        speed = params.get('speed', 30)
        
        return await self.async_hardware_controller.ptz_goto(horizontal, vertical, speed)
        
    async def _handle_async_point_scan(self, params: Dict) -> Dict[str, Any]:
        """异步处理定点扫描"""
        if not self.async_data_collector:
            return {'success': False, 'error': 'Data collector not initialized'}
            
        scan_id = self._generate_task_id()
        return await self.async_data_collector.start_lidar_scan(
            scan_id,
            'point_scan',
            params
        )
        
    async def _handle_async_terrain_scan(self, params: Dict) -> Dict[str, Any]:
        """异步处理地形扫描"""
        if not self.async_data_collector:
            return {'success': False, 'error': 'Data collector not initialized'}
            
        scan_id = self._generate_task_id()
        return await self.async_data_collector.start_lidar_scan(
            scan_id,
            'terrain_scan',
            params
        )
        
    def _generate_task_id(self) -> str:
        """生成任务ID"""
        import uuid
        return f"async_{uuid.uuid4().hex[:8]}_{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
    def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务状态"""
        # 检查异步任务
        if task_id.startswith('async_') and self._event_loop:
            future = asyncio.run_coroutine_threadsafe(
                self.async_executor.get_task_result(task_id),
                self._event_loop
            )
            
            try:
                result = future.result(timeout=1.0)
                if result:
                    return {
                        'task_id': task_id,
                        'status': 'completed' if result.success else 'failed',
                        'result': result.data if result.success else result.error,
                        'async': True
                    }
            except:
                pass
                
        # 使用原始任务总线
        return self.task_bus.get_task_status(task_id)
        
    async def get_async_statistics(self) -> Dict[str, Any]:
        """获取异步组件统计信息"""
        stats = {
            'executor': await self.async_executor.get_statistics() if self.async_executor else {},
            'upload_manager': await self.async_upload_manager.get_statistics() if self.async_upload_manager else {},
            'hardware_controller': await self.async_hardware_controller.get_statistics() if self.async_hardware_controller else {},
            'data_collector': await self.async_data_collector.get_statistics() if self.async_data_collector else {}
        }
        
        return stats
        
    def get_status(self) -> Dict[str, Any]:
        """获取总线状态"""
        base_status = self.task_bus.get_status()
        
        # 添加异步统计
        if self._event_loop:
            future = asyncio.run_coroutine_threadsafe(
                self.get_async_statistics(),
                self._event_loop
            )
            
            try:
                async_stats = future.result(timeout=1.0)
                base_status['async_components'] = async_stats
            except:
                base_status['async_components'] = {}
                
        return base_status
        
    # 代理其他方法到原始任务总线
    def register_module(self, module):
        return self.task_bus.register_module(module)
        
    def unregister_module(self, module_name):
        return self.task_bus.unregister_module(module_name)
        
    def cancel_task(self, task_id):
        if task_id.startswith('async_') and self._event_loop:
            future = asyncio.run_coroutine_threadsafe(
                self.async_executor.cancel_task(task_id),
                self._event_loop
            )
            return future.result(timeout=1.0)
        return self.task_bus.cancel_task(task_id)
        
    def get_registered_modules(self):
        return self.task_bus.get_registered_modules()
        
    def get_active_tasks(self):
        return self.task_bus.get_active_tasks()
        
    def publish_message(self, topic, data):
        return self.task_bus.publish_message(topic, data)
        
    def subscribe_message(self, topic, callback):
        return self.task_bus.subscribe_message(topic, callback)