"""
异步数据采集器
优化激光雷达和相机的数据采集性能
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
import os
from pathlib import Path
import json
from concurrent.futures import ThreadPoolExecutor


class AsyncDataCollector:
    """异步数据采集器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.logger = logging.getLogger("AsyncDataCollector")
        self.config = config
        
        # 数据目录
        self.data_dir = config.get('data_dir', '/home/<USER>/edge-server/data')
        
        # 激光雷达配置
        self.lidar_config = config.get('lidar', {})
        self.lidar_ip = self.lidar_config.get('ip', '*************')
        self.lidar_port = self.lidar_config.get('port', 2368)
        
        # 相机配置
        self.camera_config = config.get('camera', {})
        self.camera_url = self.camera_config.get('url', 'http://localhost:8080')
        
        # 并发控制
        self.max_concurrent_scans = config.get('max_concurrent_scans', 3)
        self.max_concurrent_captures = config.get('max_concurrent_captures', 5)
        
        # 线程池（用于I/O密集型操作）
        self.executor = ThreadPoolExecutor(max_workers=10)
        
        # 采集任务跟踪
        self.active_scans: Dict[str, Dict] = {}
        self.active_captures: Dict[str, Dict] = {}
        self._lock = asyncio.Lock()
        
        # 数据缓冲区
        self.data_buffer = asyncio.Queue(maxsize=1000)
        self.buffer_processor_task = None
        
        # 运行状态
        self.running = False
        
    async def initialize(self):
        """初始化数据采集器"""
        try:
            # 创建数据目录
            os.makedirs(self.data_dir, exist_ok=True)
            os.makedirs(os.path.join(self.data_dir, 'lidar'), exist_ok=True)
            os.makedirs(os.path.join(self.data_dir, 'camera'), exist_ok=True)
            
            # 初始化采集服务（这里应该初始化实际的SDK）
            # self.lidar_service = await self._init_lidar_service()
            # self.camera_service = await self._init_camera_service()
            
            self.logger.info("Async data collector initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize data collector: {e}")
            return False
            
    async def start(self):
        """启动数据采集器"""
        self.running = True
        
        # 启动缓冲区处理器
        self.buffer_processor_task = asyncio.create_task(self._buffer_processor())
        
        self.logger.info("Async data collector started")
        
    async def stop(self):
        """停止数据采集器"""
        self.running = False
        
        # 停止所有活动的采集
        await self.stop_all_scans()
        await self.stop_all_captures()
        
        # 等待缓冲区清空
        if self.buffer_processor_task:
            await self.buffer_processor_task
            
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        self.logger.info("Async data collector stopped")
        
    async def start_lidar_scan(self, scan_id: str, scan_type: str,
                              params: Dict[str, Any]) -> Dict[str, Any]:
        """异步启动激光雷达扫描"""
        async with self._lock:
            if len(self.active_scans) >= self.max_concurrent_scans:
                return {
                    'success': False,
                    'error': f'Maximum concurrent scans ({self.max_concurrent_scans}) reached'
                }
                
        # 创建扫描任务
        scan_task = {
            'scan_id': scan_id,
            'scan_type': scan_type,
            'params': params,
            'status': 'starting',
            'started_at': datetime.now(),
            'data_count': 0,
            'output_dir': None
        }
        
        # 添加到活动扫描
        async with self._lock:
            self.active_scans[scan_id] = scan_task
            
        # 启动扫描
        if scan_type == 'point_scan':
            asyncio.create_task(self._execute_point_scan(scan_task))
        elif scan_type == 'terrain_scan':
            asyncio.create_task(self._execute_terrain_scan(scan_task))
        else:
            return {'success': False, 'error': f'Unknown scan type: {scan_type}'}
            
        return {
            'success': True,
            'scan_id': scan_id,
            'message': f'{scan_type} started'
        }
        
    async def _execute_point_scan(self, scan_task: Dict):
        """执行定点扫描"""
        scan_id = scan_task['scan_id']
        params = scan_task['params']
        
        try:
            # 更新状态
            scan_task['status'] = 'scanning'
            
            # 创建输出目录
            output_dir = os.path.join(
                self.data_dir, 'lidar', 'point_scan',
                f'{scan_id}_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
            )
            os.makedirs(output_dir, exist_ok=True)
            scan_task['output_dir'] = output_dir
            
            # 获取扫描参数
            duration = params.get('duration', 60)
            position = params.get('position', {})
            horizontal = position.get('horizontal', 0)
            vertical = position.get('vertical', 0)
            frequency = params.get('frequency', 10)  # 10Hz
            
            # 模拟扫描数据采集
            frame_interval = 1.0 / frequency
            total_frames = int(duration * frequency)
            
            for frame_idx in range(total_frames):
                if scan_task['status'] != 'scanning':
                    break
                    
                # 生成帧数据（实际应该从SDK获取）
                frame_data = await self._generate_lidar_frame(
                    frame_idx, horizontal, vertical
                )
                
                # 保存帧数据
                frame_file = os.path.join(output_dir, f'frame_{frame_idx:06d}.pcd')
                await self._save_frame_async(frame_file, frame_data)
                
                # 更新计数
                scan_task['data_count'] = frame_idx + 1
                
                # 添加到缓冲区
                await self.data_buffer.put({
                    'type': 'lidar_frame',
                    'scan_id': scan_id,
                    'frame_idx': frame_idx,
                    'file_path': frame_file,
                    'timestamp': datetime.now()
                })
                
                # 等待下一帧
                await asyncio.sleep(frame_interval)
                
            # 完成扫描
            scan_task['status'] = 'completed'
            scan_task['completed_at'] = datetime.now()
            
            # 生成元数据
            metadata = {
                'scan_id': scan_id,
                'scan_type': 'point_scan',
                'params': params,
                'total_frames': scan_task['data_count'],
                'duration': duration,
                'started_at': scan_task['started_at'].isoformat(),
                'completed_at': scan_task['completed_at'].isoformat()
            }
            
            metadata_file = os.path.join(output_dir, 'metadata.json')
            async with asyncio.get_event_loop().run_in_executor(
                self.executor, open, metadata_file, 'w'
            ) as f:
                await asyncio.get_event_loop().run_in_executor(
                    self.executor, json.dump, metadata, f, 2
                )
                
            self.logger.info(f"Point scan {scan_id} completed: {scan_task['data_count']} frames")
            
        except Exception as e:
            self.logger.error(f"Point scan {scan_id} failed: {e}")
            scan_task['status'] = 'failed'
            scan_task['error'] = str(e)
            
        finally:
            # 清理
            async with self._lock:
                if scan_id in self.active_scans:
                    del self.active_scans[scan_id]
                    
    async def _execute_terrain_scan(self, scan_task: Dict):
        """执行地形扫描"""
        scan_id = scan_task['scan_id']
        params = scan_task['params']
        
        try:
            # 更新状态
            scan_task['status'] = 'scanning'
            
            # 创建输出目录
            output_dir = os.path.join(
                self.data_dir, 'lidar', 'terrain_scan',
                f'{scan_id}_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
            )
            os.makedirs(output_dir, exist_ok=True)
            scan_task['output_dir'] = output_dir
            
            # 获取扫描参数
            scan_area = params.get('scan_area', {})
            h_start = scan_area.get('h_start', -45)
            h_end = scan_area.get('h_end', 45)
            v_start = scan_area.get('v_start', -30)
            v_end = scan_area.get('v_end', 30)
            h_step = params.get('h_step', 1)
            v_step = params.get('v_step', 1)
            duration_per_position = params.get('duration_per_position', 5)
            
            # 计算扫描位置
            positions = []
            for h in range(int(h_start), int(h_end) + 1, int(h_step)):
                for v in range(int(v_start), int(v_end) + 1, int(v_step)):
                    positions.append((h, v))
                    
            # 累积点云数据
            accumulated_points = []
            
            # 扫描每个位置
            for idx, (h, v) in enumerate(positions):
                if scan_task['status'] != 'scanning':
                    break
                    
                # 移动到位置（实际应该控制PTZ）
                self.logger.debug(f"Scanning position {idx+1}/{len(positions)}: H={h}, V={v}")
                
                # 采集该位置的数据
                position_data = await self._scan_position(h, v, duration_per_position)
                accumulated_points.extend(position_data)
                
                # 更新进度
                scan_task['data_count'] = len(accumulated_points)
                scan_task['progress'] = (idx + 1) / len(positions) * 100
                
            # 保存累积的点云
            output_file = os.path.join(output_dir, 'terrain_scan.pcd')
            await self._save_pointcloud_async(output_file, accumulated_points)
            
            # 完成扫描
            scan_task['status'] = 'completed'
            scan_task['completed_at'] = datetime.now()
            
            # 生成元数据
            metadata = {
                'scan_id': scan_id,
                'scan_type': 'terrain_scan',
                'params': params,
                'total_positions': len(positions),
                'total_points': len(accumulated_points),
                'started_at': scan_task['started_at'].isoformat(),
                'completed_at': scan_task['completed_at'].isoformat()
            }
            
            metadata_file = os.path.join(output_dir, 'metadata.json')
            async with asyncio.get_event_loop().run_in_executor(
                self.executor, open, metadata_file, 'w'
            ) as f:
                await asyncio.get_event_loop().run_in_executor(
                    self.executor, json.dump, metadata, f, 2
                )
                
            self.logger.info(f"Terrain scan {scan_id} completed: {len(accumulated_points)} points")
            
        except Exception as e:
            self.logger.error(f"Terrain scan {scan_id} failed: {e}")
            scan_task['status'] = 'failed'
            scan_task['error'] = str(e)
            
        finally:
            # 清理
            async with self._lock:
                if scan_id in self.active_scans:
                    del self.active_scans[scan_id]
                    
    async def stop_lidar_scan(self, scan_id: str) -> Dict[str, Any]:
        """停止激光雷达扫描"""
        async with self._lock:
            if scan_id in self.active_scans:
                self.active_scans[scan_id]['status'] = 'stopping'
                return {'success': True, 'message': 'Scan stopping'}
            else:
                return {'success': False, 'error': 'Scan not found'}
                
    async def get_scan_status(self, scan_id: str) -> Optional[Dict[str, Any]]:
        """获取扫描状态"""
        async with self._lock:
            if scan_id in self.active_scans:
                scan_task = self.active_scans[scan_id].copy()
                return {
                    'scan_id': scan_id,
                    'status': scan_task['status'],
                    'scan_type': scan_task['scan_type'],
                    'data_count': scan_task['data_count'],
                    'output_dir': scan_task['output_dir'],
                    'started_at': scan_task['started_at'].isoformat(),
                    'progress': scan_task.get('progress', 0)
                }
        return None
        
    async def start_camera_capture(self, capture_id: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """异步启动相机采集"""
        async with self._lock:
            if len(self.active_captures) >= self.max_concurrent_captures:
                return {
                    'success': False,
                    'error': f'Maximum concurrent captures ({self.max_concurrent_captures}) reached'
                }
                
        # 创建采集任务
        capture_task = {
            'capture_id': capture_id,
            'params': params,
            'status': 'starting',
            'started_at': datetime.now(),
            'frame_count': 0,
            'output_dir': None
        }
        
        # 添加到活动采集
        async with self._lock:
            self.active_captures[capture_id] = capture_task
            
        # 启动采集
        asyncio.create_task(self._execute_camera_capture(capture_task))
        
        return {
            'success': True,
            'capture_id': capture_id,
            'message': 'Camera capture started'
        }
        
    async def _execute_camera_capture(self, capture_task: Dict):
        """执行相机采集"""
        capture_id = capture_task['capture_id']
        params = capture_task['params']
        
        try:
            # 实现相机采集逻辑
            # ...
            
            capture_task['status'] = 'completed'
            
        except Exception as e:
            self.logger.error(f"Camera capture {capture_id} failed: {e}")
            capture_task['status'] = 'failed'
            capture_task['error'] = str(e)
            
        finally:
            # 清理
            async with self._lock:
                if capture_id in self.active_captures:
                    del self.active_captures[capture_id]
                    
    async def _buffer_processor(self):
        """处理数据缓冲区"""
        while self.running:
            try:
                # 批量处理缓冲区数据
                batch = []
                deadline = asyncio.get_event_loop().time() + 1.0  # 1秒批处理窗口
                
                while asyncio.get_event_loop().time() < deadline and len(batch) < 100:
                    try:
                        data = await asyncio.wait_for(
                            self.data_buffer.get(),
                            timeout=0.1
                        )
                        batch.append(data)
                    except asyncio.TimeoutError:
                        break
                        
                if batch:
                    await self._process_data_batch(batch)
                    
            except Exception as e:
                self.logger.error(f"Buffer processor error: {e}")
                await asyncio.sleep(1)
                
    async def _process_data_batch(self, batch: List[Dict]):
        """处理数据批次"""
        # 这里可以实现数据预处理、压缩、索引等操作
        self.logger.debug(f"Processing data batch: {len(batch)} items")
        
    async def _generate_lidar_frame(self, frame_idx: int, h: float, v: float) -> bytes:
        """生成激光雷达帧数据（模拟）"""
        # 实际应该从SDK获取数据
        import random
        
        points = []
        for i in range(1000):  # 每帧1000个点
            x = random.uniform(-50, 50)
            y = random.uniform(-50, 50)
            z = random.uniform(0, 10)
            intensity = random.randint(0, 255)
            points.append(f"{x:.3f} {y:.3f} {z:.3f} {intensity}")
            
        # PCD格式头
        header = f"""# .PCD v0.7 - Point Cloud Data file format
VERSION 0.7
FIELDS x y z intensity
SIZE 4 4 4 4
TYPE F F F U
COUNT 1 1 1 1
WIDTH {len(points)}
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS {len(points)}
DATA ascii
"""
        
        return (header + '\n'.join(points)).encode()
        
    async def _scan_position(self, h: float, v: float, duration: float) -> List[Dict]:
        """扫描特定位置（模拟）"""
        # 实际应该控制PTZ并采集数据
        await asyncio.sleep(duration)
        
        # 返回模拟数据
        points = []
        for i in range(int(duration * 1000)):  # 每秒1000个点
            points.append({
                'x': h + (i % 10) * 0.1,
                'y': v + (i % 10) * 0.1,
                'z': i * 0.01,
                'intensity': i % 255
            })
        return points
        
    async def _save_frame_async(self, file_path: str, data: bytes):
        """异步保存帧数据"""
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            self.executor,
            lambda: Path(file_path).write_bytes(data)
        )
        
    async def _save_pointcloud_async(self, file_path: str, points: List[Dict]):
        """异步保存点云数据"""
        # 生成PCD格式
        header = f"""# .PCD v0.7 - Point Cloud Data file format
VERSION 0.7
FIELDS x y z intensity
SIZE 4 4 4 4
TYPE F F F U
COUNT 1 1 1 1
WIDTH {len(points)}
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS {len(points)}
DATA ascii
"""
        
        lines = [header]
        for p in points:
            lines.append(f"{p['x']:.3f} {p['y']:.3f} {p['z']:.3f} {p['intensity']}")
            
        data = '\n'.join(lines).encode()
        
        # 异步保存
        await self._save_frame_async(file_path, data)
        
    async def stop_all_scans(self):
        """停止所有扫描"""
        async with self._lock:
            for scan_id in list(self.active_scans.keys()):
                self.active_scans[scan_id]['status'] = 'stopping'
                
    async def stop_all_captures(self):
        """停止所有采集"""
        async with self._lock:
            for capture_id in list(self.active_captures.keys()):
                self.active_captures[capture_id]['status'] = 'stopping'
                
    async def get_statistics(self) -> Dict[str, Any]:
        """获取采集器统计信息"""
        async with self._lock:
            return {
                'active_scans': len(self.active_scans),
                'active_captures': len(self.active_captures),
                'buffer_size': self.data_buffer.qsize(),
                'max_concurrent_scans': self.max_concurrent_scans,
                'max_concurrent_captures': self.max_concurrent_captures
            }