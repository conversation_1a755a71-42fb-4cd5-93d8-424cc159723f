"""
异步硬件控制器
优化硬件控制操作的并发性能
"""

import asyncio
import aiohttp
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import json
from asyncio import Queue, Lock


class AsyncHardwareController:
    """异步硬件控制器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.logger = logging.getLogger("AsyncHardwareController")
        self.config = config
        
        # 硬件服务配置
        self.base_url = config.get('base_url', 'http://localhost:7080')
        self.channel_id = config.get('channel_id', 'd4ad2070b92f0000')
        self.timeout = config.get('timeout', 30)
        
        # DO口配置
        self.do_config = {
            'lidar': 1,
            'ptz': 2,
            'spare1': 3,
            'spare2': 4,
            'spare3': 5,
            'spare4': 6
        }
        
        # HTTP会话
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 命令队列和并发控制
        self.command_queue = Queue()
        self.max_concurrent_commands = config.get('max_concurrent_commands', 5)
        
        # 状态缓存
        self.status_cache = {}
        self.cache_ttl = config.get('cache_ttl', 5)  # 5秒缓存
        self.cache_lock = Lock()
        
        # 运行状态
        self.running = False
        
    async def initialize(self):
        """初始化控制器"""
        try:
            # 创建HTTP会话
            connector = aiohttp.TCPConnector(limit=self.max_concurrent_commands)
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=self.timeout)
            )
            
            # 测试连接
            await self._test_connection()
            
            self.logger.info("Async hardware controller initialized")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize hardware controller: {e}")
            return False
            
    async def _test_connection(self):
        """测试硬件服务连接"""
        async with self.session.get(f"{self.base_url}/health") as response:
            if response.status != 200:
                raise ConnectionError(f"Hardware service returned {response.status}")
                
    async def start(self):
        """启动控制器"""
        self.running = True
        
        # 启动命令处理器
        for i in range(self.max_concurrent_commands):
            asyncio.create_task(self._command_processor(i))
            
        # 启动状态更新器
        asyncio.create_task(self._status_updater())
        
        self.logger.info("Async hardware controller started")
        
    async def stop(self):
        """停止控制器"""
        self.running = False
        
        # 关闭HTTP会话
        if self.session:
            await self.session.close()
            
        self.logger.info("Async hardware controller stopped")
        
    async def power_on(self, device: str) -> Dict[str, Any]:
        """异步开启设备电源"""
        if device not in self.do_config:
            return {'success': False, 'error': f'Unknown device: {device}'}
            
        command = {
            'type': 'power_on',
            'device': device,
            'do_index': self.do_config[device],
            'timestamp': datetime.now()
        }
        
        return await self._execute_command(command)
        
    async def power_off(self, device: str) -> Dict[str, Any]:
        """异步关闭设备电源"""
        if device not in self.do_config:
            return {'success': False, 'error': f'Unknown device: {device}'}
            
        command = {
            'type': 'power_off',
            'device': device,
            'do_index': self.do_config[device],
            'timestamp': datetime.now()
        }
        
        return await self._execute_command(command)
        
    async def power_cycle(self, device: str, delay: float = 5.0) -> Dict[str, Any]:
        """异步重启设备电源"""
        # 先关闭
        off_result = await self.power_off(device)
        if not off_result.get('success'):
            return off_result
            
        # 等待
        await asyncio.sleep(delay)
        
        # 再开启
        return await self.power_on(device)
        
    async def ptz_goto(self, horizontal: float, vertical: float, 
                      speed: int = 30) -> Dict[str, Any]:
        """异步控制PTZ定位"""
        command = {
            'type': 'ptz_goto',
            'horizontal': horizontal,
            'vertical': vertical,
            'speed': speed,
            'timestamp': datetime.now()
        }
        
        return await self._execute_command(command)
        
    async def ptz_move(self, direction: str, speed: int = 50, 
                      duration: float = 1.0) -> Dict[str, Any]:
        """异步控制PTZ移动"""
        if direction not in ['up', 'down', 'left', 'right']:
            return {'success': False, 'error': f'Invalid direction: {direction}'}
            
        command = {
            'type': 'ptz_move',
            'direction': direction,
            'speed': speed,
            'duration': duration,
            'timestamp': datetime.now()
        }
        
        return await self._execute_command(command)
        
    async def ptz_stop(self) -> Dict[str, Any]:
        """异步停止PTZ"""
        command = {
            'type': 'ptz_stop',
            'timestamp': datetime.now()
        }
        
        return await self._execute_command(command)
        
    async def get_power_status(self, device: Optional[str] = None) -> Dict[str, Any]:
        """异步获取电源状态"""
        # 检查缓存
        cache_key = f'power_status_{device or "all"}'
        cached = await self._get_cached_status(cache_key)
        if cached:
            return cached
            
        try:
            async with self.session.get(
                f"{self.base_url}/status/do",
                params={"channel": self.channel_id}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    do_data = data.get('data', {})
                    
                    if device:
                        # 特定设备状态
                        if device in self.do_config:
                            do_index = self.do_config[device]
                            status = do_data.get(f'DO{do_index}', 0)
                            result = {
                                'success': True,
                                'device': device,
                                'status': 'on' if status == 1 else 'off'
                            }
                        else:
                            result = {'success': False, 'error': f'Unknown device: {device}'}
                    else:
                        # 所有设备状态
                        device_status = {}
                        for dev, idx in self.do_config.items():
                            status = do_data.get(f'DO{idx}', 0)
                            device_status[dev] = 'on' if status == 1 else 'off'
                        result = {'success': True, 'devices': device_status}
                        
                    # 更新缓存
                    await self._update_cache(cache_key, result)
                    return result
                else:
                    return {'success': False, 'error': f'HTTP {response.status}'}
                    
        except Exception as e:
            self.logger.error(f"Failed to get power status: {e}")
            return {'success': False, 'error': str(e)}
            
    async def get_ptz_position(self) -> Dict[str, Any]:
        """异步获取PTZ位置"""
        # 检查缓存
        cache_key = 'ptz_position'
        cached = await self._get_cached_status(cache_key)
        if cached:
            return cached
            
        try:
            async with self.session.get(
                f"{self.base_url}/api/ptz/angle",
                params={"ptz_addr": 30, "axis": "all"}
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    result = {'success': True, 'position': data}
                    
                    # 更新缓存
                    await self._update_cache(cache_key, result)
                    return result
                else:
                    return {'success': False, 'error': f'HTTP {response.status}'}
                    
        except Exception as e:
            self.logger.error(f"Failed to get PTZ position: {e}")
            return {'success': False, 'error': str(e)}
            
    async def emergency_shutdown(self) -> Dict[str, Any]:
        """紧急关闭所有设备"""
        results = []
        
        # 并发关闭所有设备
        tasks = []
        for device in self.do_config.keys():
            tasks.append(self.power_off(device))
            
        # 同时停止PTZ
        tasks.append(self.ptz_stop())
        
        # 等待所有操作完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计结果
        success_count = sum(1 for r in results 
                          if isinstance(r, dict) and r.get('success'))
        
        return {
            'success': success_count == len(tasks),
            'message': f'Shutdown {success_count}/{len(tasks)} devices',
            'details': results
        }
        
    async def _execute_command(self, command: Dict) -> Dict[str, Any]:
        """执行硬件命令"""
        # 添加到命令队列
        await self.command_queue.put(command)
        
        # 创建结果future
        result_future = asyncio.Future()
        command['result_future'] = result_future
        
        # 等待结果
        return await result_future
        
    async def _command_processor(self, worker_id: int):
        """命令处理器"""
        self.logger.debug(f"Command processor {worker_id} started")
        
        while self.running:
            try:
                # 从队列获取命令
                command = await asyncio.wait_for(
                    self.command_queue.get(),
                    timeout=1.0
                )
                
                # 处理命令
                result = await self._process_command(command)
                
                # 设置结果
                if 'result_future' in command:
                    command['result_future'].set_result(result)
                    
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Command processor {worker_id} error: {e}")
                
                # 设置错误结果
                if 'result_future' in command:
                    command['result_future'].set_result({
                        'success': False,
                        'error': str(e)
                    })
                    
    async def _process_command(self, command: Dict) -> Dict[str, Any]:
        """处理单个命令"""
        cmd_type = command['type']
        
        try:
            if cmd_type in ['power_on', 'power_off']:
                return await self._process_power_command(command)
            elif cmd_type == 'ptz_goto':
                return await self._process_ptz_goto(command)
            elif cmd_type == 'ptz_move':
                return await self._process_ptz_move(command)
            elif cmd_type == 'ptz_stop':
                return await self._process_ptz_stop(command)
            else:
                return {'success': False, 'error': f'Unknown command type: {cmd_type}'}
                
        except Exception as e:
            self.logger.error(f"Command processing error: {e}")
            return {'success': False, 'error': str(e)}
            
    async def _process_power_command(self, command: Dict) -> Dict[str, Any]:
        """处理电源命令"""
        do_index = command['do_index']
        value = 1 if command['type'] == 'power_on' else 0
        
        async with self.session.post(
            f"{self.base_url}/control/do",
            json={
                "channel": self.channel_id,
                "DO": do_index,
                "value": value
            }
        ) as response:
            if response.status == 200:
                self.logger.info(f"{command['type']} {command['device']} (DO{do_index})")
                
                # 等待设备启动/关闭
                if command['type'] == 'power_on':
                    if command['device'] == 'lidar':
                        await asyncio.sleep(30)  # 激光雷达需要30秒
                    elif command['device'] == 'ptz':
                        await asyncio.sleep(5)   # 云台需要5秒
                        
                return {
                    'success': True,
                    'device': command['device'],
                    'status': 'on' if value == 1 else 'off'
                }
            else:
                return {'success': False, 'error': f'HTTP {response.status}'}
                
    async def _process_ptz_goto(self, command: Dict) -> Dict[str, Any]:
        """处理PTZ定位命令"""
        async with self.session.post(
            f"{self.base_url}/control/ptz/angle",
            json={
                "channel": "0000000000000000",
                "address": 30,
                "pan": command['horizontal'],
                "tilt": command['vertical'],
                "speed": command['speed']
            }
        ) as response:
            if response.status == 200:
                # 等待移动完成
                await asyncio.sleep(3)
                
                return {
                    'success': True,
                    'horizontal': command['horizontal'],
                    'vertical': command['vertical']
                }
            else:
                return {'success': False, 'error': f'HTTP {response.status}'}
                
    async def _process_ptz_move(self, command: Dict) -> Dict[str, Any]:
        """处理PTZ移动命令"""
        direction = command['direction']
        speed = command['speed']
        duration = command['duration']
        
        # 设置移动速度
        axis = "pan" if direction in ['left', 'right'] else "tilt"
        speed_value = speed if direction in ['right', 'down'] else -speed
        
        async with self.session.post(
            f"{self.base_url}/api/ptz/speed",
            json={
                "ptz_addr": 30,
                "axis": axis,
                "speed": speed_value
            }
        ) as response:
            if response.status == 200:
                # 持续移动
                await asyncio.sleep(duration)
                
                # 停止移动
                await self._process_ptz_stop({})
                
                return {
                    'success': True,
                    'direction': direction,
                    'duration': duration
                }
            else:
                return {'success': False, 'error': f'HTTP {response.status}'}
                
    async def _process_ptz_stop(self, command: Dict) -> Dict[str, Any]:
        """处理PTZ停止命令"""
        async with self.session.post(
            f"{self.base_url}/api/ptz/stop",
            json={"ptz_addr": 30, "type": "all"}
        ) as response:
            if response.status == 200:
                return {'success': True, 'status': 'stopped'}
            else:
                return {'success': False, 'error': f'HTTP {response.status}'}
                
    async def _get_cached_status(self, key: str) -> Optional[Dict]:
        """获取缓存的状态"""
        async with self.cache_lock:
            if key in self.status_cache:
                cached = self.status_cache[key]
                age = (datetime.now() - cached['timestamp']).total_seconds()
                if age < self.cache_ttl:
                    return cached['data']
        return None
        
    async def _update_cache(self, key: str, data: Dict):
        """更新状态缓存"""
        async with self.cache_lock:
            self.status_cache[key] = {
                'data': data,
                'timestamp': datetime.now()
            }
            
    async def _status_updater(self):
        """定期更新状态缓存"""
        while self.running:
            try:
                # 更新电源状态
                await self.get_power_status()
                
                # 更新PTZ位置
                await self.get_ptz_position()
                
                # 等待下次更新
                await asyncio.sleep(self.cache_ttl)
                
            except Exception as e:
                self.logger.error(f"Status updater error: {e}")
                await asyncio.sleep(1)
                
    async def get_statistics(self) -> Dict[str, Any]:
        """获取控制器统计信息"""
        return {
            'queue_size': self.command_queue.qsize(),
            'cache_size': len(self.status_cache),
            'max_concurrent_commands': self.max_concurrent_commands
        }