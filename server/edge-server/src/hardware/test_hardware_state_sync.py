#!/usr/bin/env python3
"""
硬件状态同步测试脚本
验证硬件状态采集、上报和控制命令处理功能
"""

import asyncio
import aiohttp
import json
import logging
import sys
import time
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HardwareStateSyncTester:
    """硬件状态同步测试器"""
    
    def __init__(self, base_url: str = "http://localhost:7080", rpc_url: str = "http://localhost:8090"):
        self.base_url = base_url
        self.rpc_url = rpc_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_hardware_api(self):
        """测试硬件控制API"""
        print("\n=== 测试硬件控制API ===")
        
        # 1. 检查健康状态
        try:
            async with self.session.get(f"{self.base_url}/health") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✓ 硬件服务健康检查: {data}")
                else:
                    print(f"✗ 硬件服务健康检查失败: {resp.status}")
        except Exception as e:
            print(f"✗ 无法连接硬件服务: {e}")
            return False
        
        # 2. 查询IO状态
        try:
            async with self.session.get(f"{self.base_url}/api/io/status") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✓ IO状态查询成功:")
                    print(f"  DO状态: {data.get('do_status', [])}")
                else:
                    print(f"✗ IO状态查询失败: {resp.status}")
        except Exception as e:
            print(f"✗ IO状态查询错误: {e}")
        
        return True
    
    async def test_state_api(self):
        """测试状态管理API"""
        print("\n=== 测试状态管理API ===")
        
        # 1. 获取完整状态
        try:
            async with self.session.get(f"{self.base_url}/api/state/full") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    if data.get('success'):
                        state = data.get('data', {})
                        print(f"✓ 完整状态获取成功:")
                        print(f"  IO状态数: {len(state.get('io_states', {}))}")
                        print(f"  PTZ状态数: {len(state.get('ptz_states', {}))}")
                        print(f"  业务状态: {state.get('business_state', {})}")
                else:
                    print(f"✗ 完整状态获取失败: {resp.status}")
        except Exception as e:
            print(f"✗ 状态API未就绪: {e}")
            return False
        
        # 2. 获取IO状态
        try:
            async with self.session.get(f"{self.base_url}/api/state/io") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    if data.get('success'):
                        io_states = data.get('data', {})
                        print(f"✓ IO状态详情:")
                        for channel, state in io_states.items():
                            print(f"  DO{channel}: {state.get('state')} - {state.get('device')}")
                else:
                    print(f"✗ IO状态获取失败: {resp.status}")
        except Exception as e:
            print(f"✗ IO状态获取错误: {e}")
        
        return True
    
    async def test_rpc_api(self):
        """测试RPC API"""
        print("\n=== 测试RPC API ===")
        
        # 1. 健康检查
        try:
            async with self.session.get(f"{self.rpc_url}/health") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✓ RPC服务健康检查: {data}")
                else:
                    print(f"✗ RPC服务健康检查失败: {resp.status}")
        except Exception as e:
            print(f"✗ 无法连接RPC服务: {e}")
            return False
        
        # 2. 测试RPC调用 - 查询IO状态
        rpc_request = {
            "jsonrpc": "2.0",
            "method": "io.status",
            "params": {},
            "id": 1
        }
        
        try:
            async with self.session.post(f"{self.rpc_url}/rpc", json=rpc_request) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    if 'result' in data:
                        result = data['result']
                        print(f"✓ RPC IO状态查询成功:")
                        print(f"  缓存状态: {result.get('cached_status', {})}")
                    elif 'error' in data:
                        print(f"✗ RPC错误: {data['error']}")
                else:
                    print(f"✗ RPC请求失败: {resp.status}")
        except Exception as e:
            print(f"✗ RPC调用错误: {e}")
        
        return True
    
    async def test_state_change(self):
        """测试状态变化检测"""
        print("\n=== 测试状态变化检测 ===")
        
        # 1. 获取初始状态
        initial_state = None
        try:
            async with self.session.get(f"{self.base_url}/api/state/full") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    initial_state = data.get('data', {})
                    print("✓ 获取初始状态成功")
        except Exception as e:
            print(f"✗ 获取初始状态失败: {e}")
            return False
        
        # 2. 控制DO1开启
        print("\n控制DO1开启...")
        control_data = {
            "do_index": 1,
            "status": 1
        }
        
        try:
            async with self.session.post(f"{self.base_url}/api/io/control", json=control_data) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    if data.get('success'):
                        print(f"✓ DO1开启成功: {data.get('message')}")
                    else:
                        print(f"✗ DO1开启失败: {data.get('error')}")
                else:
                    print(f"✗ 控制请求失败: {resp.status}")
        except Exception as e:
            print(f"✗ 控制请求错误: {e}")
            return False
        
        # 3. 等待状态更新
        print("\n等待5秒让状态更新...")
        await asyncio.sleep(5)
        
        # 4. 获取新状态并比较
        try:
            async with self.session.get(f"{self.base_url}/api/state/full") as resp:
                if resp.status == 200:
                    data = await resp.json()
                    new_state = data.get('data', {})
                    
                    # 比较状态变化
                    old_do1 = initial_state.get('io_states', {}).get('1', {}).get('state')
                    new_do1 = new_state.get('io_states', {}).get('1', {}).get('state')
                    
                    if old_do1 != new_do1:
                        print(f"✓ 检测到状态变化: DO1 从 {old_do1} 变为 {new_do1}")
                    else:
                        print(f"✗ 未检测到状态变化: DO1 仍为 {old_do1}")
        except Exception as e:
            print(f"✗ 获取新状态失败: {e}")
        
        # 5. 关闭DO1
        print("\n控制DO1关闭...")
        control_data['status'] = 0
        
        try:
            async with self.session.post(f"{self.base_url}/api/io/control", json=control_data) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    if data.get('success'):
                        print(f"✓ DO1关闭成功")
        except Exception as e:
            print(f"✗ 关闭DO1失败: {e}")
        
        return True
    
    async def test_control_commands(self):
        """测试控制命令处理"""
        print("\n=== 测试控制命令处理 ===")
        
        # 1. 测试IO控制命令
        rpc_request = {
            "jsonrpc": "2.0",
            "method": "io.control",
            "params": {
                "channel": 3,
                "state": True
            },
            "id": 2
        }
        
        try:
            async with self.session.post(f"{self.rpc_url}/rpc", json=rpc_request) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    if 'result' in data:
                        result = data['result']
                        print(f"✓ RPC IO控制成功: {result}")
                    else:
                        print(f"✗ RPC IO控制失败: {data}")
        except Exception as e:
            print(f"✗ RPC IO控制错误: {e}")
        
        # 2. 测试批量请求
        batch_request = [
            {
                "jsonrpc": "2.0",
                "method": "io.status",
                "params": {},
                "id": 3
            },
            {
                "jsonrpc": "2.0",
                "method": "device.getStatus",
                "params": {},
                "id": 4
            }
        ]
        
        try:
            async with self.session.post(f"{self.rpc_url}/rpc", json=batch_request) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✓ 批量RPC请求成功，收到 {len(data)} 个响应")
                    for response in data:
                        if 'result' in response:
                            print(f"  - 请求{response.get('id')}成功")
                        else:
                            print(f"  - 请求{response.get('id')}失败: {response.get('error')}")
        except Exception as e:
            print(f"✗ 批量RPC请求错误: {e}")
        
        return True
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("=" * 60)
        print("硬件状态同步功能测试")
        print("=" * 60)
        
        # 测试硬件API
        if not await self.test_hardware_api():
            print("\n硬件服务未就绪，请先启动硬件控制服务")
            return
        
        # 测试状态API
        await self.test_state_api()
        
        # 测试RPC API
        if not await self.test_rpc_api():
            print("\nRPC服务未就绪，请先启动状态同步服务")
            return
        
        # 测试状态变化
        await self.test_state_change()
        
        # 测试控制命令
        await self.test_control_commands()
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)


async def main():
    """主函数"""
    # 可以通过参数指定服务地址
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:7080"
    rpc_url = sys.argv[2] if len(sys.argv) > 2 else "http://localhost:8090"
    
    async with HardwareStateSyncTester(base_url, rpc_url) as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())