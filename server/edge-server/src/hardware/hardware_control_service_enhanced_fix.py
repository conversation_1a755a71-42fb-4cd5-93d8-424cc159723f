"""
硬件控制服务增强修复
修复DO查询超时和业务流程问题
"""

import asyncio
import logging
import time
import binascii
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

# ==================== DO查询优化 ====================

class OptimizedRelayService:
    """优化的继电器服务"""
    
    def __init__(self, original_service):
        self.original = original_service
        self.last_query_time = 0
        self.query_interval = 5.0  # 最小查询间隔
        self.cached_status = [0] * 6
        self.cache_valid = False
        
    async def query_do_status_optimized(self, slave_addr: int):
        """优化的DO状态查询"""
        current_time = time.time()
        
        # 1. 如果距离上次查询不到5秒，直接返回缓存
        if self.cache_valid and (current_time - self.last_query_time) < self.query_interval:
            logger.debug(f"使用缓存DO状态（{current_time - self.last_query_time:.1f}秒前查询）")
            return True, {
                "slave_addr": slave_addr,
                "do_status": self.cached_status.copy(),
                "message": "缓存状态",
                "cached": True
            }
        
        # 2. 异步查询，不阻塞
        asyncio.create_task(self._async_query_update(slave_addr))
        
        # 3. 立即返回当前缓存
        return True, {
            "slave_addr": slave_addr, 
            "do_status": self.cached_status.copy(),
            "message": "使用缓存，后台更新中",
            "cached": True
        }
    
    async def _async_query_update(self, slave_addr: int):
        """后台异步更新DO状态"""
        try:
            # 构建查询命令
            cmd = self.original.netty_server.ModbusUtils.build_do_query_command(slave_addr)
            hex_cmd = binascii.hexlify(cmd).decode()
            
            # 单次查询，短超时
            response = await self.original.netty_server.send_command_with_response(
                self.original.io_channel, hex_cmd, timeout=0.5
            )
            
            if response:
                # 解析响应
                do_status = self._parse_do_response(response, slave_addr)
                if do_status is not None:
                    self.cached_status = do_status
                    self.cache_valid = True
                    self.last_query_time = time.time()
                    logger.debug(f"DO状态更新成功: {do_status}")
                    
                    # 更新原始缓存
                    async with self.original.cache_lock:
                        for i in range(6):
                            key = f"{slave_addr}:{i+1}"
                            self.original.do_status_cache[key] = do_status[i]
            else:
                logger.debug("DO状态查询无响应，保持缓存")
                
        except Exception as e:
            logger.debug(f"后台DO查询异常: {e}")
    
    def _parse_do_response(self, response: bytes, slave_addr: int) -> Optional[list]:
        """解析DO响应"""
        try:
            # DI上报格式
            if len(response) >= 7 and response[0] == 0x40 and response[1] == 0x57:
                if response[2] == slave_addr and response[3] == 0x00:
                    status_byte = response[4]
                    return [(status_byte >> i) & 0x01 for i in range(6)]
            
            # 标准Modbus格式
            elif len(response) >= 6 and response[0] == slave_addr and response[1] == 0x01:
                status_byte = response[3] if len(response) > 3 else 0
                return [(status_byte >> i) & 0x01 for i in range(6)]
                
        except Exception as e:
            logger.debug(f"解析DO响应失败: {e}")
        
        return None


# ==================== 业务流程优化 ====================

class EnhancedBusinessManager:
    """增强的业务管理器"""
    
    def __init__(self, hardware_manager):
        self.hw = hardware_manager
        self.business_tasks = {}  # 业务任务跟踪
        self.MAX_BUSINESS_DURATION = 1200  # 20分钟
        
    async def start_business_task(self, task_id: str, task_type: str, 
                                 auto_shutdown: bool = True) -> bool:
        """启动业务任务"""
        logger.info(f"启动业务任务: {task_id} ({task_type})")
        
        # 记录任务
        self.business_tasks[task_id] = {
            'type': task_type,
            'start_time': time.time(),
            'auto_shutdown': auto_shutdown,
            'devices': []
        }
        
        # 激活业务状态
        await self.hw.set_business_active(True, task_type)
        
        return True
    
    async def end_business_task(self, task_id: str) -> bool:
        """结束业务任务"""
        if task_id not in self.business_tasks:
            logger.warning(f"未知的业务任务: {task_id}")
            return False
        
        task = self.business_tasks[task_id]
        duration = time.time() - task['start_time']
        logger.info(f"结束业务任务: {task_id}，持续{duration/60:.1f}分钟")
        
        # 如果配置了自动关闭，关闭相关设备
        if task['auto_shutdown']:
            await self._shutdown_task_devices(task)
        
        # 清理任务
        del self.business_tasks[task_id]
        
        # 如果没有其他活跃任务，结束业务状态
        if not self.business_tasks:
            await self.hw.set_business_active(False, task['type'])
            # 关闭所有关键设备
            await self._ensure_devices_off()
        
        return True
    
    async def _shutdown_task_devices(self, task: Dict):
        """关闭任务相关设备"""
        logger.info("执行任务设备关闭...")
        
        # 关闭记录的设备
        for device in task.get('devices', []):
            if device['type'] == 'do':
                await self.hw.relay_service.control_do(
                    device['addr'], device['index'], 0
                )
    
    async def _ensure_devices_off(self):
        """确保关键设备关闭"""
        logger.info("确保所有关键设备关闭...")
        
        # 关闭激光雷达和云台
        for do_index in [1, 2]:  # DO1=激光雷达, DO2=云台
            success, msg = await self.hw.relay_service.control_do(1, do_index, 0)
            if success:
                device_name = "激光雷达" if do_index == 1 else "云台"
                logger.info(f"✓ {device_name}已关闭")
    
    async def check_business_timeout(self):
        """检查业务超时"""
        current_time = time.time()
        timeout_tasks = []
        
        for task_id, task in self.business_tasks.items():
            duration = current_time - task['start_time']
            if duration > self.MAX_BUSINESS_DURATION:
                logger.warning(f"业务任务 {task_id} 超时（{duration/60:.1f}分钟）")
                timeout_tasks.append(task_id)
        
        # 结束超时任务
        for task_id in timeout_tasks:
            await self.end_business_task(task_id)


# ==================== 保护机制增强 ====================

class EnhancedProtectionMechanism:
    """增强的保护机制"""
    
    def __init__(self, hardware_manager, business_manager):
        self.hw = hardware_manager
        self.bm = business_manager
        
    async def protection_check_enhanced(self):
        """增强的保护检查"""
        # 1. 检查业务超时
        await self.bm.check_business_timeout()
        
        # 2. 获取可靠的DO状态（使用优化查询）
        do_status = await self._get_reliable_status()
        
        # 3. 检查设备通电时间
        current_time = time.time()
        
        # 根据业务状态选择保护时限
        if self.hw.business_active:
            max_time = 1800  # 业务期间30分钟
            logger.debug(f"业务模式保护检查（限制{max_time/60}分钟）")
        else:
            max_time = 600  # 无业务10分钟（生产环境）
            logger.debug(f"正常模式保护检查（限制{max_time/60}分钟）")
        
        # 检查关键设备
        devices_to_shutdown = []
        for do_index in [1, 2]:  # 激光雷达和云台
            if do_index <= len(do_status) and do_status[do_index - 1] == 1:
                # 设备通电中
                if do_index not in self.hw.device_power_on_times:
                    self.hw.device_power_on_times[do_index] = current_time
                    device_name = "激光雷达" if do_index == 1 else "云台"
                    logger.info(f"{device_name}(DO{do_index}) 检测到通电")
                else:
                    duration = current_time - self.hw.device_power_on_times[do_index]
                    if duration > max_time:
                        device_name = "激光雷达" if do_index == 1 else "云台"
                        devices_to_shutdown.append((do_index, device_name, duration))
        
        # 执行保护性断电
        if devices_to_shutdown:
            logger.warning(f"触发保护机制，关闭{len(devices_to_shutdown)}个设备")
            for do_index, name, duration in devices_to_shutdown:
                await self._safe_shutdown_device(do_index, name, duration)
    
    async def _get_reliable_status(self) -> list:
        """获取可靠的设备状态"""
        # 从缓存获取
        status = [0] * 6
        async with self.hw.relay_service.cache_lock:
            for i in range(6):
                key = f"1:{i+1}"
                status[i] = self.hw.relay_service.do_status_cache.get(key, 0)
        return status
    
    async def _safe_shutdown_device(self, do_index: int, name: str, duration: float):
        """安全关闭设备"""
        # 如果是云台，先归零
        if do_index == 2:
            logger.info("云台归零后关闭...")
            await self.hw.ptz_service.move_to_angle(30, 0, 0)
            await asyncio.sleep(2)
        
        # 关闭设备
        success, msg = await self.hw.relay_service.control_do(1, do_index, 0)
        if success:
            logger.info(f"✓ {name}保护性断电成功（运行{duration/60:.1f}分钟）")
            if do_index in self.hw.device_power_on_times:
                del self.hw.device_power_on_times[do_index]


# ==================== 应用修复 ====================

def apply_enhanced_fixes(hardware_manager, relay_service):
    """应用增强修复"""
    logger.info("应用增强修复...")
    
    # 1. 优化DO查询
    optimized_relay = OptimizedRelayService(relay_service)
    relay_service.query_do_status = optimized_relay.query_do_status_optimized
    
    # 2. 增强业务管理
    business_manager = EnhancedBusinessManager(hardware_manager)
    hardware_manager.business_manager = business_manager
    
    # 3. 增强保护机制
    protection = EnhancedProtectionMechanism(hardware_manager, business_manager)
    hardware_manager._check_and_protect_devices = protection.protection_check_enhanced
    
    # 4. 调整时限
    hardware_manager.MAX_BUSINESS_DURATION = 1200  # 20分钟
    
    logger.info("增强修复应用完成：")
    logger.info("- DO查询异步化，不再阻塞")
    logger.info("- 业务最长20分钟")
    logger.info("- 任务完成自动关闭设备")
    logger.info("- 保护机制更可靠")
    
    return optimized_relay, business_manager, protection


if __name__ == "__main__":
    print("硬件控制服务增强修复")
    print("1. DO查询异步化，避免超时阻塞")
    print("2. 业务时限调整为20分钟")
    print("3. 任务完成自动关闭设备")
    print("4. 云台归零后再断电")