"""
串口通信优化器
提供重试机制、动态超时和连接池管理
"""

import asyncio
import logging
import time
from typing import Optional, Dict, List, Tuple
from collections import deque
from datetime import datetime

logger = logging.getLogger(__name__)


class SerialResponseTracker:
    """串口响应时间跟踪器"""
    
    def __init__(self, window_size: int = 20):
        self.response_times = deque(maxlen=window_size)
        self.error_count = 0
        self.success_count = 0
        
    def record_success(self, response_time: float):
        """记录成功响应"""
        self.response_times.append(response_time)
        self.success_count += 1
        
    def record_error(self):
        """记录错误"""
        self.error_count += 1
        
    def get_dynamic_timeout(self, base_timeout: float = 2.0) -> float:
        """获取动态超时时间"""
        if not self.response_times:
            return base_timeout
            
        # 计算平均响应时间
        avg_time = sum(self.response_times) / len(self.response_times)
        
        # 计算标准差
        variance = sum((t - avg_time) ** 2 for t in self.response_times) / len(self.response_times)
        std_dev = variance ** 0.5
        
        # 动态超时 = 平均值 + 3倍标准差
        dynamic_timeout = avg_time + (3 * std_dev)
        
        # 限制在合理范围内
        return max(0.5, min(dynamic_timeout, base_timeout * 2))
        
    def get_health_score(self) -> float:
        """获取健康分数 (0-1)"""
        total = self.success_count + self.error_count
        if total == 0:
            return 1.0
        return self.success_count / total


class SerialCommunicationOptimizer:
    """串口通信优化器"""
    
    def __init__(self, netty_server):
        self.netty_server = netty_server
        self.channel_trackers: Dict[str, SerialResponseTracker] = {}
        self.retry_config = {
            'max_retries': 3,
            'initial_delay': 0.1,
            'backoff_factor': 2.0,
            'max_delay': 2.0
        }
        
    def get_tracker(self, channel: str) -> SerialResponseTracker:
        """获取通道跟踪器"""
        if channel not in self.channel_trackers:
            self.channel_trackers[channel] = SerialResponseTracker()
        return self.channel_trackers[channel]
        
    async def send_command_with_retry(self, channel: str, hex_command: str, 
                                    max_retries: Optional[int] = None) -> Tuple[bool, Optional[bytes]]:
        """发送命令并支持重试"""
        if max_retries is None:
            max_retries = self.retry_config['max_retries']
            
        tracker = self.get_tracker(channel)
        last_error = None
        
        for attempt in range(max_retries + 1):
            try:
                # 获取动态超时
                timeout = tracker.get_dynamic_timeout()
                logger.debug(f"尝试 {attempt + 1}/{max_retries + 1}, 超时: {timeout:.2f}秒")
                
                # 发送命令
                start_time = time.time()
                response = await self.netty_server.send_command_with_response(
                    channel, hex_command, timeout=timeout
                )
                
                if response:
                    # 记录成功响应时间
                    response_time = time.time() - start_time
                    tracker.record_success(response_time)
                    logger.info(f"命令成功，响应时间: {response_time:.3f}秒")
                    return True, response
                    
                # 没有响应，但也没有异常
                tracker.record_error()
                last_error = "No response received"
                
            except asyncio.TimeoutError:
                tracker.record_error()
                last_error = f"Timeout after {timeout:.2f}s"
                logger.warning(f"第 {attempt + 1} 次尝试超时")
                
            except Exception as e:
                tracker.record_error()
                last_error = str(e)
                logger.error(f"第 {attempt + 1} 次尝试失败: {e}")
                
            # 计算重试延迟
            if attempt < max_retries:
                delay = min(
                    self.retry_config['initial_delay'] * (self.retry_config['backoff_factor'] ** attempt),
                    self.retry_config['max_delay']
                )
                logger.info(f"等待 {delay:.2f} 秒后重试...")
                await asyncio.sleep(delay)
                
        # 所有重试都失败
        logger.error(f"所有重试都失败: {last_error}")
        return False, None
        
    async def batch_send_commands(self, commands: List[Tuple[str, str]]) -> List[Tuple[bool, Optional[bytes]]]:
        """批量发送命令"""
        tasks = []
        for channel, hex_command in commands:
            task = self.send_command_with_retry(channel, hex_command)
            tasks.append(task)
            
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        final_results = []
        for result in results:
            if isinstance(result, Exception):
                final_results.append((False, None))
            else:
                final_results.append(result)
                
        return final_results
        
    def get_channel_health(self, channel: str) -> Dict:
        """获取通道健康状态"""
        tracker = self.get_tracker(channel)
        
        return {
            'channel': channel,
            'health_score': tracker.get_health_score(),
            'success_count': tracker.success_count,
            'error_count': tracker.error_count,
            'avg_response_time': sum(tracker.response_times) / len(tracker.response_times) 
                                if tracker.response_times else 0,
            'dynamic_timeout': tracker.get_dynamic_timeout()
        }
        
    def get_all_channels_health(self) -> List[Dict]:
        """获取所有通道健康状态"""
        return [self.get_channel_health(channel) for channel in self.channel_trackers]


class OptimizedRelayService:
    """优化的继电器服务"""
    
    def __init__(self, netty_server):
        self.netty_server = netty_server
        self.optimizer = SerialCommunicationOptimizer(netty_server)
        self.io_channel = None
        
    async def control_do_optimized(self, slave_addr: int, do_index: int, 
                                  status: int) -> Tuple[bool, str]:
        """优化的DO控制"""
        if not 1 <= do_index <= 6:
            return False, f"DO索引必须在1-6之间"
            
        # 构建命令
        from .hardware_control_service import ModbusUtils
        cmd = ModbusUtils.build_do_command(slave_addr, do_index-1, status)
        hex_cmd = cmd.hex()
        
        action = "打开" if status == 1 else "关闭"
        logger.info(f"{action}DO{do_index}: 使用优化通信")
        
        # 使用优化的发送方法
        success, response = await self.optimizer.send_command_with_retry(
            self.io_channel, hex_cmd
        )
        
        if success:
            return True, f"DO{do_index}{action}成功"
        else:
            return False, f"DO{do_index}{action}失败（重试后）"
            
    async def query_do_status_optimized(self, slave_addr: int) -> Tuple[bool, Dict]:
        """优化的DO状态查询"""
        from .hardware_control_service import ModbusUtils
        cmd = ModbusUtils.build_do_query_command(slave_addr)
        hex_cmd = cmd.hex()
        
        logger.info(f"查询DO状态: 使用优化通信")
        
        # 使用更少的重试次数进行状态查询
        success, response = await self.optimizer.send_command_with_retry(
            self.io_channel, hex_cmd, max_retries=2
        )
        
        if success and response:
            # 解析响应（复用原有逻辑）
            # ... 解析代码 ...
            return True, {"status": "received", "raw": response.hex()}
        else:
            # 返回缓存或默认状态
            return False, {
                "status": "timeout",
                "do_status": [0, 0, 0, 0, 0, 0],
                "message": "查询超时，返回默认状态"
            }