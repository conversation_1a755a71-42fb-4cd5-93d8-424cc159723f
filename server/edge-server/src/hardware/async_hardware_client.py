"""
异步硬件控制客户端
优化网络请求性能
"""

import asyncio
import aiohttp
import logging
from typing import Dict, Optional, Any, List, Tuple
from datetime import datetime
import json

logger = logging.getLogger(__name__)

class AsyncHardwareClient:
    """异步硬件控制客户端"""
    
    def __init__(self, base_url: str = "http://localhost:7080", timeout: float = 30.0):
        self.base_url = base_url.rstrip('/')
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        self.session: Optional[aiohttp.ClientSession] = None
        self._lock = asyncio.Lock()
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.disconnect()
        
    async def connect(self):
        """建立连接"""
        if not self.session:
            self.session = aiohttp.ClientSession(timeout=self.timeout)
            logger.info("AsyncHardwareClient connected")
            
    async def disconnect(self):
        """断开连接"""
        if self.session:
            await self.session.close()
            self.session = None
            logger.info("AsyncHardwareClient disconnected")
            
    async def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """执行HTTP请求"""
        if not self.session:
            await self.connect()
            
        url = f"{self.base_url}{endpoint}"
        
        try:
            async with self.session.request(method, url, **kwargs) as response:
                response.raise_for_status()
                return await response.json()
        except aiohttp.ClientError as e:
            logger.error(f"Request failed: {method} {url} - {e}")
            raise
            
    # DO控制相关方法
    async def set_do(self, channel: int, status: int) -> Dict[str, Any]:
        """设置DO通道状态"""
        return await self._request(
            'POST',
            '/do/control',
            json={'channel': channel, 'status': status}
        )
        
    async def set_multiple_do(self, channels: List[Tuple[int, int]]) -> Dict[str, Any]:
        """批量设置DO通道"""
        # 并发执行多个请求
        tasks = [
            self.set_do(channel, status)
            for channel, status in channels
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        success_count = sum(1 for r in results if not isinstance(r, Exception))
        return {
            'success': success_count == len(channels),
            'results': results,
            'success_count': success_count,
            'total': len(channels)
        }
        
    async def get_do_status(self) -> Dict[str, Any]:
        """获取所有DO状态"""
        return await self._request('GET', '/do/status')
        
    async def emergency_stop(self) -> Dict[str, Any]:
        """紧急停止所有设备"""
        return await self._request('POST', '/emergency/stop')
        
    # PTZ控制相关方法
    async def move_ptz_to(self, ptz_type: str, position: float,
                         speed: int = 1000) -> Dict[str, Any]:
        """移动PTZ到指定位置"""
        return await self._request(
            'POST',
            '/ptz/move',
            json={
                'type': ptz_type,
                'position': position,
                'speed': speed
            }
        )
        
    async def get_ptz_position(self, ptz_type: str) -> Dict[str, Any]:
        """获取PTZ当前位置"""
        return await self._request('GET', f'/ptz/position/{ptz_type}')
        
    async def search_ptz_datum(self, ptz_type: str) -> Dict[str, Any]:
        """搜索PTZ基准点"""
        return await self._request('POST', f'/ptz/search/{ptz_type}')
        
    async def stop_ptz(self, ptz_type: str) -> Dict[str, Any]:
        """停止PTZ运动"""
        return await self._request('POST', f'/ptz/stop/{ptz_type}')
        
    # 组合操作
    async def move_to_scan_position(self, horizontal: float, vertical: float,
                                   wait: bool = True) -> Dict[str, Any]:
        """移动到扫描位置（并发执行）"""
        # 并发移动两个轴
        tasks = [
            self.move_ptz_to('PAN', horizontal),
            self.move_ptz_to('TILT', vertical)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 检查结果
        success = all(not isinstance(r, Exception) for r in results)
        
        if success and wait:
            # 等待移动完成
            await self.wait_for_position(horizontal, vertical)
            
        return {
            'success': success,
            'pan_result': results[0] if not isinstance(results[0], Exception) else str(results[0]),
            'tilt_result': results[1] if not isinstance(results[1], Exception) else str(results[1])
        }
        
    async def wait_for_position(self, target_horizontal: float, 
                               target_vertical: float,
                               tolerance: float = 0.1,
                               timeout: float = 30.0) -> bool:
        """等待PTZ到达目标位置"""
        start_time = asyncio.get_event_loop().time()
        
        while (asyncio.get_event_loop().time() - start_time) < timeout:
            # 并发获取两个轴的位置
            try:
                pan_pos, tilt_pos = await asyncio.gather(
                    self.get_ptz_position('PAN'),
                    self.get_ptz_position('TILT')
                )
                
                current_h = pan_pos.get('position', 0)
                current_v = tilt_pos.get('position', 0)
                
                # 检查是否到达目标位置
                if (abs(current_h - target_horizontal) <= tolerance and
                    abs(current_v - target_vertical) <= tolerance):
                    return True
                    
            except Exception as e:
                logger.error(f"Error checking position: {e}")
                
            # 短暂等待
            await asyncio.sleep(0.1)
            
        return False
        
    # 健康检查
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return await self._request('GET', '/health')
        
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        # 并发获取多个状态
        tasks = {
            'health': self.health_check(),
            'do_status': self.get_do_status(),
            'pan_position': self.get_ptz_position('PAN'),
            'tilt_position': self.get_ptz_position('TILT')
        }
        
        results = {}
        for name, task in tasks.items():
            try:
                results[name] = await task
            except Exception as e:
                results[name] = {'error': str(e)}
                
        return results
        
    # 批量操作优化
    async def execute_scan_sequence(self, positions: List[Dict[str, float]],
                                   scan_duration: float = 5.0) -> List[Dict[str, Any]]:
        """执行扫描序列"""
        results = []
        
        for i, pos in enumerate(positions):
            logger.info(f"Scanning position {i+1}/{len(positions)}: {pos}")
            
            # 移动到位置
            move_result = await self.move_to_scan_position(
                pos['horizontal'],
                pos['vertical'],
                wait=True
            )
            
            if not move_result['success']:
                results.append({
                    'position': pos,
                    'success': False,
                    'error': 'Failed to move to position'
                })
                continue
                
            # 开启激光雷达
            await self.set_do(1, 1)  # DO1 - LiDAR power
            
            # 等待扫描
            await asyncio.sleep(scan_duration)
            
            # 关闭激光雷达
            await self.set_do(1, 0)
            
            results.append({
                'position': pos,
                'success': True,
                'scan_duration': scan_duration
            })
            
        return results


class AsyncHardwarePool:
    """异步硬件客户端连接池"""
    
    def __init__(self, base_url: str = "http://localhost:7080",
                 pool_size: int = 5, timeout: float = 30.0):
        self.base_url = base_url
        self.pool_size = pool_size
        self.timeout = timeout
        self.clients: List[AsyncHardwareClient] = []
        self.available = asyncio.Queue(maxsize=pool_size)
        self._initialized = False
        
    async def initialize(self):
        """初始化连接池"""
        if self._initialized:
            return
            
        # 创建客户端
        for _ in range(self.pool_size):
            client = AsyncHardwareClient(self.base_url, self.timeout)
            await client.connect()
            self.clients.append(client)
            await self.available.put(client)
            
        self._initialized = True
        logger.info(f"Hardware client pool initialized with {self.pool_size} clients")
        
    async def acquire(self) -> AsyncHardwareClient:
        """获取客户端"""
        if not self._initialized:
            await self.initialize()
        return await self.available.get()
        
    async def release(self, client: AsyncHardwareClient):
        """释放客户端"""
        await self.available.put(client)
        
    async def close(self):
        """关闭连接池"""
        # 等待所有客户端返回
        while not self.available.empty():
            client = await self.available.get()
            await client.disconnect()
            
        self._initialized = False
        logger.info("Hardware client pool closed")
        
    async def execute(self, func_name: str, *args, **kwargs) -> Any:
        """执行操作"""
        client = await self.acquire()
        try:
            func = getattr(client, func_name)
            return await func(*args, **kwargs)
        finally:
            await self.release(client)