"""
硬件控制API适配器
解决API参数不匹配问题，提供兼容层
"""

import logging
from aiohttp import web
import json

logger = logging.getLogger(__name__)


class HardwareAPIAdapter:
    """硬件控制API适配器"""
    
    def __init__(self, hardware_api_handler):
        self.handler = hardware_api_handler
        
    async def io_control_adapter(self, request):
        """IO控制适配器 - 兼容不同的参数格式"""
        try:
            data = await request.json()
            
            # 参数映射
            # 支持的参数格式：
            # 1. 新格式: {"do_index": 1, "status": 1}
            # 2. 旧格式: {"port": 1, "action": "on"}
            
            # 检查是否是旧格式
            if "port" in data and "action" in data:
                # 转换旧格式到新格式
                do_index = data["port"]
                action = data["action"]
                status = 1 if action == "on" else 0
                
                # 构建新格式请求
                new_data = {
                    "slave_addr": data.get("slave_addr", 1),
                    "do_index": do_index,
                    "status": status
                }
                
                logger.info(f"参数适配: 旧格式 {data} -> 新格式 {new_data}")
                
                # 创建新的请求对象
                request._payload = json.dumps(new_data).encode()
                
            # 调用原始处理函数
            return await self.handler.io_control(request)
            
        except Exception as e:
            logger.error(f"IO控制适配器错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
            
    async def io_status_adapter(self, request):
        """IO状态查询适配器"""
        # 直接转发，因为查询接口参数格式相同
        return await self.handler.io_status(request)
        
    async def ptz_stop_adapter(self, request):
        """PTZ停止适配器 - 处理JSON解析错误"""
        try:
            # 尝试读取JSON数据
            try:
                data = await request.json()
            except:
                # 如果JSON解析失败，使用默认值
                data = {"type": "all", "ptz_addr": 30}
                logger.warning("PTZ停止请求JSON解析失败，使用默认值")
            
            # 创建新的请求对象
            request._payload = json.dumps(data).encode()
            
            # 调用原始处理函数
            return await self.handler.ptz_stop(request)
            
        except Exception as e:
            logger.error(f"PTZ停止适配器错误: {e}")
            return web.json_response({"error": str(e)}, status=500)


def setup_adapter_routes(app, hardware_api_handler):
    """设置适配器路由"""
    adapter = HardwareAPIAdapter(hardware_api_handler)
    
    # 添加兼容路由
    app.router.add_post('/api/io/control', adapter.io_control_adapter)
    app.router.add_get('/api/io/status', adapter.io_status_adapter)
    app.router.add_post('/api/ptz/stop', adapter.ptz_stop_adapter)
    
    logger.info("硬件控制API适配器路由已设置")