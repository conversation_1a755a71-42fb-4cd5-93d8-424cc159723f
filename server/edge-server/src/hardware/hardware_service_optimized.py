"""
优化的硬件控制服务
集成连接池、重试机制、健康检查等高级特性
"""

import asyncio
import logging
from typing import Dict, Optional, Tuple, List
from datetime import datetime
import aiohttp
from fastapi import FastAPI, HTTPException
from fastapi.responses import JSONResponse
import uvicorn

from .connection_pool import Serial<PERSON>onnectionPool
from .serial_comm import SerialCommunicationOptimizer

logger = logging.getLogger(__name__)


class OptimizedHardwareService:
    """优化的硬件控制服务"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.serial_config = config.get('serial_server', {})
        
        # 串口服务器连接池
        base_url = f"http://{self.serial_config['host']}:{self.serial_config['port']}"
        self.connection_pool = SerialConnectionPool(
            base_url=base_url,
            max_connections=config.get('max_connections', 5)
        )
        
        # 设备状态缓存
        self.device_states = {
            'lidar': {'power': False, 'last_update': None},
            'ptz': {'power': False, 'last_update': None},
            'auxiliary_1': {'power': False, 'last_update': None},
            'auxiliary_2': {'power': False, 'last_update': None},
            'auxiliary_3': {'power': False, 'last_update': None},
            'auxiliary_4': {'power': False, 'last_update': None}
        }
        
        # DO通道映射
        self.do_mapping = {
            'lidar': 1,
            'ptz': 2,
            'auxiliary_1': 3,
            'auxiliary_2': 4,
            'auxiliary_3': 5,
            'auxiliary_4': 6
        }
        
        # 反向映射
        self.do_device_mapping = {v: k for k, v in self.do_mapping.items()}
        
        # 健康检查任务
        self.health_check_task = None
        
        # FastAPI应用
        self.app = FastAPI(title="优化的硬件控制服务")
        self._setup_routes()
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            stats = self.connection_pool.get_stats()
            return {
                "status": "healthy",
                "connection_pool": stats,
                "device_states": self.device_states,
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.post("/device/power")
        async def control_device_power(device: str, power: bool):
            """控制设备电源"""
            if device not in self.do_mapping:
                raise HTTPException(400, f"未知设备: {device}")
            
            success, message = await self.control_device_power(device, power)
            if not success:
                raise HTTPException(500, message)
            
            return {
                "status": "success",
                "device": device,
                "power": power,
                "message": message
            }
        
        @self.app.get("/device/status/{device}")
        async def get_device_status(device: str):
            """获取设备状态"""
            if device not in self.device_states:
                raise HTTPException(400, f"未知设备: {device}")
            
            return {
                "device": device,
                "state": self.device_states[device],
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.get("/device/status")
        async def get_all_device_status():
            """获取所有设备状态"""
            return {
                "devices": self.device_states,
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.post("/ptz/control")
        async def control_ptz(command: str, value: float = 0):
            """云台控制"""
            success, message = await self.control_ptz(command, value)
            if not success:
                raise HTTPException(500, message)
            
            return {
                "status": "success",
                "command": command,
                "value": value,
                "message": message
            }
        
        @self.app.post("/ptz/goto")
        async def ptz_goto(pan: float, tilt: float, zoom: float = 0):
            """云台定位"""
            success, message = await self.ptz_goto_position(pan, tilt, zoom)
            if not success:
                raise HTTPException(500, message)
            
            return {
                "status": "success",
                "position": {"pan": pan, "tilt": tilt, "zoom": zoom},
                "message": message
            }
        
        @self.app.get("/ptz/position")
        async def get_ptz_position():
            """获取云台位置"""
            position = await self.get_ptz_position()
            return {
                "position": position,
                "timestamp": datetime.now().isoformat()
            }
        
        @self.app.get("/stats")
        async def get_statistics():
            """获取服务统计信息"""
            return {
                "connection_pool": self.connection_pool.get_stats(),
                "device_states": self.device_states,
                "config": {
                    "serial_server": self.serial_config['host'],
                    "port": self.serial_config['port']
                }
            }
    
    async def start(self):
        """启动服务"""
        # 启动连接池
        await self.connection_pool.start()
        
        # 启动健康检查
        self.health_check_task = asyncio.create_task(self._health_check_loop())
        
        # 初始化设备状态
        await self._init_device_states()
        
        logger.info("优化的硬件控制服务已启动")
    
    async def stop(self):
        """停止服务"""
        # 停止健康检查
        if self.health_check_task:
            self.health_check_task.cancel()
            try:
                await self.health_check_task
            except asyncio.CancelledError:
                pass
        
        # 停止连接池
        await self.connection_pool.stop()
        
        logger.info("优化的硬件控制服务已停止")
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(30)  # 每30秒检查一次
                await self._check_device_states()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查失败: {e}")
    
    async def _init_device_states(self):
        """初始化设备状态"""
        try:
            # 查询DO状态
            response = await self.connection_pool.execute(
                'GET', '/relay/do/status',
                params={'channel': self.serial_config['io_channel']}
            )
            
            if response.get('success'):
                do_status = response.get('data', {}).get('do_status', [])
                for i, status in enumerate(do_status):
                    if i + 1 in self.do_device_mapping:
                        device = self.do_device_mapping[i + 1]
                        self.device_states[device]['power'] = bool(status)
                        self.device_states[device]['last_update'] = datetime.now()
                        
        except Exception as e:
            logger.error(f"初始化设备状态失败: {e}")
    
    async def _check_device_states(self):
        """检查设备状态"""
        await self._init_device_states()
    
    async def control_device_power(self, device: str, power: bool) -> Tuple[bool, str]:
        """控制设备电源"""
        if device not in self.do_mapping:
            return False, f"未知设备: {device}"
        
        do_index = self.do_mapping[device]
        
        try:
            # 发送控制命令
            response = await self.connection_pool.execute(
                'POST', '/relay/do/control',
                json={
                    'channel': self.serial_config['io_channel'],
                    'do_index': do_index,
                    'status': 1 if power else 0
                }
            )
            
            if response.get('success'):
                # 更新状态缓存
                self.device_states[device]['power'] = power
                self.device_states[device]['last_update'] = datetime.now()
                
                action = "开启" if power else "关闭"
                return True, f"{device} 已{action}"
            else:
                return False, response.get('message', '控制失败')
                
        except Exception as e:
            logger.error(f"控制设备电源失败: {e}")
            return False, str(e)
    
    async def control_ptz(self, command: str, value: float = 0) -> Tuple[bool, str]:
        """云台控制"""
        try:
            # 检查云台电源
            if not self.device_states['ptz']['power']:
                return False, "云台未开启"
            
            # 发送控制命令
            response = await self.connection_pool.execute(
                'POST', '/ptz/control',
                json={
                    'channel': self.serial_config['ptz_channel'],
                    'command': command,
                    'value': value
                }
            )
            
            if response.get('success'):
                return True, f"云台控制命令 {command} 执行成功"
            else:
                return False, response.get('message', '控制失败')
                
        except Exception as e:
            logger.error(f"云台控制失败: {e}")
            return False, str(e)
    
    async def ptz_goto_position(self, pan: float, tilt: float, zoom: float = 0) -> Tuple[bool, str]:
        """云台定位"""
        try:
            # 检查云台电源
            if not self.device_states['ptz']['power']:
                return False, "云台未开启"
            
            # 发送定位命令
            response = await self.connection_pool.execute(
                'POST', '/ptz/goto',
                json={
                    'channel': self.serial_config['ptz_channel'],
                    'pan': pan,
                    'tilt': tilt,
                    'zoom': zoom
                }
            )
            
            if response.get('success'):
                return True, f"云台已移动到位置: pan={pan}, tilt={tilt}"
            else:
                return False, response.get('message', '定位失败')
                
        except Exception as e:
            logger.error(f"云台定位失败: {e}")
            return False, str(e)
    
    async def get_ptz_position(self) -> Dict:
        """获取云台位置"""
        try:
            response = await self.connection_pool.execute(
                'GET', '/ptz/position',
                params={'channel': self.serial_config['ptz_channel']}
            )
            
            if response.get('success'):
                return response.get('data', {})
            else:
                return {"pan": 0, "tilt": 0, "zoom": 0}
                
        except Exception as e:
            logger.error(f"获取云台位置失败: {e}")
            return {"pan": 0, "tilt": 0, "zoom": 0}


async def create_optimized_service(config: Dict) -> OptimizedHardwareService:
    """创建优化的硬件服务实例"""
    service = OptimizedHardwareService(config)
    await service.start()
    return service


def run_service(config: Dict, host: str = "0.0.0.0", port: int = 7080):
    """运行服务"""
    async def startup():
        app.state.hardware_service = await create_optimized_service(config)
    
    async def shutdown():
        if hasattr(app.state, 'hardware_service'):
            await app.state.hardware_service.stop()
    
    # 创建应用
    service = OptimizedHardwareService(config)
    app = service.app
    
    # 设置生命周期事件
    app.add_event_handler("startup", startup)
    app.add_event_handler("shutdown", shutdown)
    
    # 运行服务器
    uvicorn.run(app, host=host, port=port)


if __name__ == "__main__":
    import yaml
    
    # 加载配置
    config_path = "hardware_config.yaml"
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行服务
    run_service(config)