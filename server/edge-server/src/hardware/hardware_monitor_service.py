#!/usr/bin/env python3
"""
硬件状态监控服务
整合硬件控制、状态同步和中央通信
作为硬件相关功能的统一入口
"""

import asyncio
import logging
import sys
import os
import yaml
import signal
from typing import Dict, Any

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入必要的模块
from hardware.hardware_state_sync_service import HardwareStateSyncService
from center_connector_grpc import CenterConnectorGRPC
from config_loader import load_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HardwareMonitorService:
    """硬件监控服务主类"""
    
    def __init__(self, config_path: str = None):
        """
        初始化硬件监控服务
        
        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        if config_path:
            with open(config_path, 'r') as f:
                self.config = yaml.safe_load(f)
        else:
            self.config = load_config()
        
        # 创建服务实例
        self.state_sync_service = None
        self.center_connector = None
        
        # 运行状态
        self.running = False
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """处理系统信号"""
        logger.info(f"收到信号 {signum}，准备停止服务...")
        self.running = False
    
    async def initialize(self):
        """初始化所有服务"""
        try:
            logger.info("开始初始化硬件监控服务...")
            
            # 1. 创建硬件状态同步服务
            sync_config = {
                "redis": self.config.get('redis', {}),
                "rpc": self.config.get('rpc', {"port": 8090}),
                "state_sync": self.config.get('state_sync', {
                    "collection_interval": 10,
                    "change_threshold": 0.1,
                    "offline_timeout": 30
                }),
                "serial_server": self.config.get('serial_server', {
                    "host": "*************",
                    "io_port": 7100
                })
            }
            
            self.state_sync_service = HardwareStateSyncService(sync_config)
            await self.state_sync_service.initialize()
            
            # 2. 创建中央连接器
            center_config = self.config.get('center_server', {
                "enabled": True,
                "host": "localhost",
                "port": 51001,
                "heartbeat_interval": 30,
                "reconnect_interval": 60,
                "auto_register": True
            })
            
            self.center_connector = CenterConnectorGRPC(center_config)
            
            # 3. 注册回调
            self.state_sync_service.set_center_connector(self.center_connector)
            self.center_connector.register_hardware_state_collector(
                self.state_sync_service.collect_hardware_state
            )
            self.center_connector.register_control_command_handler(
                self.state_sync_service.handle_control_command
            )
            
            logger.info("硬件监控服务初始化完成")
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise
    
    async def start(self):
        """启动服务"""
        if self.running:
            logger.warning("服务已在运行")
            return
        
        self.running = True
        
        try:
            # 启动硬件状态同步服务
            await self.state_sync_service.start()
            
            # 启动中央连接器
            self.center_connector.start()
            
            logger.info("=" * 60)
            logger.info("硬件监控服务已启动")
            logger.info(f"RPC端口: {self.config.get('rpc', {}).get('port', 8090)}")
            logger.info(f"硬件控制API: http://localhost:7080")
            logger.info(f"状态采集间隔: {self.config.get('state_sync', {}).get('collection_interval', 10)}秒")
            logger.info("=" * 60)
            
            # 主循环
            while self.running:
                await asyncio.sleep(1)
                
                # 检查服务健康状态
                if not self._check_health():
                    logger.warning("服务健康检查失败")
                
        except Exception as e:
            logger.error(f"服务运行错误: {e}")
        finally:
            await self.stop()
    
    async def stop(self):
        """停止服务"""
        logger.info("正在停止硬件监控服务...")
        
        # 停止中央连接器
        if self.center_connector:
            self.center_connector.stop()
        
        # 停止状态同步服务
        if self.state_sync_service:
            await self.state_sync_service.stop()
        
        logger.info("硬件监控服务已停止")
    
    def _check_health(self) -> bool:
        """检查服务健康状态"""
        # 检查中央连接器状态
        if self.center_connector:
            connector_status = self.center_connector.get_status()
            if not connector_status.get('connected') and connector_status.get('enabled'):
                logger.warning("中央连接器未连接")
        
        # 检查硬件状态
        # TODO: 添加更多健康检查
        
        return True
    
    async def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        status = {
            "running": self.running,
            "center_connector": self.center_connector.get_status() if self.center_connector else None,
            "hardware_state": await self.state_sync_service.collect_hardware_state() if self.state_sync_service else None
        }
        return status


async def main():
    """主函数"""
    # 从环境变量或参数获取配置路径
    config_path = os.environ.get('HARDWARE_CONFIG_PATH')
    if not config_path and len(sys.argv) > 1:
        config_path = sys.argv[1]
    
    # 创建服务
    service = HardwareMonitorService(config_path)
    
    try:
        # 初始化
        await service.initialize()
        
        # 启动服务
        await service.start()
        
    except KeyboardInterrupt:
        logger.info("收到键盘中断")
    except Exception as e:
        logger.error(f"服务异常: {e}")
    finally:
        # 确保清理
        await service.stop()


if __name__ == "__main__":
    asyncio.run(main())