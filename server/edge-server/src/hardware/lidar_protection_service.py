#!/usr/bin/env python3
"""
激光雷达自动保护服务
定期检查雷达状态，如果在非业务场景下雷达仍在运行，则自动关闭
防止雷达长时间通电造成损坏
"""

import asyncio
import subprocess
import logging
from datetime import datetime
from typing import Optional
import sys
import os

sys.path.append('/home/<USER>/edge-server/modules/hardware/final')
from hardware_control_service import HardwareManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/home/<USER>/edge-server/modules/hardware/final/logs/lidar_protection.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('LidarProtection')

class LidarProtectionService:
    """激光雷达保护服务"""
    
    def __init__(self, check_interval: int = 600):  # 默认10分钟
        """
        初始化保护服务
        
        Args:
            check_interval: 检查间隔（秒），默认600秒（10分钟）
        """
        self.check_interval = check_interval
        self.hw_service = HardwareManager()
        self.business_active = False  # 业务是否活跃
        self.running = False
        self.check_count = 0
        
    def set_business_active(self, active: bool):
        """设置业务活跃状态"""
        self.business_active = active
        logger.info(f"业务状态设置为: {'活跃' if active else '空闲'}")
    
    async def check_lidar_status(self) -> bool:
        """
        检查雷达是否在线
        
        Returns:
            bool: True表示在线，False表示离线
        """
        try:
            # 使用ping检查雷达网络状态
            result = subprocess.run(
                ['ping', '-c', '1', '-W', '2', '192.168.1.201'],
                capture_output=True,
                text=True
            )
            return result.returncode == 0
        except Exception as e:
            logger.error(f"检查雷达状态时出错: {e}")
            return False
    
    async def shutdown_lidar_safely(self):
        """安全关闭雷达"""
        try:
            logger.warning("检测到雷达在非业务时间运行，准备关闭...")
            
            # 1. 获取当前IO状态
            status = await self.hw_service.io_status()
            if status['success'] and status['channels'].get('1', {}).get('state', False):
                logger.info("雷达IO通道确认开启，执行关闭流程...")
                
                # 2. 关闭雷达IO
                result = await self.hw_service.io_control(channel=1, state=False)
                if result['success']:
                    logger.info("雷达IO已关闭")
                    
                    # 3. 等待并验证关闭
                    await asyncio.sleep(3)
                    is_online = await self.check_lidar_status()
                    if not is_online:
                        logger.info("✅ 雷达已成功关闭")
                    else:
                        logger.error("❌ 雷达关闭后仍然在线，可能存在问题")
                else:
                    logger.error(f"关闭雷达IO失败: {result['message']}")
            else:
                logger.info("雷达IO已经是关闭状态")
                
        except Exception as e:
            logger.error(f"关闭雷达时出错: {e}")
    
    async def protection_check(self):
        """执行保护检查"""
        self.check_count += 1
        logger.info(f"执行第 {self.check_count} 次保护检查...")
        
        # 1. 检查是否在业务时间
        if self.business_active:
            logger.info("当前处于业务活跃状态，跳过保护检查")
            return
        
        # 2. 检查雷达是否在线
        is_online = await self.check_lidar_status()
        
        if is_online:
            logger.warning("⚠️ 检测到雷达在非业务时间仍在运行！")
            # 3. 如果在线且非业务时间，关闭雷达
            await self.shutdown_lidar_safely()
        else:
            logger.info("✅ 雷达状态正常（离线）")
    
    async def start(self):
        """启动保护服务"""
        self.running = True
        logger.info(f"激光雷达保护服务启动，检查间隔: {self.check_interval}秒")
        
        try:
            while self.running:
                try:
                    # 执行检查
                    await self.protection_check()
                    
                    # 等待下一次检查
                    logger.info(f"等待 {self.check_interval} 秒后进行下一次检查...")
                    await asyncio.sleep(self.check_interval)
                    
                except Exception as e:
                    logger.error(f"保护检查过程中出错: {e}")
                    await asyncio.sleep(60)  # 出错后等待1分钟再试
                    
        except KeyboardInterrupt:
            logger.info("收到中断信号，停止保护服务")
            self.stop()
    
    def stop(self):
        """停止保护服务"""
        self.running = False
        logger.info("激光雷达保护服务已停止")

async def test_protection_service():
    """测试保护服务（快速测试模式）"""
    logger.info("=== 启动保护服务测试模式 ===")
    
    # 创建保护服务（测试模式：30秒检查一次）
    protection = LidarProtectionService(check_interval=30)
    
    # 创建测试任务
    async def simulate_business():
        """模拟业务场景"""
        await asyncio.sleep(10)
        logger.info("模拟：业务开始")
        protection.set_business_active(True)
        
        await asyncio.sleep(40)
        logger.info("模拟：业务结束")
        protection.set_business_active(False)
        
        await asyncio.sleep(60)
        logger.info("模拟：停止保护服务")
        protection.stop()
    
    # 并行运行保护服务和业务模拟
    await asyncio.gather(
        protection.start(),
        simulate_business()
    )

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='激光雷达自动保护服务')
    parser.add_argument('--test', action='store_true', help='运行测试模式')
    parser.add_argument('--interval', type=int, default=600, help='检查间隔（秒），默认600')
    args = parser.parse_args()
    
    # 确保日志目录存在
    os.makedirs('/home/<USER>/edge-server/modules/hardware/final/logs', exist_ok=True)
    
    if args.test:
        # 测试模式
        asyncio.run(test_protection_service())
    else:
        # 正常模式
        protection = LidarProtectionService(check_interval=args.interval)
        try:
            asyncio.run(protection.start())
        except KeyboardInterrupt:
            logger.info("服务被用户中断")

if __name__ == "__main__":
    main()