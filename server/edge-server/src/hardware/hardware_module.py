"""
硬件控制任务总线模块
实现TaskBusModule接口，集成到任务总线
"""

import logging
from typing import Dict, Any
import asyncio
import requests

# 模拟TaskBusModule相关类
class ModuleStatus:
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"

class TaskResult:
    def __init__(self, success: bool, data=None, error=None):
        self.success = success
        self.data = data
        self.error = error

class TaskBusModule:
    def __init__(self, name):
        self.name = name
        self.status = ModuleStatus.IDLE
        self.current_task = None
        self.logger = logging.getLogger(f"module.{name}")
from .http_client import AsyncHTTPClient

class HardwareControlModule(TaskBusModule):
    """硬件控制模块"""
    
    def __init__(self, config: Dict = None):
        super().__init__("hardware")
        self.config = config or {}
        self.base_url = self.config.get('base_url', 'http://localhost:7080')
        self.channel_id = self.config.get('channel_id', 'd4ad2070b92f0000')
        self.http_client = AsyncHTTPClient(self.base_url)
        self._shutdown_executed = False  # 防止重复关闭
        
        # DO口配置（共6个继电器输出）
        self.do_config = {
            'lidar': 1,      # 激光雷达电源 - DO1
            'ptz': 2,        # 云台电源 - DO2
            'spare1': 3,     # 备用1 - DO3
            'spare2': 4,     # 备用2 - DO4
            'spare3': 5,     # 备用3 - DO5
            'spare4': 6      # 备用4 - DO6
        }
        
    async def initialize(self) -> bool:
        """初始化模块"""
        # 重试几次，给硬件服务时间启动
        max_retries = 3
        retry_delay = 2
        
        await self.http_client.connect()
        
        for attempt in range(max_retries):
            if await self.http_client.health_check():
                self.logger.info("Hardware control module initialized")
                return True
            else:
                self.logger.warning(f"Hardware service not ready, attempt {attempt+1}/{max_retries}")
            
            if attempt < max_retries - 1:
                await asyncio.sleep(retry_delay)
        
        self.logger.error("Hardware service not available after retries")
        self.status = ModuleStatus.ERROR
        return False
            
    def get_capabilities(self) -> Dict[str, Any]:
        """获取模块能力"""
        return {
            "actions": [
                "power_on",
                "power_off",
                "power_cycle",
                "get_power_status",
                "status",  # 添加status动作作为get_all_status的别名
                "ptz_goto",
                "ptz_move",
                "ptz_stop",
                "ptz_home",  # 添加云台归零动作
                "ptz_get_position",
                "emergency_shutdown",
                "get_all_status"
            ],
            "devices": {
                "do_ports": list(self.do_config.keys()),
                "ptz": ["horizontal", "vertical", "zoom"]
            },
            "features": {
                "auto_safety": True,
                "power_monitoring": True,
                "ptz_control": True
            }
        }
        
    async def handle_task(self, task: Dict[str, Any]) -> TaskResult:
        """处理任务"""
        action = task.get('action')
        params = task.get('params', {})
        task_id = task.get('task_id')
        
        try:
            self.status = ModuleStatus.BUSY
            self.current_task = task_id
            
            # 电源控制
            if action == "power_on":
                return await self._handle_power_on(params)
            elif action == "power_off":
                return await self._handle_power_off(params)
            elif action == "power_cycle":
                return await self._handle_power_cycle(params)
            elif action == "get_power_status":
                return self._handle_get_power_status(params)
                
            # PTZ控制
            elif action == "ptz_goto":
                return await self._handle_ptz_goto(params)
            elif action == "ptz_move":
                return await self._handle_ptz_move(params)
            elif action == "ptz_stop":
                return self._handle_ptz_stop()
            elif action == "ptz_home":
                return self._handle_ptz_home(params)
            elif action == "ptz_get_position":
                return self._handle_ptz_get_position()
                
            # 系统控制
            elif action == "emergency_shutdown":
                return await self._handle_emergency_shutdown()
            elif action == "get_all_status" or action == "status":
                return self._handle_get_all_status()
            else:
                return TaskResult(False, error=f"Unknown action: {action}")
                
        except Exception as e:
            self.logger.error(f"Task execution failed: {e}")
            return TaskResult(False, error=str(e))
        finally:
            self.status = ModuleStatus.IDLE
            self.current_task = None
            
    async def _handle_power_on(self, params: Dict) -> TaskResult:
        """处理电源开启"""
        device = params.get('device')
        
        if device not in self.do_config:
            return TaskResult(False, error=f"Unknown device: {device}")
            
        do_index = self.do_config[device]
        
        try:
            response = requests.post(
                f"{self.base_url}/control/do",
                json={
                    "channel": self.channel_id,
                    "DO": do_index,
                    "value": 1
                },
                proxies={'http': None, 'https': None}
            )
            
            if response.status_code == 200:
                self.logger.info(f"Powered on {device} (DO{do_index})")
                
                # 等待设备启动
                if device == 'lidar':
                    await asyncio.sleep(30)  # 激光雷达需要30秒启动
                elif device == 'ptz':
                    await asyncio.sleep(5)   # 云台需要5秒启动
                    
                return TaskResult(True, data={"device": device, "status": "on"})
            else:
                return TaskResult(False, error=f"Failed to power on: {response.text}")
                
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    async def _handle_power_off(self, params: Dict) -> TaskResult:
        """处理电源关闭"""
        device = params.get('device')
        
        if device not in self.do_config:
            return TaskResult(False, error=f"Unknown device: {device}")
            
        do_index = self.do_config[device]
        
        try:
            response = requests.post(
                f"{self.base_url}/control/do",
                json={
                    "channel": self.channel_id,
                    "DO": do_index,
                    "value": 0
                },
                proxies={'http': None, 'https': None}
            )
            
            if response.status_code == 200:
                self.logger.info(f"Powered off {device} (DO{do_index})")
                return TaskResult(True, data={"device": device, "status": "off"})
            else:
                return TaskResult(False, error=f"Failed to power off: {response.text}")
                
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    async def _handle_power_cycle(self, params: Dict) -> TaskResult:
        """处理电源重启"""
        device = params.get('device')
        delay = params.get('delay', 5)  # 默认5秒延迟
        
        # 先关闭
        off_result = self._handle_power_off(params)
        if not off_result.success:
            return off_result
            
        # 等待
        await asyncio.sleep(delay)
        
        # 再开启
        return self._handle_power_on(params)
        
    def _handle_get_power_status(self, params: Dict) -> TaskResult:
        """获取电源状态"""
        device = params.get('device')
        
        try:
            response = requests.get(
                f"{self.base_url}/status/do",
                params={"channel": self.channel_id},
                proxies={'http': None, 'https': None}
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if device:
                    # 获取特定设备状态
                    if device in self.do_config:
                        do_index = self.do_config[device]
                        # API返回格式: {"status": "success", "data": {"DO1": 0, "DO2": 1, ...}}
                        do_data = data.get('data', {})
                        status = do_data.get(f'DO{do_index}', 0)
                        return TaskResult(True, data={
                            "device": device,
                            "status": "on" if status == 1 else "off"
                        })
                    else:
                        return TaskResult(False, error=f"Unknown device: {device}")
                else:
                    # 获取所有设备状态
                    device_status = {}
                    do_data = data.get('data', {})
                    for dev, idx in self.do_config.items():
                        status = do_data.get(f'DO{idx}', 0)
                        device_status[dev] = "on" if status == 1 else "off"
                    return TaskResult(True, data=device_status)
            else:
                return TaskResult(False, error=f"Failed to get status: {response.text}")
                
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    async def _handle_ptz_goto(self, params: Dict) -> TaskResult:
        """PTZ定位"""
        horizontal = params.get('horizontal', 0)
        vertical = params.get('vertical', 0)
        speed = params.get('speed', 30)
        
        try:
            # 发送云台定位命令
            response = requests.post(
                f"{self.base_url}/control/ptz/angle",
                json={
                    "channel": "0000000000000000",
                    "address": 30,
                    "pan": horizontal,
                    "tilt": vertical,
                    "speed": speed
                },
                proxies={'http': None, 'https': None}
            )
            
            if response.status_code == 200:
                # 等待移动完成
                await asyncio.sleep(3)
                return TaskResult(True, data={
                    "horizontal": horizontal,
                    "vertical": vertical
                })
            else:
                return TaskResult(False, error=f"PTZ goto failed: {response.text}")
                
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    async def _handle_ptz_move(self, params: Dict) -> TaskResult:
        """PTZ移动"""
        direction = params.get('direction')  # up, down, left, right
        speed = params.get('speed', 50)
        duration = params.get('duration', 1)  # 移动时长（秒）
        
        direction_map = {
            'up': {'vertical': speed},
            'down': {'vertical': -speed},
            'left': {'horizontal': -speed},
            'right': {'horizontal': speed}
        }
        
        if direction not in direction_map:
            return TaskResult(False, error=f"Invalid direction: {direction}")
            
        try:
            # 设置速度并开始移动
            axis = "pan" if direction in ['left', 'right'] else "tilt"
            response = requests.post(
                f"{self.base_url}/api/ptz/speed",
                json={
                    "ptz_addr": 30,
                    "axis": axis,
                    "speed": speed if direction in ['right', 'down'] else -speed
                },
                proxies={'http': None, 'https': None}
            )
            
            if response.status_code == 200:
                # 持续移动
                await asyncio.sleep(duration)
                
                # 停止移动
                self._handle_ptz_stop()
                
                return TaskResult(True, data={
                    "direction": direction,
                    "duration": duration
                })
            else:
                return TaskResult(False, error=f"PTZ move failed: {response.text}")
                
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    def _handle_ptz_stop(self) -> TaskResult:
        """停止PTZ"""
        try:
            response = requests.post(
                f"{self.base_url}/api/ptz/stop",
                json={"ptz_addr": 30, "type": "all"},
                proxies={'http': None, 'https': None}
            )
            
            if response.status_code == 200:
                return TaskResult(True, data={"status": "stopped"})
            else:
                return TaskResult(False, error=f"PTZ stop failed: {response.text}")
                
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    def _handle_ptz_get_position(self) -> TaskResult:
        """获取PTZ位置"""
        try:
            response = requests.get(
                f"{self.base_url}/api/ptz/angle",
                params={"ptz_addr": 30, "axis": "all"},
                proxies={'http': None, 'https': None}
            )
            
            if response.status_code == 200:
                data = response.json()
                return TaskResult(True, data=data)
            else:
                return TaskResult(False, error=f"Get position failed: {response.text}")
                
        except Exception as e:
            return TaskResult(False, error=str(e))
    
    async def _handle_ptz_home(self, params: Dict) -> TaskResult:
        """云台归零"""
        channel = params.get('channel', 30)  # 默认云台地址为30
        
        try:
            # 发送归零命令
            response = requests.post(
                f"{self.base_url}/control/ptz",
                json={
                    "channel": "0000000000000000",
                    "address": channel,
                    "command": "home"
                },
                proxies={'http': None, 'https': None}
            )
            
            if response.status_code == 200:
                self.logger.info(f"PTZ homing command sent to address {channel}")
                # 等待归零完成
                await asyncio.sleep(5)
                return TaskResult(True, data={"message": f"PTZ at address {channel} returned to home position"})
            else:
                return TaskResult(False, error=f"PTZ home failed: {response.text}")
                
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    async def _handle_emergency_shutdown(self) -> TaskResult:
        """紧急关闭所有设备"""
        if self._shutdown_executed:
            self.logger.info("Emergency shutdown already executed, skipping")
            return TaskResult(True, data={"message": "Already shutdown"})
            
        try:
            self._shutdown_executed = True
            
            # 关闭所有DO口
            for device in self.do_config.keys():
                self._handle_power_off({"device": device})
                
            # 停止PTZ
            self._handle_ptz_stop()
            
            self.logger.warning("Emergency shutdown executed")
            return TaskResult(True, data={"message": "All devices powered off"})
            
        except Exception as e:
            self._shutdown_executed = False  # 失败时重置标志
            return TaskResult(False, error=str(e))
            
    def _handle_get_all_status(self) -> TaskResult:
        """获取所有硬件状态"""
        try:
            # 获取电源状态
            power_result = self._handle_get_power_status({})
            
            # 获取PTZ位置
            ptz_result = self._handle_ptz_get_position()
            
            status = {
                "power": power_result.data if power_result.success else {},
                "ptz": ptz_result.data if ptz_result.success else {},
                "module_status": self.status.value
            }
            
            return TaskResult(True, data=status)
            
        except Exception as e:
            return TaskResult(False, error=str(e))
            
    def shutdown(self):
        """关闭模块"""
        # 执行安全关闭
        self._handle_emergency_shutdown()
        self.logger.info("Hardware control module shutdown")