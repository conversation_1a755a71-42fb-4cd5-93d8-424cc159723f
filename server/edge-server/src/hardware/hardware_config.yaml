# 硬件设备配置文件
# 生成时间: 2025-07-24

# 串口服务器连接配置
serial_server:
  host: "*************"
  port: 7100  # 串口服务器连接端口
  io_channel: "d4ad2070b92f0000"  # IO通道标识
  ptz_channel: "0000000000000000"  # PTZ通道标识
  timeout: 5.0
  retry_count: 3

# IO通道映射配置
io_mapping:
  # DO通道定义
  do_channels:
    DO1:
      index: 1
      device: "lidar"
      description: "激光雷达电源控制"
      channel_id: "d4ad2070b92f0000"
    DO2:
      index: 2
      device: "ptz"
      description: "云台电源控制"
      channel_id: "d4ad2070b92f0000"
    DO3:
      index: 3
      device: "auxiliary_1"
      description: "辅助设备1"
      channel_id: "d4ad2070b92f0000"
    DO4:
      index: 4
      device: "auxiliary_2"
      description: "辅助设备2"
      channel_id: "d4ad2070b92f0000"
    DO5:
      index: 5
      device: "auxiliary_3"
      description: "辅助设备3"
      channel_id: "d4ad2070b92f0000"
    DO6:
      index: 6
      device: "auxiliary_4"
      description: "辅助设备4"
      channel_id: "d4ad2070b92f0000"

# 激光雷达配置
lidar:
  ip: "*************"
  port: 5000
  multicast_ip: "***********"
  data_port: 2368
  startup_delay: 30  # 启动后等待时间（秒）
  
# 云台配置
ptz:
  control_channel: "0000000000000000"
  protocol: "Pelco-D"
  serial_config:
    address: 30        # 485地址码
    baud_rate: 9600    # 波特率
    data_bits: 8
    stop_bits: 1
    parity: "none"
  control_params:
    default_speed: 10.0      # 默认速度（度/秒）
    angle_tolerance: 1.0     # 角度容差（度）
    # 角度范围（根据实际云台规格）
    angle_range:
      pan:                   # 水平角度
        min: 0               # 最小水平角度
        max: 359             # 最大水平角度（0-359度）
      tilt:                  # 俯仰角度
        min: -60             # 最小俯仰角度
        max: 60              # 最大俯仰角度（-60到60度）
    # 速度表配置（基于硬件速度表）
    speed_tables:
      # 水平速度表 - 112个级别（0-111索引）
      pan_speeds: 112
      pan_speed_range:       # 水平速度范围（度/秒）
        min: 0.1             # 最小速度
        max: 400.0           # 最大速度
      # 俯仰速度表 - 112个级别（0-111索引）
      tilt_speeds: 112
      tilt_speed_range:      # 俯仰速度范围（度/秒）
        min: 0.1             # 最小速度
        max: 180.0           # 最大速度
      # 常用速度预设（度/秒）
      common_speeds:
        slow: 3.0            # 慢速
        normal: 10.0         # 正常速度
        fast: 15.0           # 快速
        max_pan: 19.1        # 水平最大推荐速度
        max_tilt: 10.0       # 俯仰最大推荐速度
  home_position:
    pan: 0
    tilt: 0
  
# 长期通电保护配置
protection:
  enabled: true
  check_interval: 600  # 检查间隔：10分钟（600秒）
  max_power_on_time: 600  # 最大通电时间：10分钟无业务活动自动断电
  critical_devices:
    - "lidar"    # 激光雷达（DO1口）
    - "ptz"      # 云台（DO2口）

  # 条件性断电规则
  conditional_shutdown:
    enabled: true
    ptz_home_required: true    # 云台必须归零后才能断电
    home_verification:
      enabled: true            # 启用归零位置验证
      max_attempts: 3          # 最大归零尝试次数
      angle_tolerance: 1.0     # 归零角度容差（度）
      timeout_per_attempt: 10  # 每次尝试超时时间（秒）
    emergency_override:
      enabled: true            # 紧急情况下允许强制断电
      timeout: 30              # 紧急断电超时时间（秒）

  # 设备启动时序控制
  startup_sequence:
    enabled: true
    lidar:
      startup_delay: 30          # 激光雷达启动等待时间（秒）
      power_stabilization: 5     # 电源稳定等待时间（秒）
    ptz:
      startup_delay: 5           # 云台启动等待时间（秒）
      power_stabilization: 2     # 电源稳定等待时间（秒）
      auto_home_on_startup: true # 启动后自动归零