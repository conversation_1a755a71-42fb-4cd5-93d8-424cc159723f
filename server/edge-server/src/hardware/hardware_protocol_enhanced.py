"""
增强的硬件控制协议实现
基于Java版本的正确实现优化
"""

import struct
import binascii
import logging
from typing import Tuple, Optional, List
from enum import Enum

logger = logging.getLogger(__name__)


class PTZCommand(Enum):
    """云台命令枚举"""
    # 角度控制
    SET_PAN_ANGLE = 0x4B      # 设置水平角度
    SET_TILT_ANGLE = 0x4D     # 设置垂直角度
    GET_PAN_ANGLE = 0x51      # 查询水平角度
    GET_TILT_ANGLE = 0x53     # 查询垂直角度
    
    # 速度控制
    SET_PAN_SPEED = 0x71      # 设置水平速度
    SET_TILT_SPEED = 0x73     # 设置垂直速度
    GET_PAN_SPEED = 0x79      # 查询水平速度
    GET_TILT_SPEED = 0x7B     # 查询垂直速度
    
    # 停止命令
    STOP_ALL = 0x5F           # 停止全部
    STOP_PAN = 0x5B           # 停止水平
    STOP_TILT = 0x5D          # 停止垂直
    
    # 归零命令
    ZERO_PAN = 0x55           # 水平归零
    ZERO_TILT = 0x57          # 垂直归零


class ModbusProtocol:
    """Modbus协议实现"""
    
    # CRC16查找表（Modbus标准）
    CRC16_TABLE = [
        0x0000, 0xC0C1, 0xC181, 0x0140, 0xC301, 0x03C0, 0x0280, 0xC241,
        0xC601, 0x06C0, 0x0780, 0xC741, 0x0500, 0xC5C1, 0xC481, 0x0440,
        0xCC01, 0x0CC0, 0x0D80, 0xCD41, 0x0F00, 0xCFC1, 0xCE81, 0x0E40,
        0x0A00, 0xCAC1, 0xCB81, 0x0B40, 0xC901, 0x09C0, 0x0880, 0xC841,
        0xD801, 0x18C0, 0x1980, 0xD941, 0x1B00, 0xDBC1, 0xDA81, 0x1A40,
        0x1E00, 0xDEC1, 0xDF81, 0x1F40, 0xDD01, 0x1DC0, 0x1C80, 0xDC41,
        0x1400, 0xD4C1, 0xD581, 0x1540, 0xD701, 0x17C0, 0x1680, 0xD641,
        0xD201, 0x12C0, 0x1380, 0xD341, 0x1100, 0xD1C1, 0xD081, 0x1040,
        0xF001, 0x30C0, 0x3180, 0xF141, 0x3300, 0xF3C1, 0xF281, 0x3240,
        0x3600, 0xF6C1, 0xF781, 0x3740, 0xF501, 0x35C0, 0x3480, 0xF441,
        0x3C00, 0xFCC1, 0xFD81, 0x3D40, 0xFF01, 0x3FC0, 0x3E80, 0xFE41,
        0xFA01, 0x3AC0, 0x3B80, 0xFB41, 0x3900, 0xF9C1, 0xF881, 0x3840,
        0x2800, 0xE8C1, 0xE981, 0x2940, 0xEB01, 0x2BC0, 0x2A80, 0xEA41,
        0xEE01, 0x2EC0, 0x2F80, 0xEF41, 0x2D00, 0xEDC1, 0xEC81, 0x2C40,
        0xE401, 0x24C0, 0x2580, 0xE541, 0x2700, 0xE7C1, 0xE681, 0x2640,
        0x2200, 0xE2C1, 0xE381, 0x2340, 0xE101, 0x21C0, 0x2080, 0xE041,
        0xA001, 0x60C0, 0x6180, 0xA141, 0x6300, 0xA3C1, 0xA281, 0x6240,
        0x6600, 0xA6C1, 0xA781, 0x6740, 0xA501, 0x65C0, 0x6480, 0xA441,
        0x6C00, 0xACC1, 0xAD81, 0x6D40, 0xAF01, 0x6FC0, 0x6E80, 0xAE41,
        0xAA01, 0x6AC0, 0x6B80, 0xAB41, 0x6900, 0xA9C1, 0xA881, 0x6840,
        0x7800, 0xB8C1, 0xB981, 0x7940, 0xBB01, 0x7BC0, 0x7A80, 0xBA41,
        0xBE01, 0x7EC0, 0x7F80, 0xBF41, 0x7D00, 0xBDC1, 0xBC81, 0x7C40,
        0xB401, 0x74C0, 0x7580, 0xB541, 0x7700, 0xB7C1, 0xB681, 0x7640,
        0x7200, 0xB2C1, 0xB381, 0x7340, 0xB101, 0x71C0, 0x7080, 0xB041,
        0x5000, 0x90C1, 0x9181, 0x5140, 0x9301, 0x53C0, 0x5280, 0x9241,
        0x9601, 0x56C0, 0x5780, 0x9741, 0x5500, 0x95C1, 0x9481, 0x5440,
        0x9C01, 0x5CC0, 0x5D80, 0x9D41, 0x5F00, 0x9FC1, 0x9E81, 0x5E40,
        0x5A00, 0x9AC1, 0x9B81, 0x5B40, 0x9901, 0x59C0, 0x5880, 0x9841,
        0x8801, 0x48C0, 0x4980, 0x8941, 0x4B00, 0x8BC1, 0x8A81, 0x4A40,
        0x4E00, 0x8EC1, 0x8F81, 0x4F40, 0x8D01, 0x4DC0, 0x4C80, 0x8C41,
        0x4400, 0x84C1, 0x8581, 0x4540, 0x8701, 0x47C0, 0x4680, 0x8641,
        0x8201, 0x42C0, 0x4380, 0x8341, 0x4100, 0x81C1, 0x8081, 0x4040
    ]
    
    @classmethod
    def calc_crc16(cls, data: bytes) -> int:
        """计算CRC16（使用查找表优化）"""
        crc = 0xFFFF
        for byte in data:
            crc = (crc >> 8) ^ cls.CRC16_TABLE[(crc ^ byte) & 0xFF]
        return crc
    
    @classmethod
    def build_do_command(cls, slave_addr: int, do_index: int, status: int) -> bytes:
        """构建DO控制命令（功能码05）"""
        # Modbus写单个线圈：地址 功能码05 寄存器地址 值(FF00/0000)
        register = do_index - 1  # DO1对应寄存器0
        value = 0xFF00 if status == 1 else 0x0000
        
        cmd = struct.pack('>BBH', slave_addr, 0x05, register) + struct.pack('>H', value)
        crc = cls.calc_crc16(cmd)
        
        return cmd + struct.pack('<H', crc)  # CRC低位在前
    
    @classmethod
    def build_do_query_command(cls, slave_addr: int, start: int = 0, count: int = 8) -> bytes:
        """构建DO查询命令（功能码01）"""
        # Modbus读线圈：地址 功能码01 起始地址 数量
        cmd = struct.pack('>BBHH', slave_addr, 0x01, start, count)
        crc = cls.calc_crc16(cmd)
        
        return cmd + struct.pack('<H', crc)
    
    @classmethod
    def parse_do_response(cls, response: bytes) -> Optional[List[int]]:
        """解析DO状态响应"""
        if len(response) < 5:
            return None
        
        # 验证CRC
        data = response[:-2]
        received_crc = struct.unpack('<H', response[-2:])[0]
        calculated_crc = cls.calc_crc16(data)
        
        if received_crc != calculated_crc:
            logger.warning(f"CRC校验失败: 接收{received_crc:04X}, 计算{calculated_crc:04X}")
            return None
        
        # 解析响应：地址(1) 功能码(1) 字节数(1) 数据(n)
        if response[1] == 0x01:  # 读线圈响应
            byte_count = response[2]
            status_bytes = response[3:3+byte_count]
            
            # 解析每个位
            do_status = []
            for byte in status_bytes:
                for i in range(8):
                    do_status.append((byte >> i) & 0x01)
            
            return do_status[:8]  # 只返回前8个
        
        return None


class PTZProtocol:
    """云台控制协议实现"""
    
    # 速度表（度/秒）
    PAN_SPEED_TABLE = [0.0, 0.5, 1.0, 2.0, 3.0, 5.0, 10.0, 20.0, 30.0, 40.0, 50.0, 60.0, 80.0, 100.0, 120.0, 140.0]
    TILT_SPEED_TABLE = [0.0, 0.3, 0.5, 1.0, 2.0, 3.0, 5.0, 10.0, 15.0, 20.0, 30.0, 40.0, 50.0, 60.0, 70.0, 80.0]
    
    @staticmethod
    def calc_checksum(data: bytes) -> int:
        """计算校验和（简单累加）"""
        return sum(data) & 0xFF
    
    @classmethod
    def build_angle_command(cls, addr: int, cmd: PTZCommand, angle: float) -> bytes:
        """构建角度控制命令"""
        # 协议格式：FF 地址 命令 角度高位 角度低位 校验和
        angle_int = int(angle * 100)  # 角度*100
        
        # 处理负角度
        if angle_int < 0:
            angle_int = 0x10000 + angle_int  # 补码表示
        
        high_byte = (angle_int >> 8) & 0xFF
        low_byte = angle_int & 0xFF
        
        cmd_bytes = bytes([0xFF, addr, cmd.value, high_byte, low_byte])
        checksum = cls.calc_checksum(cmd_bytes)
        
        return cmd_bytes + bytes([checksum])
    
    @classmethod
    def build_speed_command(cls, addr: int, cmd: PTZCommand, speed: float) -> bytes:
        """构建速度控制命令"""
        # 查找最接近的速度索引
        speed_table = cls.PAN_SPEED_TABLE if cmd in [PTZCommand.SET_PAN_SPEED, PTZCommand.GET_PAN_SPEED] else cls.TILT_SPEED_TABLE
        
        speed_index = 0
        min_diff = float('inf')
        for i, table_speed in enumerate(speed_table):
            diff = abs(table_speed - speed)
            if diff < min_diff:
                min_diff = diff
                speed_index = i
        
        # 协议格式：FF 地址 命令 00 速度索引 校验和
        cmd_bytes = bytes([0xFF, addr, cmd.value, 0x00, speed_index])
        checksum = cls.calc_checksum(cmd_bytes)
        
        return cmd_bytes + bytes([checksum])
    
    @classmethod
    def build_query_command(cls, addr: int, cmd: PTZCommand) -> bytes:
        """构建查询命令"""
        # 协议格式：FF 地址 命令 00 00 校验和
        cmd_bytes = bytes([0xFF, addr, cmd.value, 0x00, 0x00])
        checksum = cls.calc_checksum(cmd_bytes)
        
        return cmd_bytes + bytes([checksum])
    
    @classmethod
    def build_stop_command(cls, addr: int, stop_type: str = "all") -> bytes:
        """构建停止命令"""
        stop_cmds = {
            "all": PTZCommand.STOP_ALL,
            "pan": PTZCommand.STOP_PAN,
            "tilt": PTZCommand.STOP_TILT
        }
        
        cmd = stop_cmds.get(stop_type, PTZCommand.STOP_ALL)
        cmd_bytes = bytes([0xFF, addr, cmd.value, 0x00, 0x00])
        checksum = cls.calc_checksum(cmd_bytes)
        
        return cmd_bytes + bytes([checksum])
    
    @classmethod
    def parse_angle_response(cls, response: bytes) -> Optional[float]:
        """解析角度响应"""
        if len(response) < 6:
            return None
        
        # 验证帧头和校验和
        if response[0] != 0xFF:
            return None
        
        checksum = cls.calc_checksum(response[:-1])
        if checksum != response[-1]:
            logger.warning(f"PTZ响应校验和错误: 接收{response[-1]:02X}, 计算{checksum:02X}")
            return None
        
        # 解析角度（高位+低位）
        angle_int = (response[3] << 8) | response[4]
        
        # 处理负数（补码）
        if angle_int >= 0x8000:
            angle_int = angle_int - 0x10000
        
        return angle_int / 100.0
    
    @classmethod
    def parse_speed_response(cls, response: bytes, is_pan: bool = True) -> Optional[float]:
        """解析速度响应"""
        if len(response) < 6:
            return None
        
        # 验证帧头和校验和
        if response[0] != 0xFF:
            return None
        
        checksum = cls.calc_checksum(response[:-1])
        if checksum != response[-1]:
            return None
        
        # 解析速度索引
        speed_index = response[4]
        speed_table = cls.PAN_SPEED_TABLE if is_pan else cls.TILT_SPEED_TABLE
        
        if speed_index < len(speed_table):
            return speed_table[speed_index]
        
        return None


class EnhancedProtocolHandler:
    """增强的协议处理器"""
    
    def __init__(self):
        self.modbus = ModbusProtocol()
        self.ptz = PTZProtocol()
        
    def format_do_command(self, slave_addr: int, do_index: int, status: int) -> Tuple[bytes, str]:
        """格式化DO命令"""
        cmd = self.modbus.build_do_command(slave_addr, do_index, status)
        hex_str = binascii.hexlify(cmd).decode()
        
        action = "打开" if status == 1 else "关闭"
        desc = f"{action}DO{do_index}"
        
        return cmd, hex_str, desc
    
    def format_ptz_angle_command(self, addr: int, angle: float, is_pan: bool = True) -> Tuple[bytes, str]:
        """格式化云台角度命令"""
        cmd_type = PTZCommand.SET_PAN_ANGLE if is_pan else PTZCommand.SET_TILT_ANGLE
        cmd = self.ptz.build_angle_command(addr, cmd_type, angle)
        hex_str = binascii.hexlify(cmd).decode()
        
        axis = "水平" if is_pan else "垂直"
        desc = f"设置{axis}角度: {angle}°"
        
        return cmd, hex_str, desc
    
    def format_ptz_speed_command(self, addr: int, speed: float, is_pan: bool = True) -> Tuple[bytes, str]:
        """格式化云台速度命令"""
        cmd_type = PTZCommand.SET_PAN_SPEED if is_pan else PTZCommand.SET_TILT_SPEED
        cmd = self.ptz.build_speed_command(addr, cmd_type, speed)
        hex_str = binascii.hexlify(cmd).decode()
        
        axis = "水平" if is_pan else "垂直"
        desc = f"设置{axis}速度: {speed}°/s"
        
        return cmd, hex_str, desc
    
    def validate_response(self, response: bytes, expected_cmd: bytes) -> bool:
        """验证响应是否匹配命令"""
        if not response:
            return False
        
        # 对于Modbus，检查地址和功能码
        if len(expected_cmd) >= 2 and len(response) >= 2:
            if expected_cmd[0] == response[0] and expected_cmd[1] == response[1]:
                return True
        
        # 对于PTZ，检查帧头和地址
        if expected_cmd[0] == 0xFF and response[0] == 0xFF:
            if expected_cmd[1] == response[1]:
                return True
        
        return False


if __name__ == "__main__":
    # 测试协议实现
    handler = EnhancedProtocolHandler()
    
    print("=== Modbus协议测试 ===")
    # DO控制命令
    cmd, hex_str, desc = handler.format_do_command(1, 1, 1)
    print(f"{desc}: {hex_str}")
    
    # DO查询命令
    query_cmd = handler.modbus.build_do_query_command(1)
    print(f"查询DO状态: {binascii.hexlify(query_cmd).decode()}")
    
    print("\n=== PTZ协议测试 ===")
    # 角度控制
    cmd, hex_str, desc = handler.format_ptz_angle_command(30, 45.0, True)
    print(f"{desc}: {hex_str}")
    
    # 速度控制
    cmd, hex_str, desc = handler.format_ptz_speed_command(30, 10.0, True)
    print(f"{desc}: {hex_str}")
    
    # 查询命令
    query_cmd = handler.ptz.build_query_command(30, PTZCommand.GET_PAN_ANGLE)
    print(f"查询水平角度: {binascii.hexlify(query_cmd).decode()}")