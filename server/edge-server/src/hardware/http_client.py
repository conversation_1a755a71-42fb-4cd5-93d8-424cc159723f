"""
异步HTTP客户端工具类
提供统一的HTTP请求接口和连接池管理
"""

import aiohttp
import asyncio
import logging
from typing import Dict, Any, Optional
from functools import wraps

logger = logging.getLogger(__name__)

def with_retry(max_retries: int = 3, backoff: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(self, *args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return await func(self, *args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        raise
                    await asyncio.sleep(backoff * (2 ** attempt))
                    logger.warning(f"Request failed, retrying {attempt + 1}/{max_retries}: {e}")
            return None
        return wrapper
    return decorator

class AsyncHTTPClient:
    """异步HTTP客户端"""
    
    def __init__(self, base_url: str, timeout: int = 5):
        self.base_url = base_url
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=timeout)
        
    async def __aenter__(self):
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.close()
    
    async def connect(self):
        """建立连接"""
        if self.session is None:
            connector = aiohttp.TCPConnector(
                limit=10,
                ttl_dns_cache=300,
                enable_cleanup_closed=True
            )
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=self.timeout
            )
    
    async def close(self):
        """关闭连接"""
        if self.session:
            await self.session.close()
            self.session = None
    
    @with_retry(max_retries=3, backoff=1.0)
    async def get(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """GET请求"""
        if not self.session:
            await self.connect()
            
        url = f"{self.base_url}{endpoint}"
        async with self.session.get(url, **kwargs) as response:
            if response.status >= 400:
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status
                )
            return await response.json()
    
    @with_retry(max_retries=3, backoff=1.0)
    async def post(self, endpoint: str, json_data: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
        """POST请求"""
        if not self.session:
            await self.connect()
            
        url = f"{self.base_url}{endpoint}"
        async with self.session.post(url, json=json_data, **kwargs) as response:
            if response.status >= 400:
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status
                )
            return await response.json()
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            result = await self.get("/health")
            return result.get("status") == "healthy"
        except Exception as e:
            logger.warning(f"Health check failed: {e}")
            return False