#!/usr/bin/env python3
"""
优化版硬件控制服务 - 基于Java实现的完整重构
主要优化：
1. DI主动上报处理（40 57格式）
2. DO查询优化（使用缓存减少485查询）
3. 完整的保护机制
4. 业务任务自动清理
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
from aiohttp import web
import yaml
import json
import struct

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 云台控制枚举
class PTZEnum(Enum):
    PAN = ("4b", "51", "52")   # 水平方向
    TILT = ("4d", "53", "54")  # 俯仰方向
    
    def __init__(self, spin_code, send_search_code, report_search_code):
        self.spin_code = spin_code
        self.send_search_code = send_search_code
        self.report_search_code = report_search_code

# Modbus CRC16计算
class ModbusProtocol:
    """Modbus协议实现"""
    CRC16_TABLE = [
        0x0000, 0xC0C1, 0xC181, 0x0140, 0xC301, 0x03C0, 0x0280, 0xC241,
        0xC601, 0x06C0, 0x0780, 0xC741, 0x0500, 0xC5C1, 0xC481, 0x0440,
        0xCC01, 0x0CC0, 0x0D80, 0xCD41, 0x0F00, 0xCFC1, 0xCE81, 0x0E40,
        0x0A00, 0xCAC1, 0xCB81, 0x0B40, 0xC901, 0x09C0, 0x0880, 0xC841,
        0xD801, 0x18C0, 0x1980, 0xD941, 0x1B00, 0xDBC1, 0xDA81, 0x1A40,
        0x1E00, 0xDEC1, 0xDF81, 0x1F40, 0xDD01, 0x1DC0, 0x1C80, 0xDC41,
        0x1400, 0xD4C1, 0xD581, 0x1540, 0xD701, 0x17C0, 0x1680, 0xD641,
        0xD201, 0x12C0, 0x1380, 0xD341, 0x1100, 0xD1C1, 0xD081, 0x1040,
        0xF001, 0x30C0, 0x3180, 0xF141, 0x3300, 0xF3C1, 0xF281, 0x3240,
        0x3600, 0xF6C1, 0xF781, 0x3740, 0xF501, 0x35C0, 0x3480, 0xF441,
        0x3C00, 0xFCC1, 0xFD81, 0x3D40, 0xFF01, 0x3FC0, 0x3E80, 0xFE41,
        0xFA01, 0x3AC0, 0x3B80, 0xFB41, 0x3900, 0xF9C1, 0xF881, 0x3840,
        0x2800, 0xE8C1, 0xE981, 0x2940, 0xEB01, 0x2BC0, 0x2A80, 0xEA41,
        0xEE01, 0x2EC0, 0x2F80, 0xEF41, 0x2D00, 0xEDC1, 0xEC81, 0x2C40,
        0xE401, 0x24C0, 0x2580, 0xE541, 0x2700, 0xE7C1, 0xE681, 0x2640,
        0x2200, 0xE2C1, 0xE381, 0x2340, 0xE101, 0x21C0, 0x2080, 0xE041,
        0xA001, 0x60C0, 0x6180, 0xA141, 0x6300, 0xA3C1, 0xA281, 0x6240,
        0x6600, 0xA6C1, 0xA781, 0x6740, 0xA501, 0x65C0, 0x6480, 0xA441,
        0x6C00, 0xACC1, 0xAD81, 0x6D40, 0xAF01, 0x6FC0, 0x6E80, 0xAE41,
        0xAA01, 0x6AC0, 0x6B80, 0xAB41, 0x6900, 0xA9C1, 0xA881, 0x6840,
        0x7800, 0xB8C1, 0xB981, 0x7940, 0xBB01, 0x7BC0, 0x7A80, 0xBA41,
        0xBE01, 0x7EC0, 0x7F80, 0xBF41, 0x7D00, 0xBDC1, 0xBC81, 0x7C40,
        0xB401, 0x74C0, 0x7580, 0xB541, 0x7700, 0xB7C1, 0xB681, 0x7640,
        0x7200, 0xB2C1, 0xB381, 0x7340, 0xB101, 0x71C0, 0x7080, 0xB041,
        0x5000, 0x90C1, 0x9181, 0x5140, 0x9301, 0x53C0, 0x5280, 0x9241,
        0x9601, 0x56C0, 0x5780, 0x9741, 0x5500, 0x95C1, 0x9481, 0x5440,
        0x9C01, 0x5CC0, 0x5D80, 0x9D41, 0x5F00, 0x9FC1, 0x9E81, 0x5E40,
        0x5A00, 0x9AC1, 0x9B81, 0x5B40, 0x9901, 0x59C0, 0x5880, 0x9841,
        0x8801, 0x48C0, 0x4980, 0x8941, 0x4B00, 0x8BC1, 0x8A81, 0x4A40,
        0x4E00, 0x8EC1, 0x8F81, 0x4F40, 0x8D01, 0x4DC0, 0x4C80, 0x8C41,
        0x4400, 0x84C1, 0x8581, 0x4540, 0x8701, 0x47C0, 0x4680, 0x8641,
        0x8201, 0x42C0, 0x4380, 0x8341, 0x4100, 0x81C1, 0x8081, 0x4040
    ]
    
    @staticmethod
    def calc_crc16(data: bytes) -> int:
        """计算Modbus CRC16"""
        crc = 0xFFFF
        for byte in data:
            crc = (crc >> 8) ^ ModbusProtocol.CRC16_TABLE[(crc ^ byte) & 0xFF]
        return crc

# 云台控制协议
class PTZProtocol:
    """云台控制协议实现 - 基于Java版本"""
    # 水平速度表
    PAN_SPEED_TABLE = [
        0.1, 0.5, 1.0, 2.0, 3.0, 7.0, 7.2, 7.4, 7.7, 8.0,
        9.0, 9.5, 10.0, 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7,
        10.8, 10.9, 11.0, 11.1, 11.3, 11.5, 11.7, 11.9, 12.1, 12.3,
        12.5, 12.7, 12.9, 13.1, 13.3, 13.5, 13.7, 13.9, 14.0, 14.2,
        14.4, 14.7, 14.9, 15.2, 15.5, 15.7, 15.9, 16.0, 16.1, 16.3,
        16.5, 16.7, 16.9, 17.0, 17.1, 17.2, 17.3, 17.4, 17.5, 17.6,
        17.7, 17.8, 17.9, 18.0
    ]
    
    # 俯仰速度表
    TILT_SPEED_TABLE = [
        0.1, 0.5, 0.8, 1.2, 1.4, 1.6, 1.8, 2.0, 2.2, 2.4,
        2.6, 2.8, 3.0, 3.2, 3.4, 3.6, 3.8, 4.0, 4.2, 4.4,
        4.6, 4.8, 5.0, 5.2, 5.4, 5.6, 5.8, 6.0, 6.2, 6.4,
        6.6, 6.8, 6.9, 7.0, 7.1, 7.2, 7.3, 7.4, 7.5, 7.6,
        7.7, 7.8, 7.9, 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 8.7,
        8.8, 8.9, 9.0, 9.1, 9.2, 9.3, 9.4, 9.5, 9.6, 9.7,
        9.8, 9.9, 10.0, 10.0
    ]
    
    @staticmethod
    def calc_checksum(data: str) -> str:
        """计算校验和"""
        data_sum = 0
        for i in range(0, len(data), 2):
            byte_str = data[i:i+2]
            data_sum += int(byte_str, 16)
        checksum = data_sum % 256
        return f"{checksum:02x}"
    
    @staticmethod
    def calc_spin_angle_cmd(angle: int, slave_address: int, direction: PTZEnum) -> str:
        """计算角度控制命令"""
        if direction == PTZEnum.PAN:
            number = angle * 100
        else:
            number = angle * 100 if angle >= 0 else (360 + angle) * 100
        
        address_hex = f"{slave_address:02x}"
        angle_hex = f"{number:04x}"
        checksum_str = address_hex + "00" + direction.spin_code + angle_hex
        spin_cmd = "ff" + checksum_str + PTZProtocol.calc_checksum(checksum_str)
        return spin_cmd
    
    @staticmethod
    def calc_search_angle_cmd(slave_address: int, direction: PTZEnum) -> str:
        """计算查询角度命令"""
        address = f"{slave_address:02x}"
        cs_str = address + "00" + direction.send_search_code + "0000"
        search_cmd = "ff" + cs_str + PTZProtocol.calc_checksum(cs_str)
        return search_cmd
    
    @staticmethod
    def calc_setup_speed_cmd(speed_per_second: float, slave_address: int, direction: PTZEnum) -> str:
        """计算设置速度命令"""
        if direction == PTZEnum.PAN:
            speed_table = PTZProtocol.PAN_SPEED_TABLE
            speed_cmd_prefix = "e08106010e"
        else:
            speed_table = PTZProtocol.TILT_SPEED_TABLE
            speed_cmd_prefix = "e08106010f"
        
        # 查找速度索引
        try:
            index = speed_table.index(speed_per_second)
        except ValueError:
            # 找最接近的速度
            import bisect
            index = bisect.bisect_left(speed_table, speed_per_second)
            if index == 0:
                index = 0
            elif index == len(speed_table):
                index = len(speed_table) - 1
            else:
                if abs(speed_table[index-1] - speed_per_second) < abs(speed_table[index] - speed_per_second):
                    index = index - 1
        
        address_hex = f"{slave_address:02x}"
        speed_index = f"{index:02x}"
        speed_cmd = speed_cmd_prefix + address_hex + speed_index
        return speed_cmd + PTZProtocol.calc_checksum(speed_cmd)
    
    @staticmethod
    def left_spin(slave_address: int) -> str:
        """左转命令"""
        address = f"{slave_address:02x}"
        cmd = address + "00042000"
        crc = PTZProtocol.calc_checksum(cmd)
        return "ff" + cmd + crc
    
    @staticmethod
    def right_spin(slave_address: int) -> str:
        """右转命令"""
        address = f"{slave_address:02x}"
        cmd = address + "00022000"
        crc = PTZProtocol.calc_checksum(cmd)
        return "ff" + cmd + crc
    
    @staticmethod
    def stop(slave_address: int) -> str:
        """停止命令"""
        address = f"{slave_address:02x}"
        cmd = address + "00000000"
        crc = PTZProtocol.calc_checksum(cmd)
        return "ff" + cmd + crc
    
    @staticmethod
    def resolve_ptz_angle(msg: str) -> Optional[dict]:
        """解析云台角度响应"""
        try:
            msg_parts = msg.strip().split()
            if len(msg_parts) != 7 or msg_parts[0] != "ff":
                return None
            
            slave_address = int(msg_parts[1], 16)
            angle = int(msg_parts[4] + msg_parts[5], 16)
            real_angle = angle // 100
            
            if msg_parts[3] == PTZEnum.PAN.report_search_code:
                return {"slave_address": slave_address, "angle": real_angle, "direction": "PAN"}
            elif msg_parts[3] == PTZEnum.TILT.report_search_code:
                if 300 <= real_angle <= 359:
                    real_angle -= 360
                return {"slave_address": slave_address, "angle": real_angle, "direction": "TILT"}
            
            return None
        except Exception as e:
            logger.error(f"云台角度解析异常: {e}")
            return None

# 继电器控制协议
class RelayProtocol:
    """继电器控制协议实现 - 基于Java版本"""
    DI_REPORT_FIRST_BYTE = "40"
    DI_REPORT_SECOND_BYTE = "57"
    
    @staticmethod
    def calc_do_search_cmd(slave_address: int, do_number: int) -> str:
        """计算DO查询命令"""
        address = f"{slave_address:02x}"
        search_do_number = f"{do_number:04x}"
        cmd_str = address + "010000" + search_do_number
        cmd_bytes = bytes.fromhex(cmd_str)
        crc = ModbusProtocol.calc_crc16(cmd_bytes)
        return cmd_str + f"{crc:04x}"
    
    @staticmethod
    def calc_do_control_cmd(slave_address: int, do_index: int, state: bool) -> str:
        """计算DO控制命令"""
        address = f"{slave_address:02x}"
        do_x = f"{do_index - 1:04x}"
        control = "ff00" if state else "0000"
        cmd = address + "05" + do_x + control
        cmd_bytes = bytes.fromhex(cmd)
        crc = ModbusProtocol.calc_crc16(cmd_bytes)
        return cmd + f"{crc:04x}"
    
    @staticmethod
    def resolve_do_status(msg: str) -> Optional[dict]:
        """解析DO状态响应"""
        try:
            msg_parts = msg.strip().split()
            if len(msg_parts) != 6:
                return None
            
            slave_address = int(msg_parts[0], 16)
            do_status_byte = int(msg_parts[3], 16)
            # 位序反转
            binary_str = format(do_status_byte, '08b')[::-1]
            
            return {
                "slave_address": slave_address,
                "do_status": list(binary_str),
                "raw_data": msg
            }
        except Exception as e:
            logger.error(f"DO状态解析异常: {e}")
            return None
    
    @staticmethod
    def resolve_di_status(msg: str) -> Optional[dict]:
        """解析DI状态上报 - DI状态反映DO的实际状态"""
        try:
            msg_parts = msg.strip().split()
            if len(msg_parts) != 6:
                return None
            
            # 检查DI上报标志
            if msg_parts[0] != RelayProtocol.DI_REPORT_FIRST_BYTE or msg_parts[1] != RelayProtocol.DI_REPORT_SECOND_BYTE:
                return None
            
            slave_address = int(msg_parts[2], 16)
            di_status_byte = int(msg_parts[3], 16)
            # 位序反转
            binary_str = format(di_status_byte, '08b')[::-1]
            
            return {
                "slave_address": slave_address,
                "di_status": list(binary_str),
                "do_status": list(binary_str),  # DI状态即为DO实际状态
                "raw_data": msg
            }
        except Exception as e:
            logger.error(f"DI状态解析异常: {e}")
            return None

# 设备状态
@dataclass
class DeviceState:
    is_powered: bool = False
    last_business_time: Optional[datetime] = None
    power_on_time: Optional[datetime] = None
    total_power_on_seconds: float = 0.0
    business_task_id: Optional[str] = None
    auto_cleanup_enabled: bool = True

# 硬件管理器
class OptimizedHardwareManager:
    def __init__(self, config: dict):
        self.config = config
        self.channels: Dict[str, asyncio.StreamWriter] = {}
        self.device_states: Dict[int, DeviceState] = {}
        self.do_status_cache: Dict[int, Dict[int, bool]] = {}
        self.last_do_query_time: Dict[int, float] = {}
        self.last_di_report_time: Dict[int, float] = {}
        self.protection_task = None
        self.ptz_angles: Dict[int, Dict[str, int]] = {}
        
        # 配置参数
        self.MAX_NO_BUSINESS_DURATION = config.get('protection', {}).get('max_no_business_duration', 600)  # 10分钟
        self.MAX_BUSINESS_DURATION = config.get('protection', {}).get('max_business_duration', 1200)  # 20分钟
        self.DO_QUERY_INTERVAL = config.get('protection', {}).get('do_query_interval', 30.0)  # 30秒
        self.DO_QUERY_TIMEOUT = config.get('protection', {}).get('do_query_timeout', 2.0)
        self.DI_REPORT_TIMEOUT = config.get('protection', {}).get('di_report_timeout', 60.0)  # DI上报超时
        
        self.pending_responses: Dict[str, asyncio.Future] = {}
        self.response_lock = asyncio.Lock()
        
    async def connect_channels(self):
        """连接所有通道"""
        for channel_name, channel_config in self.config.get('channels', {}).items():
            try:
                reader, writer = await asyncio.open_connection(
                    channel_config['host'],
                    channel_config['port']
                )
                self.channels[channel_name] = writer
                
                # 启动数据处理任务
                asyncio.create_task(self._handle_channel_data(channel_name, reader))
                
                # 发送认证数据
                auth_data = bytes.fromhex(channel_config.get('auth', ''))
                if auth_data:
                    writer.write(auth_data)
                    await writer.drain()
                    
                logger.info(f"✓ 认证成功 [{channel_config['host']}:{channel_config['port']}]: {channel_name}")
            except Exception as e:
                logger.error(f"连接通道 {channel_name} 失败: {e}")
    
    async def _handle_channel_data(self, channel_name: str, reader: asyncio.StreamReader):
        """处理通道数据 - 基于Java实现"""
        buffer = b''
        while True:
            try:
                data = await reader.read(1024)
                if not data:
                    break
                
                buffer += data
                
                while len(buffer) > 0:
                    # 1. 检查DI上报（6字节，40 57开头）
                    if len(buffer) >= 6 and buffer[0] == 0x40 and buffer[1] == 0x57:
                        hex_str = buffer[:6].hex()
                        msg = ' '.join(hex_str[i:i+2] for i in range(0, len(hex_str), 2))
                        
                        logger.info(f"检测到DI上报格式响应")
                        result = RelayProtocol.resolve_di_status(msg)
                        if result:
                            await self._update_do_status_from_di(result)
                        buffer = buffer[6:]
                        continue
                    
                    # 2. 检查DO查询响应（6字节）
                    if len(buffer) >= 6:
                        hex_str = buffer[:6].hex()
                        msg = ' '.join(hex_str[i:i+2] for i in range(0, len(hex_str), 2))
                        
                        result = RelayProtocol.resolve_do_status(msg)
                        if result:
                            await self._update_do_status(result)
                            buffer = buffer[6:]
                            continue
                    
                    # 3. 检查云台响应（7字节，ff开头）
                    if len(buffer) >= 7 and buffer[0] == 0xff:
                        hex_str = buffer[:7].hex()
                        msg = ' '.join(hex_str[i:i+2] for i in range(0, len(hex_str), 2))
                        
                        result = PTZProtocol.resolve_ptz_angle(msg)
                        if result:
                            await self._update_ptz_angle(result)
                            buffer = buffer[7:]
                            continue
                    
                    # 4. 处理注册包（8字节）
                    if len(buffer) >= 8 and channel_name not in self.channels:
                        hex_str = buffer[:8].hex()
                        logger.info(f"串口服务器注册包: {hex_str}")
                        buffer = buffer[8:]
                        continue
                    
                    # 5. 未识别的数据，跳过一个字节
                    if len(buffer) > 0:
                        logger.debug(f"未识别的数据: {buffer[0]:02x}")
                        buffer = buffer[1:]
                    else:
                        break
                        
            except Exception as e:
                logger.error(f"处理通道 {channel_name} 数据异常: {e}")
                break
    
    async def _update_do_status_from_di(self, result: dict):
        """从DI上报更新DO状态"""
        slave_addr = result['slave_address']
        do_status = result['do_status']
        
        # 更新DI上报时间
        self.last_di_report_time[slave_addr] = time.time()
        
        # 更新DO状态缓存
        if slave_addr not in self.do_status_cache:
            self.do_status_cache[slave_addr] = {}
        
        for i, status in enumerate(do_status):
            self.do_status_cache[slave_addr][i+1] = (status == '1')
        
        logger.info(f"DI上报解析成功: DO状态={do_status}")
        
        # 检查设备电源状态
        power_do = self.config['protection']['power_control_do']
        is_powered = self.do_status_cache[slave_addr].get(power_do, False)
        await self._update_device_power_state(slave_addr, is_powered)
    
    async def _update_do_status(self, result: dict):
        """更新DO状态"""
        slave_addr = result['slave_address']
        do_status = result['do_status']
        
        if slave_addr not in self.do_status_cache:
            self.do_status_cache[slave_addr] = {}
        
        for i, status in enumerate(do_status):
            self.do_status_cache[slave_addr][i+1] = (status == '1')
        
        # 通知等待的响应
        async with self.response_lock:
            for cmd_hex, future in list(self.pending_responses.items()):
                if not future.done():
                    future.set_result(result)
    
    async def _update_ptz_angle(self, result: dict):
        """更新云台角度"""
        slave_addr = result['slave_address']
        direction = result['direction']
        angle = result['angle']
        
        if slave_addr not in self.ptz_angles:
            self.ptz_angles[slave_addr] = {}
        
        self.ptz_angles[slave_addr][direction] = angle
        logger.info(f"云台从机地址:{slave_addr}-方向:{direction}-角度:{angle}")
        
        # 通知等待的响应
        async with self.response_lock:
            for cmd_hex, future in list(self.pending_responses.items()):
                if not future.done():
                    future.set_result(result)
    
    async def send_command(self, channel_name: str, cmd_hex: str, wait_response: bool = False, timeout: float = 2.0):
        """发送命令"""
        if channel_name not in self.channels:
            raise ValueError(f"通道 {channel_name} 未连接")
        
        try:
            cmd_bytes = bytes.fromhex(cmd_hex)
            writer = self.channels[channel_name]
            
            response_future = None
            if wait_response:
                response_future = asyncio.Future()
                async with self.response_lock:
                    self.pending_responses[cmd_hex] = response_future
            
            writer.write(cmd_bytes)
            await writer.drain()
            
            logger.info(f"发送命令到 {channel_name}: {cmd_hex}")
            
            if wait_response and response_future:
                try:
                    result = await asyncio.wait_for(response_future, timeout)
                    return True, result
                except asyncio.TimeoutError:
                    logger.warning(f"等待响应超时: {cmd_hex}")
                    return False, None
                finally:
                    async with self.response_lock:
                        self.pending_responses.pop(cmd_hex, None)
            
            return True, None
            
        except Exception as e:
            logger.error(f"发送命令失败: {e}")
            return False, None
    
    async def control_do(self, slave_addr: int, do_number: int, state: bool, channel_name: str = "io_channel"):
        """控制DO输出"""
        cmd = RelayProtocol.calc_do_control_cmd(slave_addr, do_number, state)
        success, _ = await self.send_command(channel_name, cmd)
        
        if success:
            # 更新缓存
            if slave_addr not in self.do_status_cache:
                self.do_status_cache[slave_addr] = {}
            self.do_status_cache[slave_addr][do_number] = state
            
            # 如果是电源控制，更新设备状态
            if do_number == self.config['protection']['power_control_do']:
                await self._update_device_power_state(slave_addr, state)
        
        return success
    
    async def query_do_status_async(self, slave_addr: int, channel_name: str = "io_channel") -> Tuple[bool, Optional[Dict[int, bool]]]:
        """异步查询DO状态"""
        current_time = time.time()
        
        # 1. 检查DI上报是否新鲜
        last_di_time = self.last_di_report_time.get(slave_addr, 0)
        if current_time - last_di_time < self.DO_QUERY_INTERVAL:
            # DI上报数据足够新鲜，直接使用缓存
            return True, self.do_status_cache.get(slave_addr, {})
        
        # 2. 检查上次查询时间
        last_query = self.last_do_query_time.get(slave_addr, 0)
        if current_time - last_query < self.DO_QUERY_INTERVAL:
            # 查询间隔未到，使用缓存
            return True, self.do_status_cache.get(slave_addr, {})
        
        # 3. 执行DO查询
        cmd = RelayProtocol.calc_do_search_cmd(slave_addr, 8)
        success, result = await self.send_command(channel_name, cmd, wait_response=True, timeout=self.DO_QUERY_TIMEOUT)
        
        if success and result:
            self.last_do_query_time[slave_addr] = current_time
            return True, self.do_status_cache.get(slave_addr, {})
        
        # 4. 查询失败，但如果有DI上报数据且不太旧，仍然返回缓存
        if last_di_time > 0 and current_time - last_di_time < self.DI_REPORT_TIMEOUT:
            logger.warning(f"DO查询失败，使用DI上报缓存数据（{current_time - last_di_time:.1f}秒前）")
            return True, self.do_status_cache.get(slave_addr, {})
        
        return False, None
    
    async def control_ptz_angle(self, slave_addr: int, angle: int, direction: str, channel_name: str = "ptz_channel"):
        """控制云台角度"""
        ptz_enum = PTZEnum.PAN if direction.upper() == "PAN" else PTZEnum.TILT
        cmd = PTZProtocol.calc_spin_angle_cmd(angle, slave_addr, ptz_enum)
        success, _ = await self.send_command(channel_name, cmd)
        return success
    
    async def set_ptz_speed(self, slave_addr: int, speed: float, direction: str, channel_name: str = "ptz_channel"):
        """设置云台速度"""
        ptz_enum = PTZEnum.PAN if direction.upper() == "PAN" else PTZEnum.TILT
        cmd = PTZProtocol.calc_setup_speed_cmd(speed, slave_addr, ptz_enum)
        success, _ = await self.send_command(channel_name, cmd)
        return success
    
    async def query_ptz_angle(self, slave_addr: int, direction: str, channel_name: str = "ptz_channel"):
        """查询云台角度"""
        ptz_enum = PTZEnum.PAN if direction.upper() == "PAN" else PTZEnum.TILT
        cmd = PTZProtocol.calc_search_angle_cmd(slave_addr, ptz_enum)
        success, result = await self.send_command(channel_name, cmd, wait_response=True)
        
        if success and result:
            return result.get('angle', 0)
        
        # 返回缓存的角度
        return self.ptz_angles.get(slave_addr, {}).get(direction.upper(), 0)
    
    async def repair_kaka(self, slave_addr: int, channel_name: str = "ptz_channel"):
        """修复云台卡顿"""
        # 右转
        cmd = PTZProtocol.right_spin(slave_addr)
        await self.send_command(channel_name, cmd)
        
        # 等待15秒
        await asyncio.sleep(15)
        
        # 停止
        cmd = PTZProtocol.stop(slave_addr)
        await self.send_command(channel_name, cmd)
        
        return True
    
    async def _update_device_power_state(self, device_id: int, is_powered: bool):
        """更新设备电源状态"""
        if device_id not in self.device_states:
            self.device_states[device_id] = DeviceState()
        
        state = self.device_states[device_id]
        
        if is_powered and not state.is_powered:
            # 设备上电
            state.power_on_time = datetime.now()
            logger.info(f"设备 {device_id} 已通电")
        elif not is_powered and state.is_powered:
            # 设备断电
            if state.power_on_time:
                duration = (datetime.now() - state.power_on_time).total_seconds()
                state.total_power_on_seconds += duration
                logger.info(f"设备 {device_id} 已断电，本次通电时长: {duration:.1f}秒")
            state.power_on_time = None
        
        state.is_powered = is_powered
    
    async def update_business_activity(self, device_id: int, task_id: Optional[str] = None):
        """更新业务活动"""
        if device_id not in self.device_states:
            self.device_states[device_id] = DeviceState()
        
        state = self.device_states[device_id]
        state.last_business_time = datetime.now()
        
        if task_id:
            state.business_task_id = task_id
            state.auto_cleanup_enabled = True
            logger.info(f"设备 {device_id} 开始业务任务: {task_id}")
    
    async def complete_business_task(self, device_id: int, task_id: str):
        """完成业务任务"""
        if device_id in self.device_states:
            state = self.device_states[device_id]
            if state.business_task_id == task_id:
                logger.info(f"任务 {task_id} 完成，准备关闭设备 {device_id}")
                state.business_task_id = None
                
                # 自动清理
                if state.auto_cleanup_enabled:
                    await self.shutdown_device(device_id)
    
    async def shutdown_device(self, device_id: int):
        """关闭设备"""
        logger.info(f"关闭设备 {device_id}")
        
        # 先关闭业务DO（1-6）
        for do_num in range(1, 7):
            await self.control_do(device_id, do_num, False)
            await asyncio.sleep(0.1)
        
        # 延时后关闭电源
        await asyncio.sleep(0.5)
        power_do = self.config['protection']['power_control_do']
        await self.control_do(device_id, power_do, False)
    
    async def run_protection_mechanism(self):
        """运行保护机制"""
        check_interval = 30  # 30秒检查一次
        
        while True:
            try:
                await asyncio.sleep(check_interval)
                
                for device_id, state in self.device_states.items():
                    if not state.is_powered:
                        continue
                    
                    # 查询DO状态（优先使用DI上报）
                    success, do_status = await self.query_do_status_async(device_id)
                    if not success:
                        logger.warning(f"查询设备 {device_id} DO状态失败")
                        continue
                    
                    # 检查电源状态
                    power_do = self.config['protection']['power_control_do']
                    is_powered = do_status.get(power_do, False)
                    
                    if not is_powered:
                        state.is_powered = False
                        continue
                    
                    # 计算通电时长
                    if state.power_on_time:
                        power_duration = (datetime.now() - state.power_on_time).total_seconds()
                    else:
                        power_duration = 0
                    
                    # 计算无业务时长
                    if state.last_business_time:
                        no_business_duration = (datetime.now() - state.last_business_time).total_seconds()
                    else:
                        no_business_duration = float('inf')
                    
                    # 保护逻辑
                    if state.business_task_id:
                        # 有业务任务
                        if no_business_duration > self.MAX_BUSINESS_DURATION:
                            logger.warning(f"设备 {device_id} 业务活动超时 ({no_business_duration:.0f}秒)，执行保护关机")
                            await self.shutdown_device(device_id)
                    else:
                        # 无业务任务
                        if no_business_duration > self.MAX_NO_BUSINESS_DURATION:
                            logger.warning(f"设备 {device_id} 无业务活动超时 ({no_business_duration:.0f}秒)，执行保护关机")
                            await self.shutdown_device(device_id)
                            
            except Exception as e:
                logger.error(f"保护机制运行异常: {e}")

# 硬件控制服务
class OptimizedHardwareControlService:
    def __init__(self):
        self.config = self._load_config()
        self.hardware_manager = OptimizedHardwareManager(self.config)
        self.app = web.Application()
        self._setup_routes()
        
    def _load_config(self):
        """加载配置文件"""
        config_path = "/home/<USER>/server/edge-server/src/hardware/hardware_config.yaml"
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"加载配置文件: {config_path}")
            return config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            # 默认配置
            return {
                "channels": {
                    "io_channel": {"host": "*************", "port": 4001, "auth": ""},
                    "ptz_channel": {"host": "*************", "port": 4002, "auth": ""}
                },
                "protection": {
                    "max_no_business_duration": 600,  # 10分钟
                    "max_business_duration": 1200,    # 20分钟
                    "power_control_do": 8,
                    "do_query_interval": 30.0,        # 30秒
                    "do_query_timeout": 2.0,
                    "di_report_timeout": 60.0         # DI上报超时
                }
            }
    
    def _setup_routes(self):
        """设置路由"""
        # 健康检查
        self.app.router.add_get('/health', self.health_check)
        
        # 继电器控制
        self.app.router.add_post('/hardware/relay/control', self.relay_control)
        self.app.router.add_get('/hardware/relay/status', self.relay_status)
        self.app.router.add_post('/hardware/relay/all_on', self.relay_all_on)
        self.app.router.add_post('/hardware/relay/all_off', self.relay_all_off)
        
        # 云台控制
        self.app.router.add_post('/hardware/ptz/angle', self.ptz_angle_control)
        self.app.router.add_post('/hardware/ptz/speed', self.ptz_speed_control)
        self.app.router.add_get('/hardware/ptz/angle', self.ptz_angle_query)
        self.app.router.add_post('/hardware/ptz/repair_kaka', self.ptz_repair_kaka)
        
        # 激光雷达控制
        self.app.router.add_post('/hardware/lidar/control', self.lidar_control)
        
        # 业务管理
        self.app.router.add_post('/hardware/business/start', self.business_start)
        self.app.router.add_post('/hardware/business/update', self.business_update)
        self.app.router.add_post('/hardware/business/complete', self.business_complete)
        
        # 兼容旧API
        self.app.router.add_post('/api/hardware/relay/control', self.relay_control)
        self.app.router.add_get('/api/hardware/relay/status', self.relay_status)
        self.app.router.add_post('/api/hardware/relay/all_on', self.relay_all_on)
        self.app.router.add_post('/api/hardware/relay/all_off', self.relay_all_off)
        self.app.router.add_post('/api/hardware/ptz/control', self.ptz_angle_control)
        self.app.router.add_get('/api/hardware/ptz/status', self.ptz_angle_query)
        self.app.router.add_post('/api/hardware/lidar/control', self.lidar_control)
        
        # Java兼容API
        self.app.router.add_post('/test/relay/doControl', self.relay_control_java)
        self.app.router.add_get('/test/relay/doCacheSearch', self.relay_cache_search_java)
        self.app.router.add_get('/test/relay/doSearch', self.relay_search_java)
        self.app.router.add_post('/test/ptz/sendSpinCmd', self.ptz_spin_java)
        self.app.router.add_post('/test/ptz/sendSetupSpeedCmd', self.ptz_speed_java)
        self.app.router.add_get('/test/ptz/getAngleValue', self.ptz_angle_java)
        self.app.router.add_get('/test/ptz/repairKaka', self.ptz_repair_java)
    
    async def health_check(self, request):
        """健康检查"""
        return web.json_response({
            "status": "healthy",
            "service": "hardware_control_optimized",
            "timestamp": datetime.now().isoformat(),
            "channels": list(self.hardware_manager.channels.keys()),
            "devices": list(self.hardware_manager.device_states.keys()),
            "version": "2.0-java-based"
        })
    
    async def relay_control(self, request):
        """继电器控制"""
        try:
            data = await request.json()
            device_id = data.get('device_id', 1)
            channel = data.get('channel', 1)
            state = data.get('state', False)
            
            success = await self.hardware_manager.control_do(device_id, channel, state)
            
            return web.json_response({
                "success": success,
                "device_id": device_id,
                "channel": channel,
                "state": state
            })
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def relay_status(self, request):
        """查询继电器状态"""
        try:
            device_id = int(request.query.get('device_id', 1))
            
            success, status = await self.hardware_manager.query_do_status_async(device_id)
            
            if success:
                return web.json_response({
                    "success": True,
                    "device_id": device_id,
                    "do_status": {k: v for k, v in status.items()}
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": "查询失败"
                }, status=500)
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def relay_all_on(self, request):
        """全部开启"""
        try:
            data = await request.json()
            device_id = data.get('device_id', 1)
            
            # 更新业务活动
            await self.hardware_manager.update_business_activity(device_id)
            
            # 依次开启
            for channel in range(1, 9):
                await self.hardware_manager.control_do(device_id, channel, True)
                await asyncio.sleep(0.1)
            
            return web.json_response({
                "success": True,
                "device_id": device_id,
                "action": "all_on"
            })
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def relay_all_off(self, request):
        """全部关闭"""
        try:
            data = await request.json()
            device_id = data.get('device_id', 1)
            
            # 依次关闭
            for channel in range(1, 9):
                await self.hardware_manager.control_do(device_id, channel, False)
                await asyncio.sleep(0.1)
            
            return web.json_response({
                "success": True,
                "device_id": device_id,
                "action": "all_off"
            })
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def ptz_angle_control(self, request):
        """云台角度控制"""
        try:
            data = await request.json()
            device_id = data.get('device_id', 30)
            angle = data.get('angle', 0)
            direction = data.get('direction', 'PAN')
            
            success = await self.hardware_manager.control_ptz_angle(device_id, angle, direction)
            
            return web.json_response({
                "success": success,
                "device_id": device_id,
                "angle": angle,
                "direction": direction
            })
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def ptz_speed_control(self, request):
        """云台速度控制"""
        try:
            data = await request.json()
            device_id = data.get('device_id', 30)
            speed = data.get('speed', 1.0)
            direction = data.get('direction', 'PAN')
            
            success = await self.hardware_manager.set_ptz_speed(device_id, speed, direction)
            
            return web.json_response({
                "success": success,
                "device_id": device_id,
                "speed": speed,
                "direction": direction
            })
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def ptz_angle_query(self, request):
        """查询云台角度"""
        try:
            device_id = int(request.query.get('device_id', 30))
            direction = request.query.get('direction', 'PAN')
            
            angle = await self.hardware_manager.query_ptz_angle(device_id, direction)
            
            return web.json_response({
                "success": True,
                "device_id": device_id,
                "direction": direction,
                "angle": angle
            })
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def ptz_repair_kaka(self, request):
        """修复云台卡顿"""
        try:
            data = await request.json()
            device_id = data.get('device_id', 30)
            channel_name = data.get('channel_name', 'ptz_channel')
            
            success = await self.hardware_manager.repair_kaka(device_id, channel_name)
            
            return web.json_response({
                "success": success,
                "device_id": device_id,
                "message": "云台修复完成"
            })
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def lidar_control(self, request):
        """激光雷达控制"""
        try:
            data = await request.json()
            action = data.get('action', 'stop')
            device_id = data.get('device_id', 1)
            task_id = data.get('task_id')
            
            if action == 'start':
                # 先开电源
                await self.hardware_manager.control_do(device_id, 8, True)
                await asyncio.sleep(1)
                # 再开激光雷达
                await self.hardware_manager.control_do(device_id, 1, True)
                # 更新业务活动
                await self.hardware_manager.update_business_activity(device_id, task_id)
                message = "激光雷达已启动"
            else:
                # 先关激光雷达
                await self.hardware_manager.control_do(device_id, 1, False)
                await asyncio.sleep(1)
                # 再关电源
                await self.hardware_manager.control_do(device_id, 8, False)
                message = "激光雷达已停止"
            
            return web.json_response({
                "success": True,
                "action": action,
                "message": message
            })
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def business_start(self, request):
        """开始业务活动"""
        try:
            data = await request.json()
            device_id = data.get('device_id', 1)
            task_id = data.get('task_id', str(time.time()))
            
            await self.hardware_manager.update_business_activity(device_id, task_id)
            
            return web.json_response({
                "success": True,
                "device_id": device_id,
                "task_id": task_id,
                "message": "业务活动已开始"
            })
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def business_update(self, request):
        """更新业务活动"""
        try:
            data = await request.json()
            device_id = data.get('device_id', 1)
            
            await self.hardware_manager.update_business_activity(device_id)
            
            return web.json_response({
                "success": True,
                "device_id": device_id,
                "message": "业务活动已更新"
            })
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def business_complete(self, request):
        """完成业务任务"""
        try:
            data = await request.json()
            device_id = data.get('device_id', 1)
            task_id = data.get('task_id')
            
            await self.hardware_manager.complete_business_task(device_id, task_id)
            
            return web.json_response({
                "success": True,
                "device_id": device_id,
                "task_id": task_id,
                "message": "业务任务已完成"
            })
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    # Java兼容API
    async def relay_control_java(self, request):
        """Java兼容：继电器控制"""
        try:
            data = await request.json()
            channel_name = data.get('channelName', 'io_channel')
            f = data.get('f', False)
            do_number = data.get('doNumber', 1)
            address = data.get('address', 1)
            
            success = await self.hardware_manager.control_do(address, do_number, f, channel_name)
            
            if f:
                cmd = RelayProtocol.calc_do_control_cmd(address, do_number, True)
            else:
                cmd = RelayProtocol.calc_do_control_cmd(address, do_number, False)
            
            return web.Response(text=cmd)
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def relay_cache_search_java(self, request):
        """Java兼容：缓存查询"""
        try:
            address = int(request.query.get('address', 1))
            do_number = int(request.query.get('doNumber', 8))
            
            cache = self.hardware_manager.do_status_cache.get(address, {})
            status = cache.get(do_number, False)
            
            return web.Response(text=str(status).lower())
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def relay_search_java(self, request):
        """Java兼容：DO查询"""
        try:
            address = int(request.query.get('address', 1))
            channel_name = request.query.get('channelName', 'io_channel')
            do_number = int(request.query.get('doNumber', 8))
            
            cmd = RelayProtocol.calc_do_search_cmd(address, do_number)
            await self.hardware_manager.send_command(channel_name, cmd)
            
            return web.Response(text="true")
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def ptz_spin_java(self, request):
        """Java兼容：云台角度控制"""
        try:
            data = await request.json()
            channel_name = data.get('channelName', 'ptz_channel')
            direction = data.get('direction', 'PAN')
            angle = data.get('angle', 0)
            address = data.get('address', 30)
            
            ptz_enum = PTZEnum.PAN if direction == 'PAN' else PTZEnum.TILT
            cmd = PTZProtocol.calc_spin_angle_cmd(angle, address, ptz_enum)
            await self.hardware_manager.send_command(channel_name, cmd)
            
            return web.Response(text="true")
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def ptz_speed_java(self, request):
        """Java兼容：云台速度控制"""
        try:
            data = await request.json()
            channel_name = data.get('channelName', 'ptz_channel')
            direction = data.get('direction', 'PAN')
            speed = data.get('speed', 1.0)
            address = data.get('address', 30)
            
            ptz_enum = PTZEnum.PAN if direction == 'PAN' else PTZEnum.TILT
            cmd = PTZProtocol.calc_setup_speed_cmd(speed, address, ptz_enum)
            await self.hardware_manager.send_command(channel_name, cmd)
            
            return web.Response(text="true")
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def ptz_angle_java(self, request):
        """Java兼容：查询云台角度"""
        try:
            slave_address = int(request.query.get('slaveAddress', 30))
            direction = request.query.get('direction', 'PAN')
            
            angle = await self.hardware_manager.query_ptz_angle(slave_address, direction)
            
            return web.Response(text=str(angle))
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def ptz_repair_java(self, request):
        """Java兼容：修复云台卡顿"""
        try:
            address = int(request.query.get('address', 30))
            channel_name = request.query.get('channelName', 'ptz_channel')
            
            success = await self.hardware_manager.repair_kaka(address, channel_name)
            
            return web.Response(text="true")
        except Exception as e:
            return web.json_response({"error": str(e)}, status=400)
    
    async def start(self):
        """启动服务"""
        # 连接通道
        await self.hardware_manager.connect_channels()
        
        # 启动保护机制
        self.hardware_manager.protection_task = asyncio.create_task(
            self.hardware_manager.run_protection_mechanism()
        )
        
        # 启动Web服务
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, '0.0.0.0', 7080)
        await site.start()
        
        logger.info("=" * 60)
        logger.info("优化版硬件控制服务已启动")
        logger.info("REST API: http://0.0.0.0:7080")
        logger.info("基于Java协议的完整实现")
        logger.info("DI主动上报支持")
        logger.info("保护机制：无业务10分钟/有业务20分钟")
        logger.info("=" * 60)

async def main():
    service = OptimizedHardwareControlService()
    await service.start()
    
    try:
        await asyncio.Event().wait()
    except KeyboardInterrupt:
        logger.info("服务正在关闭...")

if __name__ == "__main__":
    asyncio.run(main())