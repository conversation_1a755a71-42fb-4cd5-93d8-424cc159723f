# 硬件控制模块项目记忆文档

## 项目概述
- **目标**: 将硬件控制功能从中央服务器迁移到边缘服务器，减少延迟
- **原理**: 串口服务器作为TCP客户端直接连接边缘服务器
- **架构**: Netty TCP Server + REST API + 硬件控制逻辑

## 重要配置
- **边缘服务器IP**: *************
- **串口服务器IP**: *************
- **Netty端口**: 7100
- **REST API端口**: 7080（Python）/ 7070（Java）

## 硬件连接
- **激光雷达**: DO1，IP: *************
- **云台**: DO2
- **IO控制通道认证包**: d4ad2070b92f0000
- **云台控制通道认证包**: 0000000000000000

## 开发历程

### 第一阶段：理解和分析
- 分析了Java后端代码，理解了Netty服务器和串口通信机制
- 发现串口服务器是TCP客户端，而不是服务器
- 理解了8字节认证包机制和双通道架构

### 第二阶段：架构设计
- 创建了三份架构文档（Python迁移、开发计划、简化迁移）
- 最终决定保留Java代码，只做必要的清理

### 第三阶段：代码开发
- 创建了HardwareManager统一管理硬件
- 实现了PTZSpeedManager解决速度查询问题
- 开发了ScanService实现扫描业务逻辑
- 创建了EnhancedDeviceController提供REST API

### 第四阶段：测试验证
- 发现Java版本问题（需要Java 17）
- 遇到Spring Boot依赖缺失问题
- 创建Python实现作为备选方案
- 成功连接串口服务器并验证IO控制

### 第五阶段：对比分析
- 创建了完整的Python硬件控制服务器
- 编写了详细的Java vs Python对比报告
- 结论：对于边缘服务器场景，Python更适合

## 技术要点

### 1. Netty服务器
- 监听7100端口
- 处理8字节认证包
- 维护双通道连接
- 转发控制命令

### 2. 串口通信
- Modbus RTU over TCP（IO控制）
- Pelco-D协议（云台控制）
- RS485参数：9600,8,N,1

### 3. 业务逻辑
- 定点扫描：10Hz频率
- 地形扫描：累积模式
- 安全机制：任务结束必须断电

### 4. REST API
```
GET  /                    # 服务信息
GET  /health             # 健康检查
POST /device/lidar/start # 启动激光雷达
POST /device/ptz/start   # 启动云台
POST /io/control         # IO控制
POST /ptz/angle          # 设置云台角度
POST /scan/point         # 定点扫描
POST /scan/terrain       # 地形扫描
```

## 已解决的问题
1. **云台速度查询**: Pelco-D不支持，创建速度映射表
2. **Java版本兼容**: 升级到Java 17
3. **串口服务器连接**: 理解TCP客户端模式
4. **IO控制验证**: 成功控制DO1

## 待解决的问题
1. **Java依赖问题**: Maven下载超时，Spring Boot无法运行
2. **云台响应解析**: 需要实现完整的响应处理
3. **激光雷达集成**: 需要真实设备测试

## 实施建议

### 短期方案（推荐）
使用Python实现：
```bash
cd /home/<USER>/edge-server/modules/hardware
./run_python_server.sh
```

### 长期方案
1. 完善Python实现的所有功能
2. 添加完整的错误处理和日志
3. 创建systemd服务实现自启动
4. 集成到边缘服务器整体架构

## 测试步骤
1. 启动Python服务器
2. 等待串口服务器连接
3. 运行测试脚本：`python3 test_hardware_api.py`
4. 验证所有功能正常

## 注意事项
1. **安全第一**: 测试结束必须关闭所有设备
2. **网络检查**: 确保串口服务器网络通畅
3. **端口冲突**: 检查7100和7080端口未被占用
4. **日志记录**: 所有操作都有详细日志

## 版本历史
- v1.0: 初始Java实现
- v1.1: 添加业务逻辑和REST API
- v1.2: 修复云台速度查询
- v2.0: 完整Python实现
- v2.1: 对比分析和优化建议
- v2.5: 基于Java原始协议优化硬件控制服务
- v2.6: 完成Java硬件控制协议深度分析
- v2.7: 完成硬件控制完整测试验证
- v2.8: 实现真实硬件测试和雷达自动保护机制
- v2.9: 完成PTZ全部8个功能验证和状态管理方案

## PTZ控制功能确认（8个功能）
1. **水平角度设置** ✅ - POST /api/ptz/angle (axis="pan")
2. **水平角度查询** ✅ - GET /api/ptz/angle (axis="pan")
3. **垂直角度设置** ✅ - POST /api/ptz/angle (axis="tilt")
4. **垂直角度查询** ✅ - GET /api/ptz/angle (axis="tilt")
5. **水平速度设置** ✅ - POST /api/ptz/speed (axis="pan")
6. **水平速度查询** ⚠️ - GET /api/ptz/speed (axis="pan") [基于缓存]
7. **垂直速度设置** ✅ - POST /api/ptz/speed (axis="tilt")
8. **垂直速度查询** ⚠️ - GET /api/ptz/speed (axis="tilt") [基于缓存]

## 状态管理和RPC集成方案

### 核心组件
1. **DeviceStateManager** - 设备状态管理器
   - IO状态管理（6个通道）
   - PTZ状态管理（角度、速度、运动状态）
   - 业务状态管理（扫描、紧急停止）
   - Redis持久化支持

2. **EdgeControllerRPCService** - RPC服务实现
   - JSON-RPC 2.0协议
   - 支持批量请求
   - 完整的方法路由
   - 错误处理机制

3. **状态同步机制**
   - WebSocket实时推送
   - 定期状态同步
   - 事件通知系统

### RPC方法列表
- io.control - IO控制
- io.status - IO状态查询
- ptz.setAngle - 设置PTZ角度
- ptz.getAngle - 查询PTZ角度
- ptz.setSpeed - 设置PTZ速度
- ptz.getSpeed - 查询PTZ速度
- device.getStatus - 获取设备状态
- scan.start - 开始扫描
- scan.stop - 停止扫描
- emergency.stop - 紧急停止

## 下一步计划
1. 实现PTZ速度实时查询协议
2. 部署RPC服务到边缘服务器
3. 与中央控制系统集成测试
4. 添加认证和权限控制
5. 实现WebSocket状态推送
6. 性能优化和压力测试