"""
硬件控制模块
提供IO控制、PTZ控制和集成扫描功能
"""

from .hardware_module import HardwareControlModule
from .integrated_scan_service import IntegratedScanService, ScanMode, DataAcquisitionMode

# 检查是否存在task_bus_adapter
try:
    from .task_bus_adapter import HardwareTaskAdapter
    __all__ = [
        'HardwareControlModule',
        'HardwareTaskAdapter',
        'IntegratedScanService',
        'ScanMode',
        'DataAcquisitionMode'
    ]
except ImportError:
    __all__ = [
        'HardwareControlModule',
        'IntegratedScanService',
        'ScanMode',
        'DataAcquisitionMode'
    ]