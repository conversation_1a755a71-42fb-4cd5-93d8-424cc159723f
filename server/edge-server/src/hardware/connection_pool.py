"""
硬件通信连接池管理器
提供连接复用、健康检查和负载均衡
"""

import asyncio
import logging
import time
from typing import Dict, Optional, List, Any, Callable
from dataclasses import dataclass
from datetime import datetime, timedelta
from collections import defaultdict
import aiohttp

logger = logging.getLogger(__name__)


@dataclass
class ConnectionStats:
    """连接统计信息"""
    created_at: datetime
    last_used: datetime
    request_count: int = 0
    error_count: int = 0
    total_response_time: float = 0.0
    
    @property
    def avg_response_time(self) -> float:
        """平均响应时间"""
        if self.request_count == 0:
            return 0.0
        return self.total_response_time / self.request_count
    
    @property
    def error_rate(self) -> float:
        """错误率"""
        total = self.request_count + self.error_count
        if total == 0:
            return 0.0
        return self.error_count / total
    
    @property
    def health_score(self) -> float:
        """健康分数 (0-1)"""
        # 基于错误率和响应时间计算
        error_penalty = 1.0 - self.error_rate
        
        # 响应时间评分（假设1秒以内是好的）
        if self.avg_response_time == 0:
            time_score = 1.0
        else:
            time_score = max(0, 1.0 - (self.avg_response_time - 1.0))
        
        # 综合评分
        return error_penalty * 0.7 + time_score * 0.3


class ConnectionPool:
    """通用连接池基类"""
    
    def __init__(self, 
                 factory: Callable,
                 max_size: int = 10,
                 min_size: int = 2,
                 max_idle_time: int = 300,
                 health_check_interval: int = 60):
        """
        初始化连接池
        
        Args:
            factory: 连接工厂函数
            max_size: 最大连接数
            min_size: 最小连接数
            max_idle_time: 最大空闲时间（秒）
            health_check_interval: 健康检查间隔（秒）
        """
        self.factory = factory
        self.max_size = max_size
        self.min_size = min_size
        self.max_idle_time = max_idle_time
        self.health_check_interval = health_check_interval
        
        self._pool: List[Any] = []
        self._in_use: Dict[Any, ConnectionStats] = {}
        self._stats: Dict[Any, ConnectionStats] = {}
        self._lock = asyncio.Lock()
        self._closed = False
        self._health_check_task = None
        
    async def start(self):
        """启动连接池"""
        # 创建最小连接数
        for _ in range(self.min_size):
            conn = await self.factory()
            if conn:
                self._pool.append(conn)
                self._stats[conn] = ConnectionStats(
                    created_at=datetime.now(),
                    last_used=datetime.now()
                )
        
        # 启动健康检查任务
        self._health_check_task = asyncio.create_task(self._health_check_loop())
        logger.info(f"连接池已启动，初始连接数: {len(self._pool)}")
    
    async def stop(self):
        """停止连接池"""
        self._closed = True
        
        # 取消健康检查
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有连接
        async with self._lock:
            for conn in self._pool + list(self._in_use.keys()):
                await self._close_connection(conn)
            
            self._pool.clear()
            self._in_use.clear()
            self._stats.clear()
        
        logger.info("连接池已停止")
    
    async def acquire(self) -> Any:
        """获取连接"""
        if self._closed:
            raise RuntimeError("连接池已关闭")
        
        async with self._lock:
            # 尝试从池中获取连接
            while self._pool:
                conn = self._pool.pop()
                if await self._is_connection_alive(conn):
                    self._in_use[conn] = self._stats[conn]
                    self._stats[conn].last_used = datetime.now()
                    return conn
                else:
                    await self._close_connection(conn)
            
            # 如果池中没有连接，创建新连接
            if len(self._in_use) < self.max_size:
                conn = await self.factory()
                if conn:
                    stats = ConnectionStats(
                        created_at=datetime.now(),
                        last_used=datetime.now()
                    )
                    self._in_use[conn] = stats
                    self._stats[conn] = stats
                    return conn
            
            # 等待连接释放
            raise RuntimeError("连接池已满")
    
    async def release(self, conn: Any):
        """释放连接"""
        async with self._lock:
            if conn in self._in_use:
                del self._in_use[conn]
                
                # 检查连接是否还活着
                if await self._is_connection_alive(conn):
                    self._pool.append(conn)
                else:
                    await self._close_connection(conn)
    
    def record_success(self, conn: Any, response_time: float):
        """记录成功请求"""
        if conn in self._stats:
            stats = self._stats[conn]
            stats.request_count += 1
            stats.total_response_time += response_time
            stats.last_used = datetime.now()
    
    def record_error(self, conn: Any):
        """记录错误"""
        if conn in self._stats:
            stats = self._stats[conn]
            stats.error_count += 1
            stats.last_used = datetime.now()
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while not self._closed:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_check()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"健康检查失败: {e}")
    
    async def _perform_health_check(self):
        """执行健康检查"""
        async with self._lock:
            # 检查空闲连接
            to_remove = []
            for conn in self._pool[:]:
                stats = self._stats.get(conn)
                if stats:
                    # 检查空闲时间
                    idle_time = (datetime.now() - stats.last_used).total_seconds()
                    if idle_time > self.max_idle_time:
                        to_remove.append(conn)
                    # 检查健康状态
                    elif not await self._is_connection_alive(conn):
                        to_remove.append(conn)
            
            # 移除不健康的连接
            for conn in to_remove:
                self._pool.remove(conn)
                await self._close_connection(conn)
            
            # 补充最小连接数
            current_total = len(self._pool) + len(self._in_use)
            if current_total < self.min_size:
                for _ in range(self.min_size - current_total):
                    try:
                        conn = await self.factory()
                        if conn:
                            self._pool.append(conn)
                            self._stats[conn] = ConnectionStats(
                                created_at=datetime.now(),
                                last_used=datetime.now()
                            )
                    except Exception as e:
                        logger.error(f"创建连接失败: {e}")
    
    async def _is_connection_alive(self, conn: Any) -> bool:
        """检查连接是否存活（子类实现）"""
        return True
    
    async def _close_connection(self, conn: Any):
        """关闭连接（子类实现）"""
        if conn in self._stats:
            del self._stats[conn]
    
    def get_stats(self) -> Dict:
        """获取连接池统计信息"""
        return {
            "pool_size": len(self._pool),
            "in_use": len(self._in_use),
            "total": len(self._pool) + len(self._in_use),
            "max_size": self.max_size,
            "connections": [
                {
                    "in_use": conn in self._in_use,
                    "health_score": stats.health_score,
                    "request_count": stats.request_count,
                    "error_count": stats.error_count,
                    "avg_response_time": stats.avg_response_time,
                    "created_at": stats.created_at.isoformat(),
                    "last_used": stats.last_used.isoformat()
                }
                for conn, stats in self._stats.items()
            ]
        }


class HTTPConnectionPool(ConnectionPool):
    """HTTP连接池实现"""
    
    async def _is_connection_alive(self, session: aiohttp.ClientSession) -> bool:
        """检查HTTP会话是否存活"""
        return not session.closed
    
    async def _close_connection(self, session: aiohttp.ClientSession):
        """关闭HTTP会话"""
        if not session.closed:
            await session.close()
        await super()._close_connection(session)


class SerialConnectionPool:
    """串口连接池（通过网络服务器）"""
    
    def __init__(self, base_url: str, max_connections: int = 5):
        self.base_url = base_url
        self.max_connections = max_connections
        self.connections = []
        self.current_index = 0
        self._lock = asyncio.Lock()
        
        # 创建HTTP连接池
        async def factory():
            connector = aiohttp.TCPConnector(
                limit=1,
                force_close=False,
                keepalive_timeout=30
            )
            session = aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=10)
            )
            return session
        
        self.http_pool = HTTPConnectionPool(
            factory=factory,
            max_size=max_connections,
            min_size=2
        )
    
    async def start(self):
        """启动连接池"""
        await self.http_pool.start()
    
    async def stop(self):
        """停止连接池"""
        await self.http_pool.stop()
    
    async def execute(self, method: str, endpoint: str, **kwargs) -> Dict:
        """执行请求"""
        session = await self.http_pool.acquire()
        
        try:
            start_time = time.time()
            url = f"{self.base_url}{endpoint}"
            
            async with session.request(method, url, **kwargs) as response:
                data = await response.json()
                
                # 记录成功
                response_time = time.time() - start_time
                self.http_pool.record_success(session, response_time)
                
                return data
                
        except Exception as e:
            # 记录错误
            self.http_pool.record_error(session)
            raise
        finally:
            # 释放连接
            await self.http_pool.release(session)
    
    def get_stats(self) -> Dict:
        """获取统计信息"""
        return self.http_pool.get_stats()