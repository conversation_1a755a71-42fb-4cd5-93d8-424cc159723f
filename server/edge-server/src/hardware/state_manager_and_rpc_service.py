#!/usr/bin/env python3
"""
设备状态管理器和RPC服务实现
用于中央控制系统与边缘控制器的通信
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional, List, Callable
from enum import Enum
import aiohttp
from aiohttp import web
import threading
import redis
from dataclasses import dataclass, asdict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@dataclass
class IOState:
    """IO状态数据类"""
    channel: int
    state: bool
    device: str
    last_update: str
    voltage: Optional[float] = None
    current: Optional[float] = None


@dataclass
class PTZAxisState:
    """PTZ轴状态数据类"""
    angle: float
    speed: float
    speed_index: int
    target_angle: Optional[float] = None
    is_moving: bool = False
    last_update: str = ""
    error_count: int = 0


@dataclass
class BusinessState:
    """业务状态数据类"""
    scanning: bool = False
    scan_type: Optional[str] = None  # "point" or "terrain"
    scan_params: Dict[str, Any] = None
    emergency_stop: bool = False
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    
    def __post_init__(self):
        if self.scan_params is None:
            self.scan_params = {}


class DeviceStateManager:
    """设备状态管理器"""
    
    def __init__(self, redis_host: str = None, redis_port: int = 6379):
        """
        初始化状态管理器
        
        Args:
            redis_host: Redis主机地址，None则使用内存存储
            redis_port: Redis端口
        """
        self.use_redis = redis_host is not None
        self.redis_client = None
        
        if self.use_redis:
            try:
                self.redis_client = redis.Redis(
                    host=redis_host, 
                    port=redis_port, 
                    decode_responses=True
                )
                self.redis_client.ping()
                logger.info(f"已连接到Redis: {redis_host}:{redis_port}")
            except Exception as e:
                logger.warning(f"Redis连接失败，使用内存存储: {e}")
                self.use_redis = False
        
        # 内存存储
        self.io_states: Dict[int, IOState] = {}
        self.ptz_states: Dict[int, Dict[str, PTZAxisState]] = {}
        self.business_state = BusinessState()
        self.lock = threading.Lock()
        
        # 状态变更回调
        self.state_change_callbacks: List[Callable] = []
        
        # 初始化默认状态
        self._init_default_states()
    
    def _init_default_states(self):
        """初始化默认状态"""
        # 初始化IO状态
        io_devices = {
            1: "激光雷达",
            2: "云台",
            3: "照明灯",
            4: "报警器",
            5: "备用设备1",
            6: "备用设备2"
        }
        
        for channel, device in io_devices.items():
            self.io_states[channel] = IOState(
                channel=channel,
                state=False,
                device=device,
                last_update=datetime.now().isoformat()
            )
        
        # 初始化PTZ状态（默认地址30）
        self.ptz_states[30] = {
            "pan": PTZAxisState(
                angle=0.0,
                speed=10.0,
                speed_index=0,
                last_update=datetime.now().isoformat()
            ),
            "tilt": PTZAxisState(
                angle=0.0,
                speed=5.0,
                speed_index=0,
                last_update=datetime.now().isoformat()
            )
        }
    
    def update_io_state(self, channel: int, state: bool, **kwargs):
        """更新IO状态"""
        with self.lock:
            if channel in self.io_states:
                self.io_states[channel].state = state
                self.io_states[channel].last_update = datetime.now().isoformat()
                
                # 更新额外参数
                for key, value in kwargs.items():
                    if hasattr(self.io_states[channel], key):
                        setattr(self.io_states[channel], key, value)
                
                # 持久化到Redis
                if self.use_redis:
                    self._save_to_redis(f"io:{channel}", asdict(self.io_states[channel]))
                
                # 触发状态变更回调
                self._trigger_callbacks("io_changed", {
                    "channel": channel,
                    "state": state,
                    "device": self.io_states[channel].device
                })
    
    def update_ptz_state(self, ptz_addr: int, axis: str, **kwargs):
        """更新PTZ状态"""
        with self.lock:
            if ptz_addr not in self.ptz_states:
                self.ptz_states[ptz_addr] = {
                    "pan": PTZAxisState(0, 10, 0),
                    "tilt": PTZAxisState(0, 5, 0)
                }
            
            if axis in self.ptz_states[ptz_addr]:
                axis_state = self.ptz_states[ptz_addr][axis]
                
                # 更新参数
                for key, value in kwargs.items():
                    if hasattr(axis_state, key):
                        setattr(axis_state, key, value)
                
                axis_state.last_update = datetime.now().isoformat()
                
                # 检查是否到达目标角度
                if axis_state.target_angle is not None and axis_state.is_moving:
                    if abs(axis_state.angle - axis_state.target_angle) < 0.5:
                        axis_state.is_moving = False
                        axis_state.target_angle = None
                        self._trigger_callbacks("ptz_angle_reached", {
                            "ptz_addr": ptz_addr,
                            "axis": axis,
                            "angle": axis_state.angle
                        })
                
                # 持久化到Redis
                if self.use_redis:
                    self._save_to_redis(
                        f"ptz:{ptz_addr}:{axis}", 
                        asdict(axis_state)
                    )
    
    def update_business_state(self, **kwargs):
        """更新业务状态"""
        with self.lock:
            for key, value in kwargs.items():
                if hasattr(self.business_state, key):
                    setattr(self.business_state, key, value)
            
            # 持久化到Redis
            if self.use_redis:
                self._save_to_redis("business", asdict(self.business_state))
            
            # 触发回调
            self._trigger_callbacks("business_changed", asdict(self.business_state))
    
    def get_full_state(self) -> Dict[str, Any]:
        """获取完整状态"""
        with self.lock:
            return {
                "io_states": {
                    ch: asdict(state) for ch, state in self.io_states.items()
                },
                "ptz_states": {
                    addr: {
                        axis: asdict(state) for axis, state in states.items()
                    } for addr, states in self.ptz_states.items()
                },
                "business_state": asdict(self.business_state),
                "timestamp": datetime.now().isoformat()
            }
    
    def add_state_change_callback(self, callback: Callable):
        """添加状态变更回调"""
        self.state_change_callbacks.append(callback)
    
    def _trigger_callbacks(self, event_type: str, data: Dict[str, Any]):
        """触发状态变更回调"""
        for callback in self.state_change_callbacks:
            try:
                asyncio.create_task(callback(event_type, data))
            except Exception as e:
                logger.error(f"回调执行错误: {e}")
    
    def _save_to_redis(self, key: str, data: Dict):
        """保存到Redis"""
        if self.redis_client:
            try:
                self.redis_client.set(
                    f"edge_controller:{key}",
                    json.dumps(data),
                    ex=3600  # 1小时过期
                )
            except Exception as e:
                logger.error(f"Redis保存错误: {e}")
    
    def load_from_redis(self):
        """从Redis加载状态"""
        if not self.redis_client:
            return
        
        try:
            # 加载IO状态
            for key in self.redis_client.scan_iter("edge_controller:io:*"):
                channel = int(key.split(":")[-1])
                data = json.loads(self.redis_client.get(key))
                self.io_states[channel] = IOState(**data)
            
            # 加载PTZ状态
            for key in self.redis_client.scan_iter("edge_controller:ptz:*"):
                parts = key.split(":")
                addr = int(parts[-2])
                axis = parts[-1]
                
                if addr not in self.ptz_states:
                    self.ptz_states[addr] = {}
                
                data = json.loads(self.redis_client.get(key))
                self.ptz_states[addr][axis] = PTZAxisState(**data)
            
            # 加载业务状态
            business_data = self.redis_client.get("edge_controller:business")
            if business_data:
                data = json.loads(business_data)
                self.business_state = BusinessState(**data)
            
            logger.info("从Redis加载状态成功")
        except Exception as e:
            logger.error(f"从Redis加载状态失败: {e}")


class EdgeControllerRPCService:
    """边缘控制器RPC服务"""
    
    def __init__(self, hardware_manager, state_manager: DeviceStateManager):
        """
        初始化RPC服务
        
        Args:
            hardware_manager: 硬件管理器实例
            state_manager: 状态管理器实例
        """
        self.hardware_manager = hardware_manager
        self.state_manager = state_manager
        self.request_id_counter = 0
        self.pending_requests = {}
        
        # RPC方法注册
        self.rpc_methods = {
            # IO控制
            "io.control": self.handle_io_control,
            "io.status": self.handle_io_status,
            "io.batch_control": self.handle_io_batch_control,
            
            # PTZ控制
            "ptz.setAngle": self.handle_ptz_set_angle,
            "ptz.getAngle": self.handle_ptz_get_angle,
            "ptz.setSpeed": self.handle_ptz_set_speed,
            "ptz.getSpeed": self.handle_ptz_get_speed,
            "ptz.stop": self.handle_ptz_stop,
            "ptz.home": self.handle_ptz_home,
            
            # 设备管理
            "device.getStatus": self.handle_get_device_status,
            "device.power": self.handle_device_power,
            "device.emergency_stop": self.handle_emergency_stop,
            
            # 扫描控制
            "scan.start": self.handle_start_scan,
            "scan.stop": self.handle_stop_scan,
            "scan.status": self.handle_scan_status,
            
            # 系统信息
            "system.info": self.handle_system_info,
            "system.health": self.handle_system_health
        }
    
    async def handle_rpc_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理RPC请求
        
        Args:
            request: JSON-RPC 2.0请求
            
        Returns:
            JSON-RPC 2.0响应
        """
        # 验证请求格式
        if not self._validate_request(request):
            return self._error_response(
                request.get("id"),
                -32600,
                "Invalid Request"
            )
        
        method = request.get("method")
        params = request.get("params", {})
        request_id = request.get("id")
        
        # 查找方法
        handler = self.rpc_methods.get(method)
        if not handler:
            return self._error_response(
                request_id,
                -32601,
                f"Method not found: {method}"
            )
        
        # 执行方法
        try:
            result = await handler(params)
            return self._success_response(request_id, result)
        except Exception as e:
            logger.error(f"RPC方法执行错误: {method} - {e}")
            return self._error_response(
                request_id,
                -32603,
                f"Internal error: {str(e)}"
            )
    
    async def handle_io_control(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理IO控制请求"""
        channel = params.get("channel")
        state = params.get("state")
        
        if channel is None or state is None:
            raise ValueError("Missing required parameters: channel, state")
        
        # 执行IO控制
        success, msg = await self.hardware_manager.relay_service.control_do(
            1, channel, 1 if state else 0
        )
        
        # 更新状态
        if success:
            self.state_manager.update_io_state(channel, state)
        
        return {
            "success": success,
            "message": msg,
            "channel": channel,
            "state": state,
            "timestamp": datetime.now().isoformat()
        }
    
    async def handle_io_status(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理IO状态查询"""
        # 查询硬件状态
        success, data = await self.hardware_manager.relay_service.query_do_status(1)
        
        # 获取缓存状态
        full_state = self.state_manager.get_full_state()
        io_states = full_state.get("io_states", {})
        
        return {
            "success": success,
            "hardware_status": data if success else None,
            "cached_status": io_states,
            "timestamp": datetime.now().isoformat()
        }
    
    async def handle_ptz_set_angle(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理PTZ角度设置"""
        axis = params.get("axis")
        angle = params.get("angle")
        speed = params.get("speed")  # 可选，运动速度
        
        if axis not in ["pan", "tilt"] or angle is None:
            raise ValueError("Missing required parameters: axis, angle")
        
        # 设置速度（如果提供）
        if speed is not None:
            from hardware_control_service import PTZType
            ptz_type = PTZType.PAN if axis == "pan" else PTZType.TILT
            await self.hardware_manager.ptz_service.set_speed(30, speed, ptz_type)
        
        # 设置角度
        from hardware_control_service import PTZType
        ptz_type = PTZType.PAN if axis == "pan" else PTZType.TILT
        success, msg = await self.hardware_manager.ptz_service.set_angle(
            30, angle, ptz_type
        )
        
        # 更新状态
        if success:
            self.state_manager.update_ptz_state(
                30, axis, 
                target_angle=angle,
                is_moving=True
            )
        
        return {
            "success": success,
            "message": msg,
            "axis": axis,
            "angle": angle,
            "timestamp": datetime.now().isoformat()
        }
    
    async def handle_ptz_get_angle(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理PTZ角度查询"""
        axis = params.get("axis", "all")
        
        result = {}
        
        if axis in ["pan", "all"]:
            from hardware_control_service import PTZType
            success, angle = await self.hardware_manager.ptz_service.query_angle(
                30, PTZType.PAN
            )
            result["pan"] = {
                "success": success,
                "angle": angle,
                "cached": not success
            }
        
        if axis in ["tilt", "all"]:
            from hardware_control_service import PTZType
            success, angle = await self.hardware_manager.ptz_service.query_angle(
                30, PTZType.TILT
            )
            result["tilt"] = {
                "success": success,
                "angle": angle,
                "cached": not success
            }
        
        return {
            "success": True,
            "data": result,
            "timestamp": datetime.now().isoformat()
        }
    
    async def handle_start_scan(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理开始扫描请求"""
        scan_type = params.get("type")  # "point" or "terrain"
        scan_params = params.get("params", {})
        
        if scan_type not in ["point", "terrain"]:
            raise ValueError("Invalid scan type")
        
        # 更新业务状态
        self.state_manager.update_business_state(
            scanning=True,
            scan_type=scan_type,
            scan_params=scan_params,
            start_time=datetime.now().isoformat()
        )
        
        # TODO: 调用实际的扫描服务
        # from scan_service import ScanService
        # scan_service = ScanService(self.hardware_manager)
        # await scan_service.start_scan(scan_type, **scan_params)
        
        return {
            "success": True,
            "message": f"Started {scan_type} scan",
            "scan_id": f"scan_{int(datetime.now().timestamp())}",
            "timestamp": datetime.now().isoformat()
        }
    
    async def handle_emergency_stop(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """处理紧急停止"""
        # 执行紧急停止
        results = await self.hardware_manager.emergency_shutdown()
        
        # 更新状态
        self.state_manager.update_business_state(
            scanning=False,
            emergency_stop=True
        )
        
        # 更新所有IO状态为关闭
        for channel in range(1, 7):
            self.state_manager.update_io_state(channel, False)
        
        return {
            "success": True,
            "message": "Emergency stop executed",
            "results": results,
            "timestamp": datetime.now().isoformat()
        }
    
    def _validate_request(self, request: Dict[str, Any]) -> bool:
        """验证JSON-RPC请求格式"""
        return (
            isinstance(request, dict) and
            request.get("jsonrpc") == "2.0" and
            "method" in request and
            isinstance(request.get("method"), str)
        )
    
    def _success_response(self, request_id: Any, result: Any) -> Dict[str, Any]:
        """构建成功响应"""
        response = {
            "jsonrpc": "2.0",
            "result": result
        }
        if request_id is not None:
            response["id"] = request_id
        return response
    
    def _error_response(self, request_id: Any, code: int, message: str) -> Dict[str, Any]:
        """构建错误响应"""
        response = {
            "jsonrpc": "2.0",
            "error": {
                "code": code,
                "message": message
            }
        }
        if request_id is not None:
            response["id"] = request_id
        return response


class RPCWebServer:
    """RPC Web服务器"""
    
    def __init__(self, rpc_service: EdgeControllerRPCService, port: int = 8090):
        self.rpc_service = rpc_service
        self.port = port
        self.app = web.Application()
        self.websocket_clients = set()
        self.setup_routes()
    
    def setup_routes(self):
        """设置路由"""
        self.app.router.add_post('/rpc', self.handle_rpc)
        self.app.router.add_get('/ws', self.handle_websocket)
        self.app.router.add_get('/health', self.handle_health)
    
    async def handle_rpc(self, request):
        """处理RPC请求"""
        try:
            # 解析请求
            data = await request.json()
            
            # 处理批量请求
            if isinstance(data, list):
                responses = []
                for req in data:
                    resp = await self.rpc_service.handle_rpc_request(req)
                    responses.append(resp)
                return web.json_response(responses)
            else:
                # 单个请求
                response = await self.rpc_service.handle_rpc_request(data)
                return web.json_response(response)
                
        except json.JSONDecodeError:
            return web.json_response({
                "jsonrpc": "2.0",
                "error": {
                    "code": -32700,
                    "message": "Parse error"
                },
                "id": None
            })
        except Exception as e:
            logger.error(f"RPC处理错误: {e}")
            return web.json_response({
                "jsonrpc": "2.0",
                "error": {
                    "code": -32603,
                    "message": f"Internal error: {str(e)}"
                },
                "id": None
            }, status=500)
    
    async def handle_websocket(self, request):
        """处理WebSocket连接"""
        ws = web.WebSocketResponse()
        await ws.prepare(request)
        
        self.websocket_clients.add(ws)
        logger.info(f"WebSocket客户端连接: {request.remote}")
        
        try:
            async for msg in ws:
                if msg.type == aiohttp.WSMsgType.TEXT:
                    # 处理RPC请求
                    try:
                        data = json.loads(msg.data)
                        response = await self.rpc_service.handle_rpc_request(data)
                        await ws.send_json(response)
                    except Exception as e:
                        await ws.send_json({
                            "jsonrpc": "2.0",
                            "error": {
                                "code": -32603,
                                "message": str(e)
                            }
                        })
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    logger.error(f'WebSocket错误: {ws.exception()}')
        except Exception as e:
            logger.error(f"WebSocket处理错误: {e}")
        finally:
            self.websocket_clients.discard(ws)
            logger.info(f"WebSocket客户端断开: {request.remote}")
        
        return ws
    
    async def handle_health(self, request):
        """健康检查"""
        return web.json_response({
            "status": "healthy",
            "service": "edge_controller_rpc",
            "timestamp": datetime.now().isoformat()
        })
    
    async def broadcast_state(self, state: Dict[str, Any]):
        """广播状态到所有WebSocket客户端"""
        if self.websocket_clients:
            message = json.dumps({
                "jsonrpc": "2.0",
                "method": "state.update",
                "params": state
            })
            
            # 发送到所有客户端
            tasks = []
            for ws in self.websocket_clients:
                tasks.append(ws.send_str(message))
            
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def start(self):
        """启动服务器"""
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, '0.0.0.0', self.port)
        await site.start()
        logger.info(f"RPC服务器启动在端口 {self.port}")


async def test_rpc_service():
    """测试RPC服务"""
    print("=== RPC服务测试 ===")
    
    # 创建模拟的硬件管理器
    class MockHardwareManager:
        class relay_service:
            @staticmethod
            async def control_do(addr, channel, state):
                return True, f"DO{channel} {'开启' if state else '关闭'}"
            
            @staticmethod
            async def query_do_status(addr):
                return True, {"do_status": [1, 1, 0, 0, 0, 0]}
    
    # 创建状态管理器
    state_manager = DeviceStateManager()
    
    # 创建RPC服务
    rpc_service = EdgeControllerRPCService(MockHardwareManager(), state_manager)
    
    # 测试IO控制
    request = {
        "jsonrpc": "2.0",
        "method": "io.control",
        "params": {"channel": 1, "state": True},
        "id": 1
    }
    
    response = await rpc_service.handle_rpc_request(request)
    print(f"IO控制响应: {json.dumps(response, indent=2)}")
    
    # 测试状态查询
    request = {
        "jsonrpc": "2.0",
        "method": "io.status",
        "params": {},
        "id": 2
    }
    
    response = await rpc_service.handle_rpc_request(request)
    print(f"\nIO状态响应: {json.dumps(response, indent=2)}")


if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_rpc_service())