#!/usr/bin/env python3
"""
硬件状态同步服务
整合状态管理器、硬件控制服务和中央连接器
实现硬件状态的定期采集、上报和控制命令处理
"""

import asyncio
import json
import logging
import sys
import os
from typing import Dict, Any, Optional, List
from datetime import datetime
import threading

# 添加父目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入必要的模块
from hardware.state_manager_and_rpc_service import (
    DeviceStateManager, EdgeControllerRPCService, RPCWebServer
)
from hardware.hardware_control_service_optimized import OptimizedHardwareManager
from center_connector_grpc import CenterConnectorGRPC

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HardwareStateSyncService:
    """硬件状态同步服务"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化状态同步服务
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.running = False
        
        # 创建硬件管理器
        self.hardware_manager = None
        
        # 创建状态管理器
        redis_config = config.get('redis', {})
        self.state_manager = DeviceStateManager(
            redis_host=redis_config.get('host') if redis_config.get('enabled', False) else None,
            redis_port=redis_config.get('port', 6379)
        )
        
        # 创建RPC服务
        self.rpc_service = None
        self.rpc_server = None
        
        # 中央连接器（将在初始化时设置）
        self.center_connector = None
        
        # 状态采集配置
        sync_config = config.get('state_sync', {})
        self.collection_interval = sync_config.get('collection_interval', 10)  # 10秒采集一次
        self.state_change_threshold = sync_config.get('change_threshold', 0.1)  # 状态变化阈值
        
        # 状态采集任务
        self._collection_task = None
        self._previous_states = {}
        
        # 设备离线检测
        self.device_offline_timeout = sync_config.get('offline_timeout', 30)  # 30秒无响应判定离线
        self._device_last_seen = {}
        
    async def initialize(self):
        """初始化服务"""
        try:
            # 创建硬件管理器
            self.hardware_manager = OptimizedHardwareManager()
            
            # 启动Netty服务器
            await self.hardware_manager.netty_server.start()
            
            # 等待通道连接
            await self.hardware_manager.wait_for_channels()
            
            # 启动长期通电保护
            await self.hardware_manager._start_power_protection()
            
            # 创建RPC服务
            self.rpc_service = EdgeControllerRPCService(
                self.hardware_manager, 
                self.state_manager
            )
            
            # 创建RPC Web服务器
            rpc_port = self.config.get('rpc', {}).get('port', 8090)
            self.rpc_server = RPCWebServer(self.rpc_service, port=rpc_port)
            
            # 添加状态变更回调
            self.state_manager.add_state_change_callback(self._on_state_change)
            
            # 从Redis加载历史状态
            self.state_manager.load_from_redis()
            
            logger.info("硬件状态同步服务初始化完成")
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            raise
    
    def set_center_connector(self, center_connector: CenterConnectorGRPC):
        """设置中央连接器"""
        self.center_connector = center_connector
        
        # 注册硬件状态收集器
        if hasattr(center_connector, 'register_hardware_state_collector'):
            center_connector.register_hardware_state_collector(self.collect_hardware_state)
    
    async def start(self):
        """启动服务"""
        if self.running:
            logger.warning("服务已在运行")
            return
        
        self.running = True
        
        # 启动RPC服务器
        await self.rpc_server.start()
        
        # 启动状态采集任务
        self._collection_task = asyncio.create_task(self._state_collection_loop())
        
        logger.info("硬件状态同步服务已启动")
    
    async def stop(self):
        """停止服务"""
        self.running = False
        
        # 停止状态采集
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
        
        # 停止长期通电保护
        if self.hardware_manager:
            await self.hardware_manager.stop_power_protection()
        
        logger.info("硬件状态同步服务已停止")
    
    async def _state_collection_loop(self):
        """状态采集循环"""
        while self.running:
            try:
                # 采集硬件状态
                await self._collect_and_update_states()
                
                # 检测离线设备
                self._check_offline_devices()
                
                # 等待下次采集
                await asyncio.sleep(self.collection_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"状态采集错误: {e}")
                await asyncio.sleep(5)  # 错误后短暂等待
    
    async def _collect_and_update_states(self):
        """采集并更新硬件状态"""
        try:
            # 1. 采集IO状态
            success, io_data = await self.hardware_manager.relay_service.query_do_status(1)
            if success:
                do_status = io_data.get('do_status', [])
                for i, state in enumerate(do_status):
                    channel = i + 1
                    self.state_manager.update_io_state(channel, bool(state))
                
                # 更新设备最后响应时间
                self._device_last_seen['io_controller'] = datetime.now()
            
            # 2. 采集PTZ状态（如果PTZ已开启）
            ptz_do = self.hardware_manager.PTZ_DO
            if do_status and len(do_status) >= ptz_do and do_status[ptz_do - 1]:
                # 查询水平角度
                from hardware_control_service import PTZType
                success, pan_angle = await self.hardware_manager.ptz_service.query_angle(30, PTZType.PAN)
                if success:
                    self.state_manager.update_ptz_state(30, 'pan', angle=pan_angle)
                
                # 查询垂直角度
                success, tilt_angle = await self.hardware_manager.ptz_service.query_angle(30, PTZType.TILT)
                if success:
                    self.state_manager.update_ptz_state(30, 'tilt', angle=tilt_angle)
                
                # 更新设备最后响应时间
                self._device_last_seen['ptz'] = datetime.now()
            
            # 3. 检测状态变化
            current_states = self.state_manager.get_full_state()
            if self._has_significant_change(current_states):
                logger.info("检测到显著状态变化，触发上报")
                await self._report_state_change(current_states)
                self._previous_states = current_states
            
        except Exception as e:
            logger.error(f"状态采集失败: {e}")
    
    def _check_offline_devices(self):
        """检查离线设备"""
        now = datetime.now()
        
        for device, last_seen in self._device_last_seen.items():
            if (now - last_seen).total_seconds() > self.device_offline_timeout:
                logger.warning(f"设备 {device} 可能已离线，最后响应: {last_seen}")
                
                # 更新设备连接状态
                if device == 'io_controller':
                    # 将所有IO状态标记为未知
                    for channel in range(1, 7):
                        self.state_manager.update_io_state(
                            channel, False, 
                            voltage=None, 
                            current=None
                        )
                elif device == 'ptz':
                    # 更新PTZ错误计数
                    self.state_manager.update_ptz_state(
                        30, 'pan', 
                        error_count=self.state_manager.ptz_states[30]['pan'].error_count + 1
                    )
                    self.state_manager.update_ptz_state(
                        30, 'tilt',
                        error_count=self.state_manager.ptz_states[30]['tilt'].error_count + 1
                    )
    
    def _has_significant_change(self, current_states: Dict[str, Any]) -> bool:
        """检查是否有显著状态变化"""
        if not self._previous_states:
            return True
        
        # 检查IO状态变化
        prev_io = self._previous_states.get('io_states', {})
        curr_io = current_states.get('io_states', {})
        
        for channel, curr_state in curr_io.items():
            prev_state = prev_io.get(channel, {})
            if curr_state.get('state') != prev_state.get('state'):
                return True
        
        # 检查PTZ角度变化
        prev_ptz = self._previous_states.get('ptz_states', {})
        curr_ptz = current_states.get('ptz_states', {})
        
        for addr, curr_axes in curr_ptz.items():
            prev_axes = prev_ptz.get(addr, {})
            for axis, curr_axis_state in curr_axes.items():
                prev_axis_state = prev_axes.get(axis, {})
                
                # 检查角度变化
                prev_angle = prev_axis_state.get('angle', 0)
                curr_angle = curr_axis_state.get('angle', 0)
                if abs(curr_angle - prev_angle) > self.state_change_threshold:
                    return True
                
                # 检查运动状态变化
                if curr_axis_state.get('is_moving') != prev_axis_state.get('is_moving'):
                    return True
        
        # 检查业务状态变化
        prev_biz = self._previous_states.get('business_state', {})
        curr_biz = current_states.get('business_state', {})
        
        if (curr_biz.get('scanning') != prev_biz.get('scanning') or
            curr_biz.get('emergency_stop') != prev_biz.get('emergency_stop')):
            return True
        
        return False
    
    async def _report_state_change(self, states: Dict[str, Any]):
        """上报状态变化到中央服务器"""
        if self.center_connector and hasattr(self.center_connector, 'report_state_change'):
            try:
                await self.center_connector.report_state_change(states)
            except Exception as e:
                logger.error(f"状态上报失败: {e}")
    
    async def _on_state_change(self, event_type: str, data: Dict[str, Any]):
        """状态变更回调"""
        logger.info(f"状态变更事件: {event_type}, 数据: {data}")
        
        # 通过WebSocket广播状态变化
        if self.rpc_server:
            await self.rpc_server.broadcast_state({
                "event": event_type,
                "data": data,
                "timestamp": datetime.now().isoformat()
            })
    
    async def collect_hardware_state(self) -> Dict[str, Any]:
        """
        收集硬件状态（供中央连接器调用）
        
        Returns:
            硬件状态字典
        """
        full_state = self.state_manager.get_full_state()
        
        # 添加设备连接状态
        device_status = {}
        now = datetime.now()
        
        for device, last_seen in self._device_last_seen.items():
            is_online = (now - last_seen).total_seconds() <= self.device_offline_timeout
            device_status[device] = {
                "online": is_online,
                "last_seen": last_seen.isoformat()
            }
        
        # 构建完整的硬件状态
        hardware_state = {
            "io_states": full_state.get("io_states", {}),
            "ptz_states": full_state.get("ptz_states", {}),
            "business_state": full_state.get("business_state", {}),
            "device_status": device_status,
            "timestamp": full_state.get("timestamp")
        }
        
        return hardware_state
    
    async def handle_control_command(self, command: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理来自中央服务器的控制命令
        
        Args:
            command: 控制命令
            
        Returns:
            执行结果
        """
        cmd_type = command.get('type')
        params = command.get('params', {})
        
        try:
            if cmd_type == 'io_control':
                # IO控制命令
                result = await self.rpc_service.handle_io_control(params)
                
            elif cmd_type == 'ptz_control':
                # PTZ控制命令
                if params.get('action') == 'set_angle':
                    result = await self.rpc_service.handle_ptz_set_angle(params)
                elif params.get('action') == 'set_speed':
                    result = await self.rpc_service.handle_ptz_set_speed(params)
                else:
                    result = {"success": False, "message": f"未知的PTZ动作: {params.get('action')}"}
                    
            elif cmd_type == 'emergency_stop':
                # 紧急停止
                result = await self.rpc_service.handle_emergency_stop(params)
                
            elif cmd_type == 'start_scan':
                # 开始扫描
                result = await self.rpc_service.handle_start_scan(params)
                
            elif cmd_type == 'stop_scan':
                # 停止扫描
                result = await self.rpc_service.handle_stop_scan(params)
                
            else:
                result = {"success": False, "message": f"未知的命令类型: {cmd_type}"}
                
            logger.info(f"执行控制命令 {cmd_type}: {result}")
            return result
            
        except Exception as e:
            logger.error(f"执行控制命令失败: {e}")
            return {
                "success": False,
                "message": str(e),
                "error": "EXECUTION_ERROR"
            }


async def test_service():
    """测试硬件状态同步服务"""
    print("=== 硬件状态同步服务测试 ===")
    
    # 测试配置
    config = {
        "redis": {
            "enabled": False
        },
        "rpc": {
            "port": 8090
        },
        "state_sync": {
            "collection_interval": 5,
            "change_threshold": 0.1,
            "offline_timeout": 30
        },
        "serial_server": {
            "host": "*************",
            "io_port": 7100
        }
    }
    
    # 创建服务
    service = HardwareStateSyncService(config)
    
    try:
        # 初始化
        await service.initialize()
        
        # 启动服务
        await service.start()
        
        print("\n服务已启动，等待5秒进行状态采集...")
        await asyncio.sleep(5)
        
        # 获取当前状态
        hardware_state = await service.collect_hardware_state()
        print(f"\n当前硬件状态:")
        print(json.dumps(hardware_state, indent=2, ensure_ascii=False))
        
        # 测试控制命令
        print("\n测试IO控制命令...")
        result = await service.handle_control_command({
            "type": "io_control",
            "params": {
                "channel": 1,
                "state": True
            }
        })
        print(f"控制结果: {result}")
        
        # 再等待一会儿
        await asyncio.sleep(10)
        
    finally:
        # 停止服务
        await service.stop()
        print("\n服务已停止")


if __name__ == "__main__":
    asyncio.run(test_service())