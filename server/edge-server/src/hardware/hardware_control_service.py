#!/usr/bin/env python3
"""
优化版硬件控制服务
基于原始Java代码的协议实现
完全兼容原有系统
"""

import asyncio
import aiohttp
from aiohttp import web
import binascii
import logging
import struct
import threading
import time
import yaml
import os
from typing import Dict, Optional, Tuple, Any, List
from datetime import datetime
from enum import Enum

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 加载配置文件
config_path = os.path.join(os.path.dirname(__file__), 'hardware_config.yaml')
if os.path.exists(config_path):
    with open(config_path, 'r') as f:
        CONFIG = yaml.safe_load(f)
        logger.info(f"Loaded configuration from {config_path}")
else:
    logger.warning(f"Configuration file not found at {config_path}, using defaults")
    CONFIG = {
        'serial_server': {
            'host': '**************',
            'io_port': 7100,
            'ptz_port': 7200
        },
        'lidar': {'ip': '*************'},
        'protection': {
            'enabled': True,
            'check_interval': 600,
            'max_power_on_time': 600
        }
    }


class PTZType(Enum):
    """PTZ类型枚举 - 从Java PTZEnum提取"""
    PAN = ("4b", "51", "59")    # spinCode, sendSearchCode, reportSearchCode
    TILT = ("4d", "53", "5b")
    
    def __init__(self, spin_code, send_search_code, report_search_code):
        self.spin_code = spin_code
        self.send_search_code = send_search_code  
        self.report_search_code = report_search_code


class ModbusUtils:
    """Modbus协议工具类"""
    
    # CRC16表 - 从Java CRC16Util提取
    CRC16_TAB_H = [
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x00, 0xC1, 0x81, 0x40,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40, 0x01, 0xC0, 0x80, 0x41, 0x01, 0xC0, 0x80, 0x41,
        0x00, 0xC1, 0x81, 0x40
    ]
    
    CRC16_TAB_L = [
        0x00, 0xC0, 0xC1, 0x01, 0xC3, 0x03, 0x02, 0xC2, 0xC6, 0x06, 0x07, 0xC7,
        0x05, 0xC5, 0xC4, 0x04, 0xCC, 0x0C, 0x0D, 0xCD, 0x0F, 0xCF, 0xCE, 0x0E,
        0x0A, 0xCA, 0xCB, 0x0B, 0xC9, 0x09, 0x08, 0xC8, 0xD8, 0x18, 0x19, 0xD9,
        0x1B, 0xDB, 0xDA, 0x1A, 0x1E, 0xDE, 0xDF, 0x1F, 0xDD, 0x1D, 0x1C, 0xDC,
        0x14, 0xD4, 0xD5, 0x15, 0xD7, 0x17, 0x16, 0xD6, 0xD2, 0x12, 0x13, 0xD3,
        0x11, 0xD1, 0xD0, 0x10, 0xF0, 0x30, 0x31, 0xF1, 0x33, 0xF3, 0xF2, 0x32,
        0x36, 0xF6, 0xF7, 0x37, 0xF5, 0x35, 0x34, 0xF4, 0x3C, 0xFC, 0xFD, 0x3D,
        0xFF, 0x3F, 0x3E, 0xFE, 0xFA, 0x3A, 0x3B, 0xFB, 0x39, 0xF9, 0xF8, 0x38,
        0x28, 0xE8, 0xE9, 0x29, 0xEB, 0x2B, 0x2A, 0xEA, 0xEE, 0x2E, 0x2F, 0xEF,
        0x2D, 0xED, 0xEC, 0x2C, 0xE4, 0x24, 0x25, 0xE5, 0x27, 0xE7, 0xE6, 0x26,
        0x22, 0xE2, 0xE3, 0x23, 0xE1, 0x21, 0x20, 0xE0, 0xA0, 0x60, 0x61, 0xA1,
        0x63, 0xA3, 0xA2, 0x62, 0x66, 0xA6, 0xA7, 0x67, 0xA5, 0x65, 0x64, 0xA4,
        0x6C, 0xAC, 0xAD, 0x6D, 0xAF, 0x6F, 0x6E, 0xAE, 0xAA, 0x6A, 0x6B, 0xAB,
        0x69, 0xA9, 0xA8, 0x68, 0x78, 0xB8, 0xB9, 0x79, 0xBB, 0x7B, 0x7A, 0xBA,
        0xBE, 0x7E, 0x7F, 0xBF, 0x7D, 0xBD, 0xBC, 0x7C, 0xB4, 0x74, 0x75, 0xB5,
        0x77, 0xB7, 0xB6, 0x76, 0x72, 0xB2, 0xB3, 0x73, 0xB1, 0x71, 0x70, 0xB0,
        0x50, 0x90, 0x91, 0x51, 0x93, 0x53, 0x52, 0x92, 0x96, 0x56, 0x57, 0x97,
        0x55, 0x95, 0x94, 0x54, 0x9C, 0x5C, 0x5D, 0x9D, 0x5F, 0x9F, 0x9E, 0x5E,
        0x5A, 0x9A, 0x9B, 0x5B, 0x99, 0x59, 0x58, 0x98, 0x88, 0x48, 0x49, 0x89,
        0x4B, 0x8B, 0x8A, 0x4A, 0x4E, 0x8E, 0x8F, 0x4F, 0x8D, 0x4D, 0x4C, 0x8C,
        0x44, 0x84, 0x85, 0x45, 0x87, 0x47, 0x46, 0x86, 0x82, 0x42, 0x43, 0x83,
        0x41, 0x81, 0x80, 0x40
    ]
    
    @staticmethod
    def calc_crc16_modbus(data: bytes) -> bytes:
        """计算Modbus CRC16校验和"""
        crc = 0xFFFF
        
        for byte in data:
            crc ^= byte
            for _ in range(8):
                if crc & 0x0001:
                    crc = (crc >> 1) ^ 0xA001
                else:
                    crc >>= 1
        
        # 返回低字节在前，高字节在后
        return struct.pack('<H', crc)
    
    @staticmethod
    def build_do_command(slave_addr: int, channel: int, status: int) -> bytes:
        """构建DO控制命令"""
        # 功能码05，写单个线圈
        # channel从0开始（外部1-6，内部0-5）
        value = 0xFF00 if status == 1 else 0x0000
        cmd = struct.pack('>BBHH', slave_addr, 0x05, channel, value)
        crc = ModbusUtils.calc_crc16_modbus(cmd)
        return cmd + crc
    
    @staticmethod
    def build_do_query_command(slave_addr: int, count: int = 6) -> bytes:
        """构建DO查询命令"""
        # 功能码01，读线圈（只读取6个DO）
        cmd = struct.pack('>BBHH', slave_addr, 0x01, 0x0000, count)
        crc = ModbusUtils.calc_crc16_modbus(cmd)
        return cmd + crc


class PTZProtocol:
    """PTZ控制协议实现 - 基于Java原始协议"""
    
    # 速度表 - 根据文档中验证过的实际硬件速度表
    PAN_SPEED_TABLE = [
        0.1, 0.5, 1.0, 2.0, 3.0, 7.0, 7.2, 7.4, 7.7, 8.0, 9.0, 9.5,
        10.0, 10.1, 10.2, 10.3, 10.4, 10.5, 10.6, 10.7, 10.8, 10.9,
        11.0, 11.1, 11.3, 11.5, 11.7, 11.9, 12.1, 12.3, 12.5, 12.7,
        12.9, 13.1, 13.3, 13.5, 13.7, 13.9, 14.0, 14.2, 14.4, 14.7,
        14.9, 15.2, 15.5, 15.7, 15.9, 16.0, 16.1, 16.3, 16.5, 16.7,
        16.9, 17.0, 17.1, 17.2, 17.3, 17.4, 17.5, 17.6, 17.7, 17.8,
        17.9, 18.0
    ]  # 62个速度级别，范围：0.1-18.0°/s
    
    TILT_SPEED_TABLE = [
        0.1, 0.5, 0.8, 1.2, 1.4, 1.6, 1.8, 2.0, 2.2, 2.4, 2.6, 2.8, 3.0,
        3.2, 3.4, 3.6, 3.8, 4.0, 4.2, 4.4, 4.6, 4.8, 5.0, 5.2, 5.4, 5.6,
        5.8, 6.0, 6.2, 6.4, 6.6, 6.8, 6.9, 7.0, 7.1, 7.2, 7.3, 7.4, 7.5,
        7.6, 7.7, 7.8, 7.9, 8.1, 8.2, 8.3, 8.4, 8.5, 8.6, 8.7, 8.8, 8.9,
        9.0, 9.1, 9.2, 9.3, 9.4, 9.5, 9.6, 9.7, 9.8, 9.9, 10.0, 10.0
    ]  # 64个速度级别，范围：0.1-10.0°/s
    
    @staticmethod
    def calc_checksum(cmd_hex: str) -> str:
        """计算校验和 - 简单累加对256取模"""
        checksum = 0
        # 按2位分割并累加
        for i in range(0, len(cmd_hex), 2):
            byte_hex = cmd_hex[i:i+2]
            checksum += int(byte_hex, 16)
        
        # 对256取模并返回2位十六进制
        return f"{checksum % 256:02x}"
    
    @staticmethod
    def build_spin_angle_command(address: int, angle: float, ptz_type: PTZType) -> bytes:
        """
        构建转动命令
        命令格式：FF{地址}00{spinCode}{角度} + 校验和
        """
        # 角度处理
        if ptz_type == PTZType.PAN:
            # 水平角度：0-359度，乘以100
            angle = angle % 360  # 确保在0-359范围
            angle_value = int(angle * 100)
        else:  # TILT
            # 垂直角度：-60到60度（根据硬件实际支持范围）
            if angle < 0:
                # 负角度转换为(360+angle)*100
                angle_value = int((360 + angle) * 100)
            else:
                angle_value = int(angle * 100)
        
        # 构建命令（大写）
        cmd = f"FF{address:02X}00{ptz_type.spin_code.upper()}{angle_value:04X}"
        checksum = PTZProtocol.calc_checksum(cmd)
        
        # 返回完整命令（转为小写）
        full_cmd = f"ff{cmd[2:].lower()}{checksum}"
        return binascii.unhexlify(full_cmd)
    
    @staticmethod
    def build_query_angle_command(address: int, ptz_type: PTZType) -> bytes:
        """
        构建查询命令
        命令格式：FF{地址}00{sendSearchCode}0000 + 校验和
        """
        cmd = f"FF{address:02X}00{ptz_type.send_search_code.upper()}0000"
        checksum = PTZProtocol.calc_checksum(cmd)
        
        # 返回完整命令
        full_cmd = f"{cmd.lower()}{checksum}"
        return binascii.unhexlify(full_cmd)
    
    @staticmethod
    def build_speed_command(speed: float, address: int, ptz_type: PTZType) -> bytes:
        """
        构建设置速度命令
        水平：e08106010e{地址}{速度索引} + 校验和
        俯仰：e08106010f{地址}{速度索引} + 校验和
        """
        # 根据类型选择速度表
        speed_table = PTZProtocol.PAN_SPEED_TABLE if ptz_type == PTZType.PAN else PTZProtocol.TILT_SPEED_TABLE
        
        # 查找最接近的速度索引
        speed_index = 0
        min_diff = float('inf')
        for i, table_speed in enumerate(speed_table):
            diff = abs(table_speed - speed)
            if diff < min_diff:
                min_diff = diff
                speed_index = i
        
        # 构建命令
        if ptz_type == PTZType.PAN:
            cmd = f"e08106010e{address:02x}{speed_index:02x}"
        else:
            cmd = f"e08106010f{address:02x}{speed_index:02x}"
        
        checksum = PTZProtocol.calc_checksum(cmd)
        full_cmd = f"{cmd}{checksum}"
        
        logger.info(f"设置{ptz_type.name}速度: {speed}°/s -> 索引{speed_index} (实际{speed_table[speed_index]}°/s)")
        
        return binascii.unhexlify(full_cmd)
    
    @staticmethod
    def build_stop_command(address: int, stop_type: str = "all") -> bytes:
        """
        构建停止命令
        水平停止：FF{地址}00042000 + 校验和
        俯仰停止：FF{地址}00022000 + 校验和
        全停止：  FF{地址}00000000 + 校验和
        """
        if stop_type == "pan":
            cmd = f"FF{address:02X}00042000"
        elif stop_type == "tilt":
            cmd = f"FF{address:02X}00022000"
        else:  # all
            cmd = f"FF{address:02X}00000000"
        
        checksum = PTZProtocol.calc_checksum(cmd)
        full_cmd = f"{cmd.lower()}{checksum}"
        
        return binascii.unhexlify(full_cmd)
    
    @staticmethod
    def parse_angle_response(data: bytes, ptz_type: PTZType) -> Optional[float]:
        """解析角度响应"""
        if len(data) < 7:
            return None
        
        # 检查响应类型
        if data[0] != 0xFF:
            return None
        
        # 检查响应码
        response_code = f"{data[3]:02x}"
        if response_code != ptz_type.report_search_code:
            return None
        
        # 提取角度值
        angle_high = data[4]
        angle_low = data[5]
        angle_value = (angle_high << 8) | angle_low
        
        # 转换为实际角度
        angle = angle_value / 100.0
        
        # 处理TILT负角度
        if ptz_type == PTZType.TILT and angle >= 300:
            angle = angle - 360
        
        return angle


class PTZService:
    """云台控制服务 - 基于Java协议实现"""
    
    def __init__(self, netty_server):
        self.netty_server = netty_server
        self.ptz_channel = None
        
        # 状态缓存
        self.angle_cache = {}
        self.speed_cache = {}
        self.cache_lock = asyncio.Lock()
        
    async def set_angle(self, ptz_addr: int, angle: float, ptz_type: PTZType) -> Tuple[bool, str]:
        """设置角度（通用方法）"""
        # 构建命令
        cmd = PTZProtocol.build_spin_angle_command(ptz_addr, angle, ptz_type)
        hex_cmd = binascii.hexlify(cmd).decode()
        
        logger.info(f"设置{ptz_type.name}角度: 地址={ptz_addr}, 角度={angle}°, 命令={hex_cmd}")
        
        success = await self.netty_server.send_command(self.ptz_channel, hex_cmd)
        
        if success:
            # 更新缓存
            async with self.cache_lock:
                if ptz_addr not in self.angle_cache:
                    self.angle_cache[ptz_addr] = {}
                self.angle_cache[ptz_addr][ptz_type.name.lower()] = angle
                
            # 延迟以确保命令执行
            await asyncio.sleep(0.1)
            return True, f"{ptz_type.name}角度设置为{angle}°"
        
        return False, "设置失败"
    
    async def query_angle(self, ptz_addr: int, ptz_type: PTZType) -> Tuple[bool, float]:
        """查询角度（通用方法）"""
        cmd = PTZProtocol.build_query_angle_command(ptz_addr, ptz_type)
        hex_cmd = binascii.hexlify(cmd).decode()
        
        logger.info(f"查询{ptz_type.name}角度: 地址={ptz_addr}, 命令={hex_cmd}")
        
        # 发送命令并等待响应
        response = await self.netty_server.send_command_with_response(
            self.ptz_channel, hex_cmd, timeout=1.5  # 减少PTZ超时时间
        )
        
        if response:
            logger.info(f"收到响应: {binascii.hexlify(response).decode()}")
            
            angle = PTZProtocol.parse_angle_response(response, ptz_type)
            if angle is not None:
                # 更新缓存
                async with self.cache_lock:
                    if ptz_addr not in self.angle_cache:
                        self.angle_cache[ptz_addr] = {}
                    self.angle_cache[ptz_addr][ptz_type.name.lower()] = angle
                return True, angle
        
        # 返回缓存值
        async with self.cache_lock:
            if ptz_addr in self.angle_cache and ptz_type.name.lower() in self.angle_cache[ptz_addr]:
                return False, self.angle_cache[ptz_addr][ptz_type.name.lower()]
        
        return False, 0.0
    
    async def set_speed(self, ptz_addr: int, speed: float, ptz_type: PTZType) -> Tuple[bool, str, float]:
        """设置速度（通用方法）"""
        # 获取速度表和实际速度
        speed_table = PTZProtocol.PAN_SPEED_TABLE if ptz_type == PTZType.PAN else PTZProtocol.TILT_SPEED_TABLE
        
        # 查找最接近的速度索引
        speed_index = 0
        min_diff = float('inf')
        for i, table_speed in enumerate(speed_table):
            diff = abs(table_speed - speed)
            if diff < min_diff:
                min_diff = diff
                speed_index = i
        
        actual_speed = speed_table[speed_index]
        
        # 构建命令
        cmd = PTZProtocol.build_speed_command(speed, ptz_addr, ptz_type)
        hex_cmd = binascii.hexlify(cmd).decode()
        
        logger.info(f"设置{ptz_type.name}速度: 地址={ptz_addr}, 请求={speed}°/s, 实际={actual_speed}°/s, 命令={hex_cmd}")
        
        success = await self.netty_server.send_command(self.ptz_channel, hex_cmd)
        
        if success:
            # 更新缓存为实际速度
            async with self.cache_lock:
                if ptz_addr not in self.speed_cache:
                    self.speed_cache[ptz_addr] = {}
                self.speed_cache[ptz_addr][ptz_type.name.lower()] = actual_speed
            
            return True, f"{ptz_type.name}速度设置成功", actual_speed
        
        return False, "设置失败", 0
    
    async def query_speed(self, ptz_addr: int, ptz_type: PTZType) -> Tuple[bool, Dict]:
        """查询速度（从缓存）"""
        async with self.cache_lock:
            if ptz_addr in self.speed_cache and ptz_type.name.lower() in self.speed_cache[ptz_addr]:
                speed = self.speed_cache[ptz_addr][ptz_type.name.lower()]
                
                # 查找对应的速度表索引
                speed_table = PTZProtocol.PAN_SPEED_TABLE if ptz_type == PTZType.PAN else PTZProtocol.TILT_SPEED_TABLE
                speed_index = 0
                for i, table_speed in enumerate(speed_table):
                    if abs(table_speed - speed) < 0.01:
                        speed_index = i
                        break
                
                return True, {"speed": speed, "index": speed_index}
        
        # 返回默认值
        default_speed = 10.0 if ptz_type == PTZType.PAN else 5.0
        return False, {"speed": default_speed, "index": 0}
    
    async def stop(self, ptz_addr: int, stop_type: str = "all") -> Tuple[bool, str]:
        """停止云台运动"""
        cmd = PTZProtocol.build_stop_command(ptz_addr, stop_type)
        hex_cmd = binascii.hexlify(cmd).decode()
        
        logger.info(f"停止云台: 类型={stop_type}, 命令={hex_cmd}")
        
        success = await self.netty_server.send_command(self.ptz_channel, hex_cmd)
        return success, f"停止命令已发送" if success else "停止命令发送失败"


class RelayService:
    """继电器（IO）控制服务 - 优化版"""
    
    def __init__(self, netty_server):
        self.netty_server = netty_server
        self.io_channel = None
        self.do_status_cache = {}
        self.cache_lock = asyncio.Lock()
        
    async def control_do(self, slave_addr: int, do_index: int, status: int) -> Tuple[bool, str]:
        """控制DO开关"""
        if not 1 <= do_index <= 6:
            return False, f"DO索引必须在1-6之间，当前: {do_index}"
        
        # 构建命令（do_index-1因为内部从0开始）
        cmd = ModbusUtils.build_do_command(slave_addr, do_index-1, status)
        hex_cmd = binascii.hexlify(cmd).decode()
        
        action = "打开" if status == 1 else "关闭"
        logger.info(f"{action}DO{do_index}: 地址={slave_addr}, 命令={hex_cmd}")
        
        success = await self.netty_server.send_command(self.io_channel, hex_cmd)
        
        if success:
            # 更新缓存
            async with self.cache_lock:
                key = f"{slave_addr}:{do_index}"
                self.do_status_cache[key] = status
            
            # 短暂延迟确保硬件响应
            await asyncio.sleep(0.05)
            return True, f"DO{do_index}{action}成功"
        
        return False, f"DO{do_index}{action}失败"
    
    async def query_do_status(self, slave_addr: int) -> Tuple[bool, Dict]:
        """查询所有DO状态"""
        cmd = ModbusUtils.build_do_query_command(slave_addr)
        hex_cmd = binascii.hexlify(cmd).decode()
        
        logger.info(f"查询DO状态: 地址={slave_addr}, 命令={hex_cmd}")
        
        # 使用更短的超时时间，并增加重试机制
        max_retries = 3
        retry_delay = 0.5
        
        for attempt in range(max_retries):
            response = await self.netty_server.send_command_with_response(
                self.io_channel, hex_cmd, timeout=1.0  # 减少超时时间
            )
            
            if response:
                break
                
            if attempt < max_retries - 1:
                logger.warning(f"DO状态查询第{attempt+1}次失败，{retry_delay}秒后重试...")
                await asyncio.sleep(retry_delay)
        
        if response:
            logger.info(f"收到DO状态响应: {binascii.hexlify(response).decode()}")
            
            # 检查是否是DI上报格式 (40 57 ...)
            if len(response) >= 7 and response[0] == 0x40 and response[1] == 0x57:
                logger.info("检测到DI上报格式响应")
                # DI上报格式: 40 57 [地址] [00] [状态] [CRC1] [CRC2]
                if response[2] == slave_addr and response[3] == 0x00:
                    # 状态字节在第5个位置
                    status_byte = response[4]
                    
                    # 解析每个DO的状态（低位在前）
                    do_status = []
                    for i in range(6):  # 只解析6个DO
                        do_status.append((status_byte >> i) & 0x01)
                    
                    # 更新缓存
                    async with self.cache_lock:
                        for i in range(6):  # 只有6个DO
                            key = f"{slave_addr}:{i+1}"
                            self.do_status_cache[key] = do_status[i]
                    
                    logger.info(f"DI上报解析成功: DO状态={do_status}")
                    
                    return True, {
                        "slave_addr": slave_addr,
                        "do_status": do_status,
                        "raw_response": binascii.hexlify(response).decode(),
                        "format": "DI上报"
                    }
            
            # 标准Modbus响应格式
            elif len(response) >= 6:  # 最小长度：地址(1) + 功能码(1) + 字节数(1) + 数据(1) + CRC(2)
                # 验证CRC
                received_crc = response[-2:]
                calculated_crc = ModbusUtils.calc_crc16_modbus(response[:-2])
                
                if received_crc == calculated_crc:
                    # 解析响应
                    if response[0] == slave_addr and response[1] == 0x01:
                        byte_count = response[2]
                        
                        if len(response) >= 3 + byte_count + 2:  # 确保数据完整
                            # 获取状态字节
                            status_byte = response[3] if byte_count >= 1 else 0
                            
                            # 解析每个DO的状态（低位在前）
                            do_status = []
                            for i in range(6):  # 只解析6个DO
                                do_status.append((status_byte >> i) & 0x01)
                            
                            # 更新缓存
                            async with self.cache_lock:
                                for i in range(6):  # 只有6个DO
                                    key = f"{slave_addr}:{i+1}"
                                    self.do_status_cache[key] = do_status[i]
                            
                            return True, {
                                "slave_addr": slave_addr,
                                "do_status": do_status[:6],  # 只返回前6个
                                "raw_response": binascii.hexlify(response).decode()
                            }
                        else:
                            logger.error(f"响应数据不完整: 期望{3 + byte_count + 2}字节，实际{len(response)}字节")
                    else:
                        logger.error(f"响应格式错误: 地址={response[0]}, 功能码={response[1]}")
                else:
                    logger.error(f"CRC校验失败: 接收={binascii.hexlify(received_crc).decode()}, 计算={binascii.hexlify(calculated_crc).decode()}")
            else:
                logger.error(f"响应长度不足: {len(response)}字节")
        else:
            logger.warning("未收到响应或超时")
        
        # 返回缓存的状态
        async with self.cache_lock:
            cached_status = [0] * 6
            for i in range(6):
                key = f"{slave_addr}:{i+1}"
                if key in self.do_status_cache:
                    cached_status[i] = self.do_status_cache[key]
            
            return False, {
                "slave_addr": slave_addr,
                "do_status": cached_status,
                "message": "使用缓存状态"
            }
    
    async def control_all_do(self, slave_addr: int, status: int) -> Dict[str, Any]:
        """控制所有DO"""
        results = {}
        
        for i in range(1, 7):
            success, msg = await self.control_do(slave_addr, i, status)
            results[f"do{i}"] = {"success": success, "message": msg}
            
            # 短暂延迟避免命令冲突
            if i < 6:
                await asyncio.sleep(0.1)
        
        return results


class NettyServer:
    """Netty TCP服务器 - 优化版"""
    
    def __init__(self, host: str, port: int):
        self.host = host
        self.port = port
        self.server = None
        self.channels = {}
        self.response_queues = {}
        self.data_buffers = {}
        
    async def start(self):
        """启动服务器"""
        self.server = await asyncio.start_server(
            self.handle_client, self.host, self.port
        )
        
        addrs = ', '.join(str(sock.getsockname()) for sock in self.server.sockets)
        logger.info(f"优化版Netty服务器启动在 {addrs}")
        
    async def handle_client(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter):
        """处理客户端连接"""
        addr = writer.get_extra_info('peername')
        logger.info(f"新连接来自: {addr}")
        
        try:
            # 读取认证包
            auth_data = await reader.read(8)
            if len(auth_data) == 8:
                channel_name = self.authenticate(auth_data)
                if channel_name:
                    logger.info(f"✓ 认证成功 [{addr}]: {channel_name}")
                    
                    # 初始化通道
                    self.channels[channel_name] = {
                        'reader': reader,
                        'writer': writer,
                        'addr': addr
                    }
                    self.response_queues[channel_name] = asyncio.Queue()
                    self.data_buffers[channel_name] = bytes()
                    
                    # 处理数据
                    await self.handle_channel_data(channel_name, reader)
                else:
                    logger.warning(f"✗ 认证失败 [{addr}]")
                    writer.close()
                    await writer.wait_closed()
                    
        except Exception as e:
            logger.error(f"处理客户端错误: {e}")
        finally:
            # 清理资源
            if writer:
                writer.close()
                await writer.wait_closed()
                
            # 清理通道数据
            for name in list(self.channels.keys()):
                if self.channels.get(name, {}).get('addr') == addr:
                    del self.channels[name]
                    if name in self.response_queues:
                        del self.response_queues[name]
                    if name in self.data_buffers:
                        del self.data_buffers[name]
                    logger.info(f"通道 {name} 已断开")
    
    def authenticate(self, auth_data: bytes) -> Optional[str]:
        """认证客户端"""
        auth_hex = binascii.hexlify(auth_data).decode()
        
        # 根据Java代码中的认证逻辑
        if auth_hex == "d4ad2070b92f0000":
            return "io_channel"
        elif auth_hex == "0000000000000000":
            return "ptz_channel"
        else:
            return None
    
    async def handle_channel_data(self, channel_name: str, reader: asyncio.StreamReader):
        """处理通道数据 - 优化版"""
        buffer = bytes()
        
        while True:
            try:
                # 读取数据
                data = await reader.read(1024)
                if not data:
                    break
                
                # 添加到缓冲区
                buffer += data
                
                # 处理完整的响应包
                while len(buffer) > 0:
                    # 根据通道类型解析响应
                    packet_len = self.get_packet_length(buffer, channel_name)
                    
                    if packet_len > 0 and len(buffer) >= packet_len:
                        # 提取完整包
                        packet = buffer[:packet_len]
                        buffer = buffer[packet_len:]
                        
                        # 记录接收到的数据
                        hex_data = binascii.hexlify(packet).decode()
                        logger.debug(f"[{channel_name}] 收到完整包: {hex_data}")
                        
                        # 放入响应队列
                        if channel_name in self.response_queues:
                            await self.response_queues[channel_name].put(packet)
                    else:
                        # 数据不完整，等待更多数据
                        break
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"读取数据错误: {e}")
                break
    
    def get_packet_length(self, buffer: bytes, channel_name: str) -> int:
        """获取数据包长度"""
        if len(buffer) < 3:
            return 0
        
        if channel_name == "ptz_channel":
            # PTZ响应：通常是7字节或16字节
            if buffer[0] == 0xFF:
                # 角度响应通常是7字节
                return 7
            elif buffer[0] == 0x40 and buffer[1] == 0x57:
                # 特殊响应格式
                return 16
            else:
                # 其他响应，尝试找到下一个0xFF
                for i in range(1, min(len(buffer), 20)):
                    if buffer[i] == 0xFF:
                        return i
                return 7  # 默认7字节
                
        elif channel_name == "io_channel":
            # Modbus响应
            if len(buffer) >= 3:
                # 检查是否是DI上报（特殊格式）
                if buffer[0] == 0x40 and buffer[1] == 0x57:
                    return 7  # 固定7字节
                
                # 标准Modbus响应
                addr = buffer[0]
                func_code = buffer[1]
                
                # 确保是有效的Modbus地址和功能码
                if 1 <= addr <= 247:
                    if func_code == 0x01:  # 读线圈响应
                        if len(buffer) > 2:
                            byte_count = buffer[2]
                            return 3 + byte_count + 2  # 地址+功能码+字节数+数据+CRC
                    elif func_code == 0x05:  # 写单个线圈响应
                        return 8  # 固定8字节：地址+功能码+地址(2)+数据(2)+CRC(2)
                    elif func_code == 0x81 or func_code == 0x85:  # 异常响应
                        return 5  # 固定5字节：地址+功能码+异常码+CRC(2)
        
        return 0
    
    async def send_command(self, channel_name: str, hex_command: str) -> bool:
        """发送命令"""
        if channel_name not in self.channels:
            logger.error(f"通道 {channel_name} 未连接")
            return False
        
        try:
            channel = self.channels[channel_name]
            writer = channel['writer']
            
            # 转换并发送
            data = binascii.unhexlify(hex_command)
            writer.write(data)
            await writer.drain()
            
            logger.debug(f"[{channel_name}] 发送命令: {hex_command}")
            return True
            
        except Exception as e:
            logger.error(f"发送命令失败: {e}")
            return False
    
    async def send_command_with_response(self, channel_name: str, hex_command: str, 
                                       timeout: float = 2.0) -> Optional[bytes]:
        """发送命令并等待响应"""
        if channel_name not in self.channels:
            logger.error(f"通道 {channel_name} 未连接")
            return None
        
        try:
            # 清空响应队列
            queue = self.response_queues.get(channel_name)
            if queue:
                while not queue.empty():
                    try:
                        queue.get_nowait()
                    except asyncio.QueueEmpty:
                        break
            
            # 发送命令
            if not await self.send_command(channel_name, hex_command):
                return None
            
            # 等待响应
            try:
                response = await asyncio.wait_for(queue.get(), timeout=timeout)
                return response
            except asyncio.TimeoutError:
                logger.warning(f"等待响应超时: {hex_command}")
                return None
                
        except Exception as e:
            logger.error(f"发送命令并等待响应失败: {e}")
            return None
    
    async def stop(self):
        """停止服务器"""
        if self.server:
            self.server.close()
            await self.server.wait_closed()
            logger.info("Netty服务器已停止")


class HardwareManager:
    """硬件管理器 - 优化版"""
    
    # 设备配置（根据CLAUDE.md）
    LIDAR_DO = 1      # 激光雷达使用DO1
    PTZ_DO = 2        # 云台使用DO2
    DEFAULT_SLAVE_ADDR = 1
    DEFAULT_PTZ_ADDR = 30
    
    # 长期通电保护配置
    PROTECTION_CHECK_INTERVAL = 30   # 30秒检查一次 (测试用)
    MAX_POWER_ON_TIME = 60           # 60秒最大通电时间（测试用）
    CRITICAL_DEVICES = [LIDAR_DO, PTZ_DO]  # 需要保护的设备
    
    def __init__(self, netty_server: NettyServer, relay_service: RelayService, 
                 ptz_service: PTZService):
        self.netty_server = netty_server
        self.relay_service = relay_service
        self.ptz_service = ptz_service
        
        # 长期通电保护相关
        self.device_power_on_times = {}  # 记录设备通电时间
        self.business_active_lock = asyncio.Lock()  # 业务活跃锁
        self.business_active = False     # 业务活跃状态
        self.protection_task = None      # 保护任务
        self.protection_enabled = True   # 保护机制启用状态
        
    async def initialize(self) -> bool:
        """初始化硬件"""
        logger.info("初始化硬件管理器...")
        
        # 设置通道引用
        self.relay_service.io_channel = "io_channel"
        self.ptz_service.ptz_channel = "ptz_channel"
        
        # 等待通道连接
        max_wait = 10
        for i in range(max_wait):
            if "io_channel" in self.netty_server.channels and "ptz_channel" in self.netty_server.channels:
                logger.info("所有通道已连接")
                
                # 查询初始状态
                await self.query_all_status()
                
                # 启动长期通电保护机制
                await self._start_power_protection()
                
                return True
                
            await asyncio.sleep(1)
            logger.info(f"等待通道连接... {i+1}/{max_wait}")
        
        logger.error("通道连接超时")
        return False
    
    async def query_all_status(self) -> Dict[str, Any]:
        """查询所有设备状态"""
        status = {}
        
        # 查询DO状态
        success, do_data = await self.relay_service.query_do_status(self.DEFAULT_SLAVE_ADDR)
        if success:
            status["do_status"] = do_data
        
        # 查询PTZ角度
        success, pan_angle = await self.ptz_service.query_angle(self.DEFAULT_PTZ_ADDR, PTZType.PAN)
        status["pan_angle"] = {"success": success, "angle": pan_angle}
        
        success, tilt_angle = await self.ptz_service.query_angle(self.DEFAULT_PTZ_ADDR, PTZType.TILT)
        status["tilt_angle"] = {"success": success, "angle": tilt_angle}
        
        return status
    
    async def control_device_power(self, device: str, status: int) -> Tuple[bool, str]:
        """控制设备电源"""
        device_map = {
            "lidar": self.LIDAR_DO,
            "ptz": self.PTZ_DO
        }
        
        if device not in device_map:
            return False, f"未知设备: {device}"
        
        do_index = device_map[device]
        success, msg = await self.relay_service.control_do(self.DEFAULT_SLAVE_ADDR, do_index, status)
        
        if success:
            # 更新设备通电时间记录
            await self._update_power_on_time(do_index, status)
            
        return success, msg
    
    async def emergency_shutdown(self) -> Dict[str, Any]:
        """紧急停机 - 关闭所有设备"""
        logger.warning("执行紧急停机...")
        
        # 停止云台运动
        await self.ptz_service.stop(self.DEFAULT_PTZ_ADDR, "all")
        
        # 关闭所有DO
        results = await self.relay_service.control_all_do(self.DEFAULT_SLAVE_ADDR, 0)
        
        # 清空通电时间记录
        self.device_power_on_times.clear()
        
        return results
    
    async def _start_power_protection(self):
        """启动长期通电保护机制"""
        if self.protection_task is None:
            logger.info(f"启动长期通电保护机制，检查间隔: {self.PROTECTION_CHECK_INTERVAL}秒")
            self.protection_task = asyncio.create_task(self._power_protection_loop())
    
    async def _power_protection_loop(self):
        """长期通电保护循环"""
        while self.protection_enabled:
            try:
                await asyncio.sleep(self.PROTECTION_CHECK_INTERVAL)
                await self._check_and_protect_devices()
            except asyncio.CancelledError:
                logger.info("长期通电保护机制已停止")
                break
            except Exception as e:
                logger.error(f"长期通电保护检查错误: {e}")
    
    async def _check_and_protect_devices(self):
        """检查设备状态并执行保护"""
        async with self.business_active_lock:
            if self.business_active:
                logger.debug("业务活跃中，跳过保护检查")
                return
        
        try:
            # 使用缓存的DO状态（更可靠）
            success, do_data = await self.relay_service.query_do_status(self.DEFAULT_SLAVE_ADDR)
            if not success:
                # 如果查询失败，使用缓存状态
                logger.info("使用缓存DO状态进行保护检查")
                do_data = do_data if do_data else {}

            do_status = do_data.get("do_status", [0] * 6)
            current_time = time.time()
            devices_to_shutdown = []
            
            # 检查关键设备
            for do_index in self.CRITICAL_DEVICES:
                if do_index <= len(do_status) and do_status[do_index - 1] == 1:
                    # 设备处于通电状态
                    if do_index not in self.device_power_on_times:
                        # 首次发现设备通电，记录时间
                        self.device_power_on_times[do_index] = current_time
                        logger.info(f"DO{do_index} 首次检测到通电，开始计时")
                    else:
                        # 检查通电时长
                        power_on_duration = current_time - self.device_power_on_times[do_index]
                        device_name = "激光雷达" if do_index == self.LIDAR_DO else "云台"
                        
                        if power_on_duration > self.MAX_POWER_ON_TIME:
                            devices_to_shutdown.append((do_index, device_name, power_on_duration))
                            logger.warning(f"检测到{device_name}(DO{do_index})长时间通电: {power_on_duration:.1f}秒，准备断电")
                else:
                    # 设备已关闭，清除记录
                    if do_index in self.device_power_on_times:
                        del self.device_power_on_times[do_index]
            
            # 执行保护性断电
            if devices_to_shutdown:
                logger.warning(f"执行长期通电保护，关闭 {len(devices_to_shutdown)} 个设备")
                
                for do_index, device_name, duration in devices_to_shutdown:
                    success, msg = await self.relay_service.control_do(
                        self.DEFAULT_SLAVE_ADDR, do_index, 0
                    )
                    if success:
                        logger.info(f"✓ {device_name}(DO{do_index})保护性断电成功，通电时长: {duration:.1f}秒")
                        # 清除时间记录
                        if do_index in self.device_power_on_times:
                            del self.device_power_on_times[do_index]
                    else:
                        logger.error(f"✗ {device_name}(DO{do_index})保护性断电失败: {msg}")
        
        except Exception as e:
            logger.error(f"设备保护检查异常: {e}")
    
    async def _update_power_on_time(self, do_index: int, status: int):
        """更新设备通电时间记录"""
        current_time = time.time()
        
        if status == 1:  # 通电
            self.device_power_on_times[do_index] = current_time
            device_name = "激光雷达" if do_index == self.LIDAR_DO else "云台"
            logger.debug(f"{device_name}(DO{do_index})通电，开始计时")
        else:  # 断电
            if do_index in self.device_power_on_times:
                power_on_duration = current_time - self.device_power_on_times[do_index]
                device_name = "激光雷达" if do_index == self.LIDAR_DO else "云台"
                logger.debug(f"{device_name}(DO{do_index})断电，通电时长: {power_on_duration:.1f}秒")
                del self.device_power_on_times[do_index]
    
    async def set_business_active(self, active: bool, business_type: str = "unknown"):
        """设置业务活跃状态"""
        async with self.business_active_lock:
            if active != self.business_active:
                self.business_active = active
                status = "激活" if active else "结束"
                logger.info(f"业务状态变更: {business_type} {status}")
                
                if not active:
                    # 业务结束时，重置所有设备的通电计时
                    current_time = time.time()
                    for do_index in list(self.device_power_on_times.keys()):
                        self.device_power_on_times[do_index] = current_time
    
    async def get_protection_status(self) -> Dict[str, Any]:
        """获取保护机制状态"""
        current_time = time.time()
        device_info = {}
        
        for do_index, power_on_time in self.device_power_on_times.items():
            device_name = "激光雷达" if do_index == self.LIDAR_DO else "云台"
            power_on_duration = current_time - power_on_time
            remaining_time = max(0, self.MAX_POWER_ON_TIME - power_on_duration)
            
            device_info[f"DO{do_index}"] = {
                "device_name": device_name,
                "power_on_duration": power_on_duration,
                "remaining_safe_time": remaining_time,
                "status": "正常" if remaining_time > 0 else "超时"
            }
        
        return {
            "protection_enabled": self.protection_enabled,
            "business_active": self.business_active,
            "check_interval": self.PROTECTION_CHECK_INTERVAL,
            "max_power_on_time": self.MAX_POWER_ON_TIME,
            "devices": device_info
        }
    
    async def stop_power_protection(self):
        """停止长期通电保护机制"""
        self.protection_enabled = False
        if self.protection_task:
            self.protection_task.cancel()
            try:
                await self.protection_task
            except asyncio.CancelledError:
                pass
            self.protection_task = None
            logger.info("长期通电保护机制已停止")


class RESTServer:
    """REST API服务器 - 优化版"""
    
    def __init__(self, hardware_manager: HardwareManager, relay_service: RelayService,
                 ptz_service: PTZService):
        self.hardware_manager = hardware_manager
        self.relay_service = relay_service
        self.ptz_service = ptz_service
        self.app = web.Application()
        self.setup_routes()
    
    def setup_routes(self):
        """设置路由"""
        # 基础路由
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/', self.index)
        
        # IO控制
        self.app.router.add_post('/api/io/control', self.io_control)
        self.app.router.add_get('/api/io/status', self.io_status)
        self.app.router.add_post('/api/io/all', self.io_control_all)
        
        # PTZ控制
        self.app.router.add_post('/api/ptz/angle', self.ptz_set_angle)
        self.app.router.add_get('/api/ptz/angle', self.ptz_get_angle)
        self.app.router.add_post('/api/ptz/speed', self.ptz_set_speed)
        self.app.router.add_get('/api/ptz/speed', self.ptz_get_speed)
        self.app.router.add_post('/api/ptz/stop', self.ptz_stop)
        self.app.router.add_get('/api/ptz/speed_tables', self.ptz_get_speed_tables)
        self.app.router.add_post('/api/ptz/validate_speed', self.ptz_validate_speed)
        
        # 设备控制
        self.app.router.add_post('/api/device/power', self.device_power)
        self.app.router.add_post('/api/device/emergency_stop', self.emergency_stop)
        self.app.router.add_get('/api/device/status', self.device_status)
        
        # 长期通电保护
        self.app.router.add_get('/api/protection/status', self.protection_status)
        self.app.router.add_post('/api/protection/business_active', self.set_business_active)
        
        # 测试接口
        self.app.router.add_post('/api/test/all', self.test_all_functions)
        
        # 添加兼容性路由（为了支持旧版本API调用）
        self._add_compatibility_routes()
    
    def _add_compatibility_routes(self):
        """添加兼容性路由以支持旧版本API调用"""
        # IO状态查询的兼容路由
        self.app.router.add_get('/api/status/io', self._io_status_compat)
        self.app.router.add_get('/api/status/do', self._io_status_compat)
        
        # IO控制的兼容路由
        self.app.router.add_post('/api/control/do', self._io_control_compat)
        self.app.router.add_post('/api/control/all_do', self._io_control_all_compat)
        
        # PTZ控制的兼容路由  
        self.app.router.add_post('/api/control/ptz', self._ptz_control_compat)
        self.app.router.add_get('/api/status/ptz', self._ptz_status_compat)
        
        # 设备状态的兼容路由
        self.app.router.add_get('/api/status/all', self.device_status)
        
        # 业务保护的兼容路由
        self.app.router.add_post('/api/business/active', self.set_business_active)
        
        logger.info("Added compatibility routes for legacy API support")
    
    # 兼容性处理器
    async def _io_status_compat(self, request):
        """IO状态查询的兼容处理器"""
        # 直接调用并返回，io_status已经返回正确格式
        return await self.io_status(request)
    
    async def _io_control_compat(self, request):
        """IO控制的兼容处理器"""
        try:
            data = await request.json()
            # 转换参数格式
            if 'do_id' in data:
                data['do_index'] = data['do_id']
            if 'state' in data:
                data['status'] = 1 if data['state'] else 0
            
            # 构造兼容的请求数据
            slave_addr = data.get("slave_addr", 1)
            do_index = data.get("do_index") or data.get("port")
            status = data.get("status")
            
            if do_index is None or status is None:
                return web.json_response({
                    "error": "缺少必要参数，需要do_id/do_index和state/status"
                }, status=400)
            
            success, msg = await self.relay_service.control_do(slave_addr, do_index, status)
            
            return web.json_response({
                "success": success,
                "message": msg,
                "do_index": do_index,
                "status": status
            })
            
        except Exception as e:
            logger.error(f"IO控制兼容处理错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def _io_control_all_compat(self, request):
        """控制所有DO的兼容处理器"""
        try:
            data = await request.json()
            slave_addr = data.get("slave_addr", 1)
            status = data.get("status")
            
            # 兼容state参数
            if status is None and 'state' in data:
                status = 1 if data['state'] else 0
            
            if status is None:
                return web.json_response({"error": "缺少state/status参数"}, status=400)
            
            results = await self.relay_service.control_all_do(slave_addr, status)
            
            return web.json_response({
                "success": True,
                "action": "open_all" if status == 1 else "close_all",
                "results": results
            })
            
        except Exception as e:
            logger.error(f"控制所有DO兼容处理错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def _ptz_control_compat(self, request):
        """PTZ控制的兼容处理器"""
        return await self.ptz_set_angle(request)
    
    async def _ptz_status_compat(self, request):
        """PTZ状态查询的兼容处理器"""
        try:
            # 查询PAN和TILT角度
            pan_success, pan_angle = await self.ptz_service.query_angle(30, PTZType.PAN)
            tilt_success, tilt_angle = await self.ptz_service.query_angle(30, PTZType.TILT)

            return web.json_response({
                'success': True,
                'data': {
                    'pan_angle': pan_angle,
                    'tilt_angle': tilt_angle,
                    'pan_status': pan_success,
                    'tilt_status': tilt_success
                }
            })
        except Exception as e:
            logger.error(f"PTZ状态查询失败: {e}")
            return web.json_response({
                'success': False,
                'error': str(e)
            }, status=500)
    
    async def health_check(self, request):
        """健康检查"""
        channels_status = {
            "io_channel": "io_channel" in self.hardware_manager.netty_server.channels,
            "ptz_channel": "ptz_channel" in self.hardware_manager.netty_server.channels
        }
        
        return web.json_response({
            "status": "healthy",
            "service": "Optimized Hardware Control Service",
            "version": "3.0",
            "timestamp": datetime.now().isoformat(),
            "channels": channels_status
        })
    
    async def index(self, request):
        """首页"""
        return web.json_response({
            "service": "硬件控制API (优化版)",
            "description": "基于原始Java协议的精确实现",
            "endpoints": {
                "health": "GET /health",
                "io": {
                    "control": "POST /api/io/control",
                    "status": "GET /api/io/status",
                    "all": "POST /api/io/all"
                },
                "ptz": {
                    "angle": "GET/POST /api/ptz/angle",
                    "speed": "GET/POST /api/ptz/speed",
                    "stop": "POST /api/ptz/stop"
                },
                "device": {
                    "power": "POST /api/device/power",
                    "emergency_stop": "POST /api/device/emergency_stop",
                    "status": "GET /api/device/status"
                },
                "protection": {
                    "status": "GET /api/protection/status",
                    "business_active": "POST /api/protection/business_active"
                },
                "test": {
                    "all": "POST /api/test/all"
                }
            }
        })
    
    # IO控制接口
    async def io_control(self, request):
        """控制单个DO - 支持多种参数格式"""
        try:
            data = await request.json()
            slave_addr = data.get("slave_addr", 1)
            
            # 参数兼容性处理
            # 支持格式1: {"do_index": 1, "status": 1}
            # 支持格式2: {"port": 1, "action": "on"}
            
            do_index = data.get("do_index") or data.get("port")
            status = data.get("status")
            
            # 如果使用action参数，转换为status
            if status is None and "action" in data:
                action = data.get("action")
                status = 1 if action == "on" else 0
            
            if do_index is None or status is None:
                return web.json_response({
                    "error": "缺少必要参数，需要(do_index/port)和(status/action)"
                }, status=400)
            
            success, msg = await self.relay_service.control_do(slave_addr, do_index, status)
            
            # 返回兼容的响应
            response_data = {
                "success": success,
                "message": msg,
                "do_index": do_index,
                "status": status
            }
            
            # 如果是旧格式请求，添加兼容字段
            if "port" in data:
                response_data["port"] = do_index
                response_data["state"] = "on" if status == 1 else "off"
            
            return web.json_response(response_data)
            
        except Exception as e:
            logger.error(f"IO控制错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def io_status(self, request):
        """查询IO状态"""
        try:
            slave_addr = int(request.query.get("slave_addr", 1))
            
            success, data = await self.relay_service.query_do_status(slave_addr)
            
            if success:
                return web.json_response({
                    "success": True,
                    "data": {
                        "do_status": data.get("do_status", [0, 0, 0, 0, 0, 0]),
                        "slave_addr": data.get("slave_addr", slave_addr),
                        "raw_response": data.get("raw_response"),
                        "format": data.get("format")
                    }
                })
            else:
                # 即使查询失败，也返回缓存的状态数据
                return web.json_response({
                    "success": False,
                    "error": data.get("message", "查询失败"),
                    "data": {
                        "do_status": data.get("do_status", [0, 0, 0, 0, 0, 0]),
                        "slave_addr": data.get("slave_addr", slave_addr)
                    }
                })
                
        except Exception as e:
            logger.error(f"查询IO状态错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def io_control_all(self, request):
        """控制所有DO"""
        try:
            data = await request.json()
            slave_addr = data.get("slave_addr", 1)
            status = data.get("status")
            
            if status is None:
                return web.json_response({"error": "缺少status参数"}, status=400)
            
            results = await self.relay_service.control_all_do(slave_addr, status)
            
            return web.json_response({
                "success": True,
                "action": "open_all" if status == 1 else "close_all",
                "results": results
            })
            
        except Exception as e:
            logger.error(f"控制所有DO错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    # PTZ控制接口
    async def ptz_set_angle(self, request):
        """设置PTZ角度"""
        try:
            data = await request.json()
            ptz_addr = data.get("ptz_addr", 30)
            axis = data.get("axis")  # "pan" or "tilt"
            angle = data.get("angle")
            
            if axis not in ["pan", "tilt"] or angle is None:
                return web.json_response({"error": "参数错误"}, status=400)
            
            ptz_type = PTZType.PAN if axis == "pan" else PTZType.TILT
            success, msg = await self.ptz_service.set_angle(ptz_addr, angle, ptz_type)
            
            return web.json_response({
                "success": success,
                "message": msg,
                "axis": axis,
                "angle": angle
            })
            
        except Exception as e:
            logger.error(f"设置PTZ角度错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def ptz_get_angle(self, request):
        """查询PTZ角度"""
        try:
            ptz_addr = int(request.query.get("ptz_addr", 30))
            axis = request.query.get("axis", "all")
            
            result = {}
            
            if axis in ["pan", "all"]:
                success, angle = await self.ptz_service.query_angle(ptz_addr, PTZType.PAN)
                result["pan"] = {"success": success, "angle": angle}
            
            if axis in ["tilt", "all"]:
                success, angle = await self.ptz_service.query_angle(ptz_addr, PTZType.TILT)
                result["tilt"] = {"success": success, "angle": angle}
            
            return web.json_response({
                "success": True,
                "data": result
            })
            
        except Exception as e:
            logger.error(f"查询PTZ角度错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def ptz_set_speed(self, request):
        """设置PTZ速度"""
        try:
            data = await request.json()
            ptz_addr = data.get("ptz_addr", 30)
            axis = data.get("axis")
            speed = data.get("speed")
            
            if axis not in ["pan", "tilt"] or speed is None:
                return web.json_response({"error": "参数错误"}, status=400)
            
            ptz_type = PTZType.PAN if axis == "pan" else PTZType.TILT
            success, msg, actual_speed = await self.ptz_service.set_speed(ptz_addr, speed, ptz_type)
            
            return web.json_response({
                "success": success,
                "message": msg,
                "axis": axis,
                "requested_speed": speed,
                "actual_speed": actual_speed
            })
            
        except Exception as e:
            logger.error(f"设置PTZ速度错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def ptz_get_speed(self, request):
        """查询PTZ速度"""
        try:
            ptz_addr = int(request.query.get("ptz_addr", 30))
            axis = request.query.get("axis", "all")
            
            result = {}
            
            if axis in ["pan", "all"]:
                success, speed_data = await self.ptz_service.query_speed(ptz_addr, PTZType.PAN)
                result["pan"] = {"success": success, **speed_data}
            
            if axis in ["tilt", "all"]:
                success, speed_data = await self.ptz_service.query_speed(ptz_addr, PTZType.TILT)
                result["tilt"] = {"success": success, **speed_data}
            
            return web.json_response({
                "success": True,
                "data": result
            })
            
        except Exception as e:
            logger.error(f"查询PTZ速度错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def ptz_stop(self, request):
        """停止PTZ运动"""
        try:
            # 处理可能的JSON解析错误
            try:
                data = await request.json()
            except:
                # 如果JSON解析失败，使用默认值
                logger.warning("PTZ停止请求JSON解析失败，使用默认值")
                data = {}
            
            ptz_addr = data.get("ptz_addr", 30)
            stop_type = data.get("type", "all")  # "pan", "tilt", "all"
            
            success, msg = await self.ptz_service.stop(ptz_addr, stop_type)
            
            return web.json_response({
                "success": success,
                "message": msg,
                "type": stop_type
            })
            
        except Exception as e:
            logger.error(f"停止PTZ错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def ptz_get_speed_tables(self, request):
        """获取PTZ速度表"""
        try:
            # 获取当前速度
            ptz_addr = int(request.query.get("ptz_addr", 30))
            
            # 查询当前速度
            pan_success, pan_data = await self.ptz_service.query_speed(ptz_addr, PTZType.PAN)
            tilt_success, tilt_data = await self.ptz_service.query_speed(ptz_addr, PTZType.TILT)
            
            return web.json_response({
                "success": True,
                "data": {
                    "pan_speeds": PTZProtocol.PAN_SPEED_TABLE,
                    "tilt_speeds": PTZProtocol.TILT_SPEED_TABLE,
                    "current_speeds": {
                        "pan_speed": pan_data.get("speed", 0),
                        "tilt_speed": tilt_data.get("speed", 0)
                    }
                }
            })
        except Exception as e:
            logger.error(f"获取速度表错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def ptz_validate_speed(self, request):
        """验证PTZ速度值"""
        try:
            data = await request.json()
            axis = data.get("axis")
            speed = data.get("speed")
            
            if axis not in ["pan", "tilt"] or speed is None:
                return web.json_response({"error": "参数错误"}, status=400)
            
            # 选择速度表
            speed_table = PTZProtocol.PAN_SPEED_TABLE if axis == "pan" else PTZProtocol.TILT_SPEED_TABLE
            
            # 查找最接近的速度
            speed_index = 0
            min_diff = float('inf')
            for i, table_speed in enumerate(speed_table):
                diff = abs(table_speed - speed)
                if diff < min_diff:
                    min_diff = diff
                    speed_index = i
            
            closest_speed = speed_table[speed_index]
            is_exact = (speed in speed_table)
            
            # 构建预览命令
            ptz_addr = data.get("ptz_addr", 30)
            direction_code = "0e" if axis == "pan" else "0f"
            cmd_data = f"e0810601{direction_code}{ptz_addr:02x}{speed_index:02x}"
            checksum = PTZProtocol.calc_checksum(cmd_data)
            command_preview = cmd_data + checksum
            
            return web.json_response({
                "success": True,
                "data": {
                    "input_speed": speed,
                    "axis": axis,
                    "is_exact_match": is_exact,
                    "closest_speed": closest_speed,
                    "speed_index": speed_index,
                    "command_preview": command_preview
                }
            })
            
        except Exception as e:
            logger.error(f"验证速度错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    # 设备控制接口
    async def device_power(self, request):
        """设备电源控制"""
        try:
            data = await request.json()
            device = data.get("device")
            status = data.get("status")
            
            if device not in ["lidar", "ptz"] or status is None:
                return web.json_response({"error": "参数错误"}, status=400)
            
            success, msg = await self.hardware_manager.control_device_power(device, status)
            
            return web.json_response({
                "success": success,
                "message": msg,
                "device": device,
                "status": status
            })
            
        except Exception as e:
            logger.error(f"设备电源控制错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def emergency_stop(self, request):
        """紧急停机"""
        try:
            results = await self.hardware_manager.emergency_shutdown()
            
            return web.json_response({
                "success": True,
                "message": "紧急停机执行完成",
                "results": results
            })
            
        except Exception as e:
            logger.error(f"紧急停机错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def device_status(self, request):
        """查询设备状态"""
        try:
            status = await self.hardware_manager.query_all_status()
            
            return web.json_response({
                "success": True,
                "data": status,
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"查询设备状态错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def test_all_functions(self, request):
        """测试所有功能"""
        try:
            results = {}
            
            # 1. 测试IO控制
            logger.info("测试IO控制...")
            results["io_test"] = {}
            
            # 打开DO1
            success, msg = await self.relay_service.control_do(1, 1, 1)
            results["io_test"]["do1_open"] = {"success": success, "msg": msg}
            await asyncio.sleep(0.5)
            
            # 查询状态
            success, data = await self.relay_service.query_do_status(1)
            results["io_test"]["status_query"] = {"success": success, "data": data}
            
            # 关闭DO1
            success, msg = await self.relay_service.control_do(1, 1, 0)
            results["io_test"]["do1_close"] = {"success": success, "msg": msg}
            
            # 2. 测试PTZ控制
            logger.info("测试PTZ控制...")
            results["ptz_test"] = {}
            
            # 设置速度
            success, msg = await self.ptz_service.set_speed(30, 5.0, PTZType.PAN)
            results["ptz_test"]["set_pan_speed"] = {"success": success, "msg": msg}
            
            success, msg = await self.ptz_service.set_speed(30, 3.0, PTZType.TILT)
            results["ptz_test"]["set_tilt_speed"] = {"success": success, "msg": msg}
            
            # 设置角度
            success, msg = await self.ptz_service.set_angle(30, 45.0, PTZType.PAN)
            results["ptz_test"]["set_pan_angle"] = {"success": success, "msg": msg}
            await asyncio.sleep(2)
            
            # 查询角度
            success, angle = await self.ptz_service.query_angle(30, PTZType.PAN)
            results["ptz_test"]["query_pan_angle"] = {"success": success, "angle": angle}
            
            # 归零
            success, msg = await self.ptz_service.set_angle(30, 0.0, PTZType.PAN)
            results["ptz_test"]["home_pan"] = {"success": success, "msg": msg}
            
            success, msg = await self.ptz_service.set_angle(30, 0.0, PTZType.TILT)
            results["ptz_test"]["home_tilt"] = {"success": success, "msg": msg}
            
            return web.json_response({
                "success": True,
                "message": "功能测试完成",
                "results": results
            })
            
        except Exception as e:
            logger.error(f"功能测试错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    # 长期通电保护接口
    async def protection_status(self, request):
        """查询长期通电保护状态"""
        try:
            status = await self.hardware_manager.get_protection_status()
            
            return web.json_response({
                "success": True,
                "data": status,
                "timestamp": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"查询保护状态错误: {e}")
            return web.json_response({"error": str(e)}, status=500)
    
    async def set_business_active(self, request):
        """设置业务活跃状态"""
        try:
            data = await request.json()
            active = data.get("active", False)
            business_type = data.get("business_type", "unknown")
            
            await self.hardware_manager.set_business_active(active, business_type)
            
            return web.json_response({
                "success": True,
                "message": f"业务状态已设置为: {'激活' if active else '结束'}",
                "active": active,
                "business_type": business_type
            })
            
        except Exception as e:
            logger.error(f"设置业务状态错误: {e}")
            return web.json_response({"error": str(e)}, status=500)


async def main():
    """主函数"""
    # 创建Netty服务器
    netty_server = NettyServer('0.0.0.0', 7100)
    
    # 创建服务实例
    relay_service = RelayService(netty_server)
    ptz_service = PTZService(netty_server)
    hardware_manager = HardwareManager(netty_server, relay_service, ptz_service)
    
    # 创建REST服务器
    rest_server = RESTServer(hardware_manager, relay_service, ptz_service)
    
    # 启动Netty服务器
    await netty_server.start()
    
    # 初始化硬件
    await hardware_manager.initialize()
    
    # 启动REST服务器
    runner = web.AppRunner(rest_server.app)
    await runner.setup()
    site = web.TCPSite(runner, '0.0.0.0', 7080)
    await site.start()
    
    logger.info("=" * 60)
    logger.info("优化版硬件控制服务已启动")
    logger.info("REST API: http://0.0.0.0:7080")
    logger.info("Netty Server: tcp://0.0.0.0:7100")
    logger.info("基于原始Java协议的精确实现")
    logger.info("=" * 60)
    
    try:
        # 保持运行
        await asyncio.Future()
    except KeyboardInterrupt:
        logger.info("收到停止信号")
    finally:
        # 清理
        await hardware_manager.stop_power_protection()
        await hardware_manager.emergency_shutdown()
        await netty_server.stop()
        await runner.cleanup()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序已停止")