#!/usr/bin/env python3
"""
优化版硬件控制服务 - 针对IO延迟优化
主要优化：
1. IO操作后增加5秒等待时间
2. 查询超时时间增加到3秒
3. 优化重试策略
"""

import asyncio
import aiohttp
from aiohttp import web
import binascii
import logging
import struct
import threading
import time
import yaml
import os
from typing import Dict, Optional, Tuple, Any, List
from datetime import datetime
from enum import Enum

# 从原始服务导入所需的类
from hardware_control_service import (
    PTZType, ModbusUtils, PTZProtocol, NettyServer,
    RelayService, PTZService, HardwareManager, RESTServer,
    CONFIG
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OptimizedRelayService(RelayService):
    """优化的继电器服务 - 针对IO延迟优化"""
    
    # 优化的超时设置
    QUERY_TIMEOUT = 3.0  # 查询超时时间增加到3秒
    IO_OPERATION_DELAY = 5.0  # IO操作后等待5秒
    
    async def control_do(self, slave_addr: int, do_index: int, status: int) -> Tuple[bool, str]:
        """控制单个DO - 优化版"""
        action = "打开" if status == 1 else "关闭"
        logger.info(f"{action}DO{do_index}: 地址={slave_addr}")
        
        # 构建命令
        cmd = ModbusUtils.build_do_command(slave_addr, do_index - 1, status)
        hex_cmd = binascii.hexlify(cmd).decode()
        logger.info(f"{action}DO{do_index}: 地址={slave_addr}, 命令={hex_cmd}")
        
        # 发送命令
        success = await self.netty_server.send_command(self.io_channel, hex_cmd)
        
        if success:
            # 更新缓存
            async with self.cache_lock:
                key = f"{slave_addr}:{do_index}"
                self.do_status_cache[key] = status
            
            # 重要：IO操作后等待硬件响应
            logger.info(f"DO{do_index}控制命令已发送，等待{self.IO_OPERATION_DELAY}秒让硬件响应...")
            await asyncio.sleep(self.IO_OPERATION_DELAY)
            
            return True, f"DO{do_index}{action}成功"
        
        return False, f"DO{do_index}{action}失败"
    
    async def query_do_status(self, slave_addr: int) -> Tuple[bool, Dict]:
        """查询所有DO状态 - 优化版"""
        cmd = ModbusUtils.build_do_query_command(slave_addr)
        hex_cmd = binascii.hexlify(cmd).decode()
        
        logger.info(f"查询DO状态: 地址={slave_addr}, 命令={hex_cmd}")
        
        # 优化的重试策略
        max_retries = 2  # 减少重试次数，但增加单次超时时间
        retry_delays = [1.0, 2.0]  # 递增的重试延迟
        
        for attempt in range(max_retries):
            response = await self.netty_server.send_command_with_response(
                self.io_channel, hex_cmd, timeout=self.QUERY_TIMEOUT
            )
            
            if response:
                break
                
            if attempt < max_retries - 1:
                delay = retry_delays[attempt]
                logger.warning(f"DO状态查询第{attempt+1}次失败，{delay}秒后重试...")
                await asyncio.sleep(delay)
        
        if response:
            logger.info(f"收到DO状态响应: {binascii.hexlify(response).decode()}")
            
            # 检查是否是DI上报格式 (40 57 ...)
            if len(response) >= 7 and response[0] == 0x40 and response[1] == 0x57:
                logger.info("检测到DI上报格式响应")
                # DI上报格式: 40 57 [地址] [00] [状态] [CRC1] [CRC2]
                if response[2] == slave_addr and response[3] == 0x00:
                    # 状态字节在第5个位置
                    status_byte = response[4]
                    
                    # 解析每个DO的状态（低位在前）
                    do_status = []
                    for i in range(6):  # 只解析6个DO
                        do_status.append((status_byte >> i) & 0x01)
                    
                    # 更新缓存
                    async with self.cache_lock:
                        for i in range(6):  # 只有6个DO
                            key = f"{slave_addr}:{i+1}"
                            self.do_status_cache[key] = do_status[i]
                    
                    logger.info(f"DI上报解析成功: DO状态={do_status}")
                    
                    return True, {
                        "slave_addr": slave_addr,
                        "do_status": do_status,
                        "raw_response": binascii.hexlify(response).decode(),
                        "format": "DI上报"
                    }
            
            # 标准Modbus响应格式
            elif len(response) >= 6:  # 最小长度：地址(1) + 功能码(1) + 字节数(1) + 数据(1) + CRC(2)
                # 验证CRC
                received_crc = response[-2:]
                calculated_crc = ModbusUtils.calc_crc16_modbus(response[:-2])
                
                if received_crc == calculated_crc:
                    # 解析响应
                    if response[0] == slave_addr and response[1] == 0x01:
                        byte_count = response[2]
                        
                        if len(response) >= 3 + byte_count + 2:  # 确保数据完整
                            # 获取状态字节
                            status_byte = response[3] if byte_count >= 1 else 0
                            
                            # 解析每个DO的状态（低位在前）
                            do_status = []
                            for i in range(6):  # 只解析6个DO
                                do_status.append((status_byte >> i) & 0x01)
                            
                            # 更新缓存
                            async with self.cache_lock:
                                for i in range(6):  # 只有6个DO
                                    key = f"{slave_addr}:{i+1}"
                                    self.do_status_cache[key] = do_status[i]
                            
                            return True, {
                                "slave_addr": slave_addr,
                                "do_status": do_status[:6],  # 只返回前6个
                                "raw_response": binascii.hexlify(response).decode()
                            }
                        else:
                            logger.error(f"响应数据不完整: 期望{3 + byte_count + 2}字节，实际{len(response)}字节")
                    else:
                        logger.error(f"响应格式错误: 地址={response[0]}, 功能码={response[1]}")
                else:
                    logger.error(f"CRC校验失败: 接收={binascii.hexlify(received_crc).decode()}, 计算={binascii.hexlify(calculated_crc).decode()}")
            else:
                logger.error(f"响应长度不足: {len(response)}字节")
        else:
            logger.warning("未收到响应或超时")
        
        # 返回缓存的状态
        async with self.cache_lock:
            cached_status = []
            for i in range(1, 7):  # DO1-DO6
                key = f"{slave_addr}:{i}"
                cached_status.append(self.do_status_cache.get(key, 0))
            
            return False, {
                "slave_addr": slave_addr,
                "do_status": cached_status,
                "message": "查询超时，返回缓存状态"
            }
    
    async def control_all_do(self, slave_addr: int, status: int) -> Dict[str, Any]:
        """控制所有DO - 优化版"""
        results = []
        
        # 批量发送控制命令，每个命令间隔较短时间
        for do_index in range(1, 7):  # DO1-DO6
            cmd = ModbusUtils.build_do_command(slave_addr, do_index - 1, status)
            hex_cmd = binascii.hexlify(cmd).decode()
            
            action = "打开" if status == 1 else "关闭"
            logger.info(f"{action}DO{do_index}: 地址={slave_addr}, 命令={hex_cmd}")
            
            success = await self.netty_server.send_command(self.io_channel, hex_cmd)
            
            if success:
                # 更新缓存
                async with self.cache_lock:
                    key = f"{slave_addr}:{do_index}"
                    self.do_status_cache[key] = status
                
                results.append({
                    "do_index": do_index,
                    "success": True,
                    "message": f"DO{do_index}{action}成功"
                })
            else:
                results.append({
                    "do_index": do_index,
                    "success": False,
                    "message": f"DO{do_index}{action}失败"
                })
            
            # 短暂延迟避免命令冲突
            if do_index < 6:
                await asyncio.sleep(0.2)  # 略微增加间隔
        
        # 批量操作后等待
        logger.info(f"所有DO控制命令已发送，等待{self.IO_OPERATION_DELAY}秒让硬件响应...")
        await asyncio.sleep(self.IO_OPERATION_DELAY)
        
        return {
            "action": "open_all" if status == 1 else "close_all",
            "results": results
        }


class OptimizedPTZService(PTZService):
    """优化的云台服务 - 增加操作延迟"""
    
    PTZ_OPERATION_DELAY = 3.0  # 云台操作后等待3秒
    QUERY_TIMEOUT = 2.0  # 查询超时时间
    
    async def set_angle(self, ptz_addr: int, angle: float, ptz_type: PTZType) -> Tuple[bool, str]:
        """设置云台角度 - 优化版"""
        # 构建命令
        cmd = PTZProtocol.build_spin_angle_command(ptz_addr, angle, ptz_type)
        hex_cmd = binascii.hexlify(cmd).decode()
        
        type_name = "PAN" if ptz_type == PTZType.PAN else "TILT"
        logger.info(f"设置{type_name}角度: 地址={ptz_addr}, 角度={angle}°, 命令={hex_cmd}")
        
        # 发送命令
        success = await self.netty_server.send_command(self.ptz_channel, hex_cmd)
        
        if success:
            # 更新缓存
            if ptz_type == PTZType.PAN:
                self.cached_pan_angle = angle
            else:
                self.cached_tilt_angle = angle
            
            # 等待云台运动完成
            logger.info(f"{type_name}角度设置命令已发送，等待{self.PTZ_OPERATION_DELAY}秒让云台运动...")
            await asyncio.sleep(self.PTZ_OPERATION_DELAY)
            
            return True, f"{type_name}角度设置成功: {angle}°"
        
        return False, f"{type_name}角度设置失败"
    
    async def query_angle(self, ptz_addr: int, ptz_type: PTZType) -> Tuple[bool, float]:
        """查询云台角度 - 优化版"""
        # 构建查询命令
        cmd = PTZProtocol.build_query_angle_command(ptz_addr, ptz_type)
        hex_cmd = binascii.hexlify(cmd).decode()
        
        type_name = "PAN" if ptz_type == PTZType.PAN else "TILT"
        logger.info(f"查询{type_name}角度: 地址={ptz_addr}, 命令={hex_cmd}")
        
        # 发送命令并等待响应 - 使用更长的超时时间
        response = await self.netty_server.send_command_with_response(
            self.ptz_channel, hex_cmd, timeout=self.QUERY_TIMEOUT
        )
        
        if response:
            hex_response = binascii.hexlify(response).decode()
            logger.info(f"收到{type_name}角度响应: {hex_response}")
            
            # 根据Java代码的解析逻辑
            if len(response) >= 6:
                # 响应格式: FF [地址] 00 [59/5B] [角度高] [角度低] [校验和]
                if response[0] == 0xFF and response[1] == ptz_addr:
                    expected_code = bytes.fromhex(ptz_type.report_search_code)[0]
                    if response[3] == expected_code:
                        # 解析角度
                        angle_raw = (response[4] << 8) | response[5]
                        
                        if ptz_type == PTZType.PAN:
                            angle = angle_raw / 100.0
                            self.cached_pan_angle = angle
                        else:  # TILT
                            angle = (angle_raw / 100.0) - 90.0
                            self.cached_tilt_angle = angle
                        
                        return True, angle
        
        # 返回缓存的角度
        if ptz_type == PTZType.PAN:
            return False, self.cached_pan_angle
        else:
            return False, self.cached_tilt_angle


class OptimizedHardwareManager(HardwareManager):
    """优化的硬件管理器"""
    
    def __init__(self):
        """初始化优化的硬件管理器"""
        # 初始化网络服务器
        host = CONFIG.get('serial_server', {}).get('host', '**************')
        io_port = CONFIG.get('serial_server', {}).get('io_port', 7100)
        
        self.netty_server = NettyServer(host, io_port)
        
        # 使用优化的服务
        self.relay_service = OptimizedRelayService(self.netty_server)
        self.ptz_service = OptimizedPTZService(self.netty_server)
        
        # 设备状态
        self.device_power_on_times = {}
        
        # 业务状态
        self.business_active = False
        self.business_active_lock = asyncio.Lock()
        
        # 长期通电保护
        self.protection_enabled = True
        self.protection_task = None
        
        # 从配置读取参数
        protection_config = CONFIG.get('protection', {})
        self.PROTECTION_CHECK_INTERVAL = protection_config.get('check_interval', 600)
        self.MAX_POWER_ON_TIME = protection_config.get('max_power_on_time', 600)
        
        # 关键设备DO索引
        io_mapping = CONFIG.get('io_mapping', {}).get('do_channels', {})
        self.LIDAR_DO = next((v['index'] for k, v in io_mapping.items() if v.get('device') == 'lidar'), 1)
        self.PTZ_DO = next((v['index'] for k, v in io_mapping.items() if v.get('device') == 'ptz'), 2)
        self.CRITICAL_DEVICES = [self.LIDAR_DO, self.PTZ_DO]
    
    async def device_power_control(self, device: str, power: bool) -> Tuple[bool, str]:
        """设备电源控制 - 使用优化的服务"""
        # 根据设备名称获取DO索引
        device_do_map = {
            "lidar": self.LIDAR_DO,
            "ptz": self.PTZ_DO
        }
        
        do_index = device_do_map.get(device.lower())
        if not do_index:
            return False, f"未知设备: {device}"
        
        status = 1 if power else 0
        success, msg = await self.relay_service.control_do(
            self.DEFAULT_SLAVE_ADDR, do_index, status
        )
        
        if success:
            # 更新设备通电时间记录
            await self._update_power_on_time(do_index, status)
            
        return success, msg


async def main():
    """主函数 - 使用优化的组件"""
    # 创建优化的硬件管理器
    hardware_manager = OptimizedHardwareManager()
    
    # 启动Netty服务器
    await hardware_manager.netty_server.start()
    
    # 等待通道连接
    await hardware_manager.wait_for_channels()
    
    # 启动长期通电保护机制
    await hardware_manager._start_power_protection()
    
    # 创建REST服务器 - 使用优化的管理器
    rest_server = RESTServer(
        hardware_manager,
        hardware_manager.relay_service,
        hardware_manager.ptz_service
    )
    
    # 启动REST API
    runner = web.AppRunner(rest_server.app)
    await runner.setup()
    site = web.TCPSite(runner, '0.0.0.0', 7080)
    await site.start()
    
    logger.info("=" * 60)
    logger.info("优化版硬件控制服务已启动")
    logger.info(f"REST API: http://0.0.0.0:7080")
    logger.info(f"Netty Server: tcp://0.0.0.0:{hardware_manager.netty_server.port}")
    logger.info("主要优化：")
    logger.info("- IO操作后等待5秒")
    logger.info("- 查询超时增加到3秒")
    logger.info("- 云台操作后等待3秒")
    logger.info("=" * 60)
    
    # 保持运行
    try:
        await asyncio.Event().wait()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
    finally:
        # 清理资源
        await hardware_manager.stop_power_protection()
        await runner.cleanup()


if __name__ == "__main__":
    asyncio.run(main())