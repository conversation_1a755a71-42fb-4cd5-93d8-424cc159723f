"""
硬件控制服务修复补丁
修复以下问题：
1. DO查询超时导致保护机制失效
2. 业务激活后无时限保护
3. 设备通电时间记录缺失
"""

import asyncio
import logging
import time
from typing import Dict, Any

logger = logging.getLogger(__name__)

class HardwareManagerFix:
    """硬件管理器修复补丁"""
    
    # 业务活动最大时长（10分钟）
    MAX_BUSINESS_DURATION = 600  # 10分钟
    
    def __init__(self, original_manager):
        self.original_manager = original_manager
        self.business_start_times = {}  # 记录业务开始时间
        
    async def set_business_active_with_timeout(self, active: bool, business_type: str = "unknown"):
        """设置业务活跃状态（带超时）"""
        async with self.original_manager.business_active_lock:
            if active:
                # 记录业务开始时间
                self.business_start_times[business_type] = time.time()
                self.original_manager.business_active = True
                logger.info(f"业务状态变更: {business_type} 激活（最长{self.MAX_BUSINESS_DURATION/60}分钟）")
            else:
                # 清除业务时间记录
                if business_type in self.business_start_times:
                    duration = time.time() - self.business_start_times[business_type]
                    logger.info(f"业务状态变更: {business_type} 结束（持续{duration/60:.1f}分钟）")
                    del self.business_start_times[business_type]
                self.original_manager.business_active = False
                
                # 业务结束时，重置所有设备的通电计时
                current_time = time.time()
                for do_index in list(self.original_manager.device_power_on_times.keys()):
                    self.original_manager.device_power_on_times[do_index] = current_time
                    logger.info(f"重置DO{do_index}通电计时")
    
    async def check_and_protect_devices_enhanced(self):
        """增强的设备保护检查"""
        # 检查业务是否超时
        current_time = time.time()
        business_timeout = False
        
        async with self.original_manager.business_active_lock:
            if self.original_manager.business_active:
                # 检查所有活跃业务是否超时
                for business_type, start_time in list(self.business_start_times.items()):
                    if current_time - start_time > self.MAX_BUSINESS_DURATION:
                        logger.warning(f"业务 {business_type} 超时，自动结束")
                        del self.business_start_times[business_type]
                        business_timeout = True
                
                # 如果所有业务都超时了，结束业务状态
                if business_timeout and len(self.business_start_times) == 0:
                    self.original_manager.business_active = False
                    logger.info("所有业务已超时，恢复保护检查")
                elif not business_timeout:
                    logger.debug("业务活跃中，但仍执行基础保护检查")
                    # 即使业务活跃，也要检查是否有设备超过最大时限
                    # 这里可以设置一个更长的时限，比如30分钟
                    BUSINESS_MAX_POWER_TIME = 1800  # 30分钟
                    await self._check_devices_with_limit(BUSINESS_MAX_POWER_TIME)
                    return
        
        # 执行正常的保护检查
        await self._check_devices_with_limit(self.original_manager.MAX_POWER_ON_TIME)
    
    async def _check_devices_with_limit(self, max_time: int):
        """使用指定时限检查设备"""
        try:
            # 获取DO状态（使用缓存优先）
            do_status = await self._get_reliable_do_status()
            
            current_time = time.time()
            devices_to_shutdown = []
            
            # 检查关键设备
            for do_index in self.original_manager.CRITICAL_DEVICES:
                if do_index <= len(do_status) and do_status[do_index - 1] == 1:
                    # 设备处于通电状态
                    if do_index not in self.original_manager.device_power_on_times:
                        # 首次发现设备通电，记录时间
                        self.original_manager.device_power_on_times[do_index] = current_time
                        device_name = "激光雷达" if do_index == self.original_manager.LIDAR_DO else "云台"
                        logger.info(f"{device_name}(DO{do_index}) 首次检测到通电，开始计时")
                    else:
                        # 检查通电时长
                        power_on_duration = current_time - self.original_manager.device_power_on_times[do_index]
                        device_name = "激光雷达" if do_index == self.original_manager.LIDAR_DO else "云台"
                        
                        if power_on_duration > max_time:
                            devices_to_shutdown.append((do_index, device_name, power_on_duration))
                            logger.warning(f"检测到{device_name}(DO{do_index})长时间通电: {power_on_duration:.1f}秒，准备断电")
                else:
                    # 设备已关闭，清除记录
                    if do_index in self.original_manager.device_power_on_times:
                        del self.original_manager.device_power_on_times[do_index]
            
            # 执行保护性断电
            if devices_to_shutdown:
                logger.warning(f"执行长期通电保护，关闭 {len(devices_to_shutdown)} 个设备")
                
                for do_index, device_name, duration in devices_to_shutdown:
                    success, msg = await self.original_manager.relay_service.control_do(
                        self.original_manager.DEFAULT_SLAVE_ADDR, do_index, 0
                    )
                    if success:
                        logger.info(f"✓ {device_name}(DO{do_index})保护性断电成功，通电时长: {duration/60:.1f}分钟")
                        # 清除时间记录
                        if do_index in self.original_manager.device_power_on_times:
                            del self.original_manager.device_power_on_times[do_index]
                    else:
                        logger.error(f"✗ {device_name}(DO{do_index})保护性断电失败: {msg}")
        
        except Exception as e:
            logger.error(f"设备保护检查异常: {e}")
    
    async def _get_reliable_do_status(self):
        """获取可靠的DO状态（优先使用缓存）"""
        # 先尝试从缓存获取
        cached_status = [0] * 6
        has_cache = False
        
        async with self.original_manager.relay_service.cache_lock:
            for i in range(6):
                key = f"{self.original_manager.DEFAULT_SLAVE_ADDR}:{i+1}"
                if key in self.original_manager.relay_service.do_status_cache:
                    cached_status[i] = self.original_manager.relay_service.do_status_cache[key]
                    has_cache = True
        
        if has_cache:
            logger.debug(f"使用缓存DO状态: {cached_status}")
            return cached_status
        
        # 如果没有缓存，尝试查询（但不依赖结果）
        try:
            success, do_data = await self.original_manager.relay_service.query_do_status(
                self.original_manager.DEFAULT_SLAVE_ADDR
            )
            if success:
                return do_data.get("do_status", [0] * 6)
        except:
            pass
        
        # 返回默认状态
        logger.warning("无法获取DO状态，使用默认值")
        return [0] * 6


class RelayServiceFix:
    """继电器服务修复补丁"""
    
    def __init__(self, original_service, hardware_manager):
        self.original_service = original_service
        self.hardware_manager = hardware_manager
        
    async def control_do_with_tracking(self, slave_addr: int, do_index: int, status: int):
        """控制DO并记录通电时间"""
        # 调用原始的control_do
        success, msg = await self.original_service.control_do(slave_addr, do_index, status)
        
        if success:
            # 更新通电时间记录
            await self._update_power_on_time(do_index, status)
            
        return success, msg
    
    async def _update_power_on_time(self, do_index: int, status: int):
        """更新设备通电时间记录"""
        current_time = time.time()
        
        if status == 1:  # 通电
            self.hardware_manager.device_power_on_times[do_index] = current_time
            device_name = "激光雷达" if do_index == self.hardware_manager.LIDAR_DO else "云台"
            logger.info(f"{device_name}(DO{do_index})通电，开始计时")
        else:  # 断电
            if do_index in self.hardware_manager.device_power_on_times:
                power_on_duration = current_time - self.hardware_manager.device_power_on_times[do_index]
                device_name = "激光雷达" if do_index == self.hardware_manager.LIDAR_DO else "云台"
                logger.info(f"{device_name}(DO{do_index})断电，通电时长: {power_on_duration/60:.1f}分钟")
                del self.hardware_manager.device_power_on_times[do_index]


def apply_fixes(hardware_manager, relay_service):
    """应用修复补丁"""
    logger.info("应用硬件控制服务修复补丁...")
    
    # 创建修复实例
    hw_fix = HardwareManagerFix(hardware_manager)
    relay_fix = RelayServiceFix(relay_service, hardware_manager)
    
    # 替换原有方法
    hardware_manager.set_business_active = hw_fix.set_business_active_with_timeout
    hardware_manager._check_and_protect_devices = hw_fix.check_and_protect_devices_enhanced
    
    # 包装relay_service的control_do方法
    original_control_do = relay_service.control_do
    relay_service.control_do = relay_fix.control_do_with_tracking
    
    logger.info("修复补丁应用完成：")
    logger.info("- 业务活动最大时长: 10分钟")
    logger.info("- 业务期间最大通电时间: 30分钟")
    logger.info("- DO控制自动记录通电时间")
    logger.info("- 增强的保护机制检查")
    
    return hw_fix, relay_fix


if __name__ == "__main__":
    # 测试代码
    print("硬件控制服务修复补丁")
    print("修复内容：")
    print("1. DO查询超时不影响保护机制")
    print("2. 业务活动10分钟自动超时")
    print("3. 自动记录设备通电时间")
    print("4. 业务期间仍有30分钟保护限制")