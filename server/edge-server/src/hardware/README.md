# 硬件控制系统 - Final版本

本目录包含边缘服务器硬件控制系统的最终实现版本。

## 核心服务文件

### 1. hardware_control_service.py
主硬件控制服务，包含：
- Netty TCP服务器（端口7100）
- PTZ云台控制（角度、速度）
- IO继电器控制
- REST API服务（端口8080）

### 2. scan_service.py
激光雷达扫描业务逻辑：
- 定点扫描（单点10Hz采集）
- 地形扫描（多层360度扫描）
- 数据采集管理

### 3. lidar_protection_service.py
雷达自动保护服务：
- 每10分钟检查雷达状态
- 非业务时间自动断电
- 防止设备长时间运行

### 4. state_manager_and_rpc_service.py
设备状态管理和RPC服务：
- 设备状态统一管理
- JSON-RPC 2.0协议支持
- WebSocket实时状态推送
- Redis持久化支持（可选）

## 文档

### PTZ_CONTROL_COMPLETE_REFERENCE.md
PTZ云台控制完整代码参考，包含：
- 所有PTZ控制协议实现
- 角度/速度设置和查询
- REST API接口定义
- 集成示例代码

### TEST_REPORT.md
完整的测试报告，包含所有功能测试结果。

## 测试脚本

- `integrated_test.py` - 综合功能测试
- `test_ptz_complete.py` - PTZ完整功能测试
- `test_io_complete.py` - IO控制完整测试
- `test_lidar_business.py` - 激光雷达业务逻辑测试

## 快速启动

```bash
# 启动硬件控制服务
python3 hardware_control_service.py

# 启动雷达保护服务（可选）
python3 lidar_protection_service.py

# 运行综合测试
python3 integrated_test.py
```

## API端点

- REST API: http://localhost:8080
- RPC服务: http://localhost:8090 (需单独启动)
- Netty TCP: 0.0.0.0:7100

## 硬件配置

- 激光雷达: DO1, IP *************
- 云台: DO2, 地址30
- 串口服务器: *************