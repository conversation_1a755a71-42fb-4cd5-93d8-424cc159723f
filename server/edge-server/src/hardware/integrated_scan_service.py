"""
集成扫描服务
整合硬件控制和激光雷达数据采集功能
"""

from enum import Enum
from typing import Dict, Any, Tuple, Optional, Callable
import asyncio
import logging
import numpy as np
from datetime import datetime
from pathlib import Path
import json
import time

logger = logging.getLogger(__name__)


class ScanMode(Enum):
    """扫描模式枚举"""
    AREA = "area"
    LINEAR = "linear"
    SINGLE_POINT = "single_point"
    VERTICAL = "vertical"


class DataAcquisitionMode(Enum):
    """数据采集模式枚举"""
    CONTINUOUS = "continuous"
    TRIGGERED = "triggered"
    MANUAL = "manual"


class IntegratedScanService:
    """集成扫描服务类"""
    
    def __init__(self, lidar_config=None, hardware_config=None):
        self.lidar_config = lidar_config or {}
        self.hardware_config = hardware_config or {}
        self.is_scanning = False
        self.current_scan_mode = None
        self.current_acquisition_mode = None
        self.current_scan_task = None
        self._scan_data = {}
        
        # 导入激光雷达控制器
        try:
            from ..lidar.scan_controller import LidarController
            self.lidar_controller = LidarController(lidar_config) if lidar_config else None
        except Exception as e:
            logger.warning(f"无法导入激光雷达控制器: {e}")
            self.lidar_controller = None
            
        # 硬件控制API
        self.hardware_api_url = hardware_config.get('base_url', 'http://localhost:7080')
        self.lidar_ip = hardware_config.get('lidar_ip', '*************')
        
        logger.info("集成扫描服务初始化完成")
    
    async def start_scan(self, mode: ScanMode, acquisition_mode: DataAcquisitionMode, **kwargs) -> Dict[str, Any]:
        """启动扫描"""
        self.is_scanning = True
        self.current_scan_mode = mode
        self.current_acquisition_mode = acquisition_mode
        logger.info(f"启动扫描 - 模式: {mode.value}, 采集模式: {acquisition_mode.value}")
        return {"status": "success", "message": "扫描已启动（临时实现）"}
    
    async def stop_scan(self) -> Dict[str, Any]:
        """停止扫描"""
        self.is_scanning = False
        logger.info("停止扫描")
        return {"status": "success", "message": "扫描已停止（临时实现）"}
    
    def get_scan_status(self) -> Dict[str, Any]:
        """获取扫描状态"""
        return {
            "is_scanning": self.is_scanning,
            "scan_mode": self.current_scan_mode.value if self.current_scan_mode else None,
            "acquisition_mode": self.current_acquisition_mode.value if self.current_acquisition_mode else None,
            "current_task": self.current_scan_task
        }
    
    async def configure_scan_parameters(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """配置扫描参数"""
        logger.info(f"配置扫描参数: {parameters}")
        return {"status": "success", "message": "参数已配置（临时实现）"}
    
    async def initialize(self) -> Dict[str, Any]:
        """初始化服务"""
        logger.info("初始化集成扫描服务")
        # 临时实现，返回成功状态
        return {"status": "success", "message": "服务已初始化（临时实现）"}
    
    def shutdown(self):
        """关闭服务"""
        logger.info("关闭集成扫描服务")
        if self.is_scanning:
            asyncio.create_task(self.stop_scan())
        self.is_scanning = False
        self.current_scan_task = None
    
    async def execute_point_scan(self, task_id: str, pan_angle: float, tilt_angle: float, 
                                duration: float, data_mode: str = "continuous") -> Dict[str, Any]:
        """执行定点扫描"""
        logger.info(f"执行定点扫描 - 任务ID: {task_id}, 水平角: {pan_angle}, 垂直角: {tilt_angle}, 持续时间: {duration}s")
        
        try:
            self.is_scanning = True
            self.current_scan_task = {
                "task_id": task_id,
                "type": "point_scan",
                "pan_angle": pan_angle,
                "tilt_angle": tilt_angle,
                "duration": duration,
                "start_time": datetime.utcnow()
            }
            
            # 1. 启动设备
            await self._power_on_devices()
            
            # 2. 移动云台到指定位置
            await self._move_ptz(pan_angle, tilt_angle)
            
            # 3. 执行数据采集
            output_dir = Path(f"/data/lidar/scans/{task_id}")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            if self.lidar_controller:
                # 真实数据采集
                await self.lidar_controller.connect()
                scan_files = await self._collect_point_scan_data(output_dir, duration)
                await self.lidar_controller.disconnect()
            else:
                # 模拟数据
                scan_files = await self._simulate_point_scan(output_dir, duration)
            
            # 4. 云台归零
            await self._move_ptz_home()
            
            # 5. 关闭设备
            await self._power_off_devices()
            
            self.is_scanning = False
            
            return {
                "status": "success",
                "output_dir": str(output_dir),
                "scan_files": scan_files,
                "frame_count": len(scan_files),
                "message": "定点扫描完成"
            }
            
        except Exception as e:
            self.is_scanning = False
            logger.error(f"定点扫描失败: {e}")
            await self._emergency_shutdown()
            return {
                "status": "error",
                "error": str(e),
                "message": "定点扫描失败"
            }
    
    async def execute_terrain_scan(self, task_id: str, pan_range: Tuple[float, float], 
                                  tilt_range: Tuple[float, float], pan_step: float, 
                                  tilt_step: float, scan_duration_per_position: float,
                                  data_mode: str = "accumulate") -> Dict[str, Any]:
        """执行地形扫描"""
        logger.info(f"执行地形扫描 - 任务ID: {task_id}, 水平范围: {pan_range}, 垂直范围: {tilt_range}")
        
        try:
            self.is_scanning = True
            self.current_scan_task = {
                "task_id": task_id,
                "type": "terrain_scan",
                "pan_range": pan_range,
                "tilt_range": tilt_range,
                "pan_step": pan_step,
                "tilt_step": tilt_step,
                "start_time": datetime.utcnow()
            }
            
            # 1. 启动设备
            await self._power_on_devices()
            
            # 2. 创建输出目录
            output_dir = Path(f"/data/lidar/terrain/{task_id}")
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # 3. 计算扫描路径（蛇形优化）
            scan_path = self._calculate_snake_path(pan_range, tilt_range, pan_step, tilt_step)
            total_positions = len(scan_path)
            
            # 4. 执行扫描
            all_points = []
            for idx, (pan, tilt) in enumerate(scan_path):
                if not self.is_scanning:
                    break
                    
                logger.info(f"扫描位置 {idx+1}/{total_positions}: 水平{pan}°, 垂直{tilt}°")
                
                # 移动云台
                await self._move_ptz(pan, tilt)
                
                # 采集数据
                if self.lidar_controller:
                    points = await self._collect_position_data(scan_duration_per_position)
                else:
                    points = self._generate_mock_points(46080 * int(scan_duration_per_position))
                
                all_points.append(points)
                
                # 保存位置数据
                pos_dir = output_dir / f"pos_{idx:03d}_pan{int(pan)}_tilt{int(tilt)}"
                pos_dir.mkdir(exist_ok=True)
                await self._save_mock_pcd(points, pos_dir / "accumulated.pcd")
            
            # 5. 合并所有点云
            if all_points:
                merged_points = np.vstack(all_points)
                await self._save_mock_pcd(merged_points, output_dir / "terrain_scan.pcd")
            
            # 6. 云台归零
            await self._move_ptz_home()
            
            # 7. 关闭设备
            await self._power_off_devices()
            
            self.is_scanning = False
            
            return {
                "status": "success",
                "output_file": str(output_dir / "terrain_scan.pcd"),
                "output_dir": str(output_dir),
                "total_points": len(merged_points) if all_points else 0,
                "scan_positions": len(scan_path),
                "message": "地形扫描完成"
            }
            
        except Exception as e:
            self.is_scanning = False
            logger.error(f"地形扫描失败: {e}")
            await self._emergency_shutdown()
            return {
                "status": "error",
                "error": str(e),
                "message": "地形扫描失败"
            }
    
    def _calculate_snake_path(self, pan_range: Tuple[float, float], tilt_range: Tuple[float, float],
                             pan_step: float, tilt_step: float) -> list:
        """计算蛇形扫描路径"""
        path = []
        tilt_values = np.arange(tilt_range[0], tilt_range[1] + tilt_step, tilt_step)
        
        for i, tilt in enumerate(tilt_values):
            pan_values = np.arange(pan_range[0], pan_range[1] + pan_step, pan_step)
            
            # 奇数层反向扫描
            if i % 2 == 1:
                pan_values = pan_values[::-1]
                
            for pan in pan_values:
                path.append((pan, tilt))
                
        return path
    
    async def _collect_position_data(self, duration: float) -> np.ndarray:
        """采集单个位置的数据"""
        points_list = []
        start_time = time.time()
        
        while time.time() - start_time < duration:
            frame = await self.lidar_controller.capture_single_frame()
            if frame and frame.points is not None:
                points_list.append(frame.points)
            await asyncio.sleep(0.1)
            
        # 合并所有点
        if points_list:
            return np.vstack(points_list)
        else:
            return np.empty((0, 4))
    
    async def stop_current_scan(self) -> Dict[str, Any]:
        """停止当前扫描"""
        logger.info("停止当前扫描")
        self.is_scanning = False
        
        # 立即执行安全关机
        await self._emergency_shutdown()
        
        self.current_scan_task = None
        return {"status": "success", "message": "扫描已停止"}
    
    async def _power_on_devices(self):
        """启动设备"""
        logger.info("启动激光雷达和云台")
        # 这里应该调用硬件API，目前简化实现
        await asyncio.sleep(10)  # 等待设备初始化
    
    async def _power_off_devices(self):
        """关闭设备"""
        logger.info("关闭激光雷达和云台")
        await asyncio.sleep(1)
    
    async def _move_ptz(self, pan: float, tilt: float):
        """移动云台"""
        logger.info(f"移动云台到: 水平{pan}°, 垂直{tilt}°")
        await asyncio.sleep(3)  # 等待云台到位
    
    async def _move_ptz_home(self):
        """云台归零"""
        logger.info("云台归零")
        await self._move_ptz(0, 0)
    
    async def _emergency_shutdown(self):
        """紧急关机"""
        logger.warning("执行紧急关机")
        await self._move_ptz_home()
        await self._power_off_devices()
    
    async def _collect_point_scan_data(self, output_dir: Path, duration: float) -> list:
        """采集定点扫描数据"""
        scan_files = []
        frame_count = 0
        start_time = time.time()
        
        # 设置帧回调
        async def save_frame(frame):
            nonlocal frame_count
            frame_count += 1
            filename = f"frame_{frame_count:06d}.pcd"
            filepath = output_dir / filename
            await self.lidar_controller.save_pcd(frame, filepath)
            scan_files.append(filename)
        
        # 启动流式采集
        await self.lidar_controller.start_streaming(save_frame)
        
        # 等待采集完成
        while time.time() - start_time < duration:
            await asyncio.sleep(0.1)
            
        # 停止采集
        await self.lidar_controller.stop_streaming()
        
        logger.info(f"定点扫描完成，采集{frame_count}帧")
        return scan_files
    
    async def _simulate_point_scan(self, output_dir: Path, duration: float) -> list:
        """模拟定点扫描数据"""
        scan_files = []
        frames = int(duration * 10)  # 10Hz
        
        for i in range(frames):
            # 生成模拟点云
            points = self._generate_mock_points(46080)
            
            # 保存PCD文件
            filename = f"frame_{i+1:06d}.pcd"
            filepath = output_dir / filename
            await self._save_mock_pcd(points, filepath)
            scan_files.append(filename)
            
            await asyncio.sleep(0.1)  # 10Hz
            
        return scan_files
    
    def _generate_mock_points(self, count: int) -> np.ndarray:
        """生成模拟点云数据"""
        x = np.random.uniform(-50, 50, count)
        y = np.random.uniform(-50, 50, count)
        z = np.random.uniform(-5, 5, count)
        intensity = np.random.randint(0, 255, count)
        return np.column_stack([x, y, z, intensity])
    
    async def _save_mock_pcd(self, points: np.ndarray, filepath: Path):
        """保存模拟点云为PCD文件"""
        header = f"""# .PCD v0.7 - Point Cloud Data file format
VERSION 0.7
FIELDS x y z intensity
SIZE 4 4 4 4
TYPE F F F F
COUNT 1 1 1 1
WIDTH {len(points)}
HEIGHT 1
VIEWPOINT 0 0 0 1 0 0 0
POINTS {len(points)}
DATA ascii
"""
        
        with open(filepath, 'w') as f:
            f.write(header)
            for point in points:
                f.write(f"{point[0]:.6f} {point[1]:.6f} {point[2]:.6f} {point[3]:.0f}\n")