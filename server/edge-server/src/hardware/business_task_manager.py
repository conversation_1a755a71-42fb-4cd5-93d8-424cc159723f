"""
业务任务管理器
确保任务完成后自动关闭设备
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, Set
from enum import Enum

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """任务状态"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"


class BusinessTaskManager:
    """业务任务管理器"""
    
    def __init__(self, hardware_manager):
        self.hardware_manager = hardware_manager
        self.active_tasks: Dict[str, Dict[str, Any]] = {}
        self.task_devices: Dict[str, Set[int]] = {}  # 任务使用的设备
        self.cleanup_tasks = {}  # 清理任务
        
    async def register_task(self, task_id: str, task_type: str, 
                          expected_duration: int = 600,
                          auto_cleanup: bool = True) -> bool:
        """注册业务任务"""
        logger.info(f"注册业务任务: {task_id} ({task_type})")
        
        self.active_tasks[task_id] = {
            'type': task_type,
            'status': TaskStatus.PENDING,
            'start_time': None,
            'expected_duration': expected_duration,
            'auto_cleanup': auto_cleanup,
            'devices': set()
        }
        
        return True
    
    async def start_task(self, task_id: str) -> bool:
        """开始执行任务"""
        if task_id not in self.active_tasks:
            logger.error(f"未知任务: {task_id}")
            return False
        
        task = self.active_tasks[task_id]
        task['status'] = TaskStatus.RUNNING
        task['start_time'] = time.time()
        
        # 激活业务保护
        await self.hardware_manager.set_business_active(True, task['type'])
        
        logger.info(f"任务 {task_id} 开始执行")
        
        # 启动超时监控
        if task['expected_duration'] > 0:
            self.cleanup_tasks[task_id] = asyncio.create_task(
                self._task_timeout_monitor(task_id, task['expected_duration'])
            )
        
        return True
    
    async def register_device_usage(self, task_id: str, do_index: int):
        """注册任务使用的设备"""
        if task_id not in self.task_devices:
            self.task_devices[task_id] = set()
        
        self.task_devices[task_id].add(do_index)
        
        if task_id in self.active_tasks:
            self.active_tasks[task_id]['devices'].add(do_index)
        
        device_name = "激光雷达" if do_index == 1 else "云台" if do_index == 2 else f"设备{do_index}"
        logger.debug(f"任务 {task_id} 注册使用 {device_name}(DO{do_index})")
    
    async def complete_task(self, task_id: str, success: bool = True) -> bool:
        """完成任务"""
        if task_id not in self.active_tasks:
            logger.error(f"未知任务: {task_id}")
            return False
        
        task = self.active_tasks[task_id]
        duration = time.time() - task['start_time'] if task['start_time'] else 0
        
        task['status'] = TaskStatus.COMPLETED if success else TaskStatus.FAILED
        logger.info(f"任务 {task_id} 完成，状态: {task['status'].value}，耗时: {duration/60:.1f}分钟")
        
        # 取消超时监控
        if task_id in self.cleanup_tasks:
            self.cleanup_tasks[task_id].cancel()
            del self.cleanup_tasks[task_id]
        
        # 自动清理
        if task['auto_cleanup']:
            await self._cleanup_task(task_id)
        
        return True
    
    async def _cleanup_task(self, task_id: str):
        """清理任务资源"""
        logger.info(f"清理任务 {task_id} 资源")
        
        if task_id not in self.active_tasks:
            return
        
        task = self.active_tasks[task_id]
        
        # 1. 关闭任务使用的设备
        devices_to_close = task.get('devices', set())
        if task_id in self.task_devices:
            devices_to_close.update(self.task_devices[task_id])
        
        for do_index in devices_to_close:
            device_name = "激光雷达" if do_index == 1 else "云台" if do_index == 2 else f"设备{do_index}"
            logger.info(f"关闭 {device_name}(DO{do_index})")
            
            # 如果是云台，先归零
            if do_index == 2:
                try:
                    await self.hardware_manager.ptz_service.move_to_angle(30, 0, 0)
                    await asyncio.sleep(2)
                    logger.info("云台已归零")
                except Exception as e:
                    logger.error(f"云台归零失败: {e}")
            
            # 关闭设备
            try:
                success, msg = await self.hardware_manager.relay_service.control_do(1, do_index, 0)
                if success:
                    logger.info(f"✓ {device_name} 已关闭")
                else:
                    logger.error(f"✗ {device_name} 关闭失败: {msg}")
            except Exception as e:
                logger.error(f"关闭设备异常: {e}")
        
        # 2. 清理记录
        del self.active_tasks[task_id]
        if task_id in self.task_devices:
            del self.task_devices[task_id]
        
        # 3. 如果没有其他活跃任务，结束业务状态
        if not self.active_tasks:
            logger.info("所有任务已完成，结束业务状态")
            await self.hardware_manager.set_business_active(False, task['type'])
            
            # 确保所有关键设备都已关闭
            await self._ensure_all_devices_off()
    
    async def _ensure_all_devices_off(self):
        """确保所有关键设备关闭"""
        logger.info("确保所有关键设备关闭...")
        
        # 关闭所有关键设备
        for do_index in [1, 2]:  # 激光雷达和云台
            try:
                success, msg = await self.hardware_manager.relay_service.control_do(1, do_index, 0)
                if success:
                    device_name = "激光雷达" if do_index == 1 else "云台"
                    logger.info(f"✓ {device_name} 确认关闭")
            except:
                pass
    
    async def _task_timeout_monitor(self, task_id: str, timeout: int):
        """任务超时监控"""
        try:
            await asyncio.sleep(timeout)
            
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                if task['status'] == TaskStatus.RUNNING:
                    logger.warning(f"任务 {task_id} 超时（{timeout/60:.0f}分钟）")
                    task['status'] = TaskStatus.TIMEOUT
                    await self._cleanup_task(task_id)
                    
        except asyncio.CancelledError:
            logger.debug(f"任务 {task_id} 超时监控已取消")
    
    async def emergency_stop_all(self):
        """紧急停止所有任务"""
        logger.warning("紧急停止所有任务")
        
        # 复制任务列表，避免迭代时修改
        task_ids = list(self.active_tasks.keys())
        
        for task_id in task_ids:
            await self.complete_task(task_id, success=False)
        
        # 确保所有设备关闭
        await self._ensure_all_devices_off()
    
    def get_active_tasks(self) -> Dict[str, Any]:
        """获取活跃任务列表"""
        result = {}
        current_time = time.time()
        
        for task_id, task in self.active_tasks.items():
            runtime = 0
            if task['start_time']:
                runtime = current_time - task['start_time']
            
            result[task_id] = {
                'type': task['type'],
                'status': task['status'].value,
                'runtime_minutes': runtime / 60,
                'devices': list(task.get('devices', [])),
                'auto_cleanup': task['auto_cleanup']
            }
        
        return result


# 集成到硬件管理器
def integrate_task_manager(hardware_manager):
    """集成任务管理器到硬件管理器"""
    task_manager = BusinessTaskManager(hardware_manager)
    hardware_manager.task_manager = task_manager
    
    # 包装设备控制函数，自动记录设备使用
    original_control_do = hardware_manager.relay_service.control_do
    
    async def control_do_with_tracking(slave_addr: int, do_index: int, status: int):
        result = await original_control_do(slave_addr, do_index, status)
        
        # 如果是开启设备，记录到当前任务
        if status == 1 and hasattr(hardware_manager, 'current_task_id'):
            await task_manager.register_device_usage(
                hardware_manager.current_task_id, do_index
            )
        
        return result
    
    hardware_manager.relay_service.control_do = control_do_with_tracking
    
    logger.info("业务任务管理器已集成")
    return task_manager


if __name__ == "__main__":
    print("业务任务管理器")
    print("功能：")
    print("1. 任务注册和跟踪")
    print("2. 设备使用记录")
    print("3. 任务完成自动清理")
    print("4. 超时保护")
    print("5. 紧急停止")