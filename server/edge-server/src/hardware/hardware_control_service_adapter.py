#!/usr/bin/env python3
"""
硬件控制服务路由适配器
为了兼容不同版本的API调用，添加路由别名
"""

import logging
from aiohttp import web

logger = logging.getLogger(__name__)


def add_compatibility_routes(app: web.Application):
    """添加兼容性路由"""
    
    # 获取原始路由处理器
    routes = {}
    for route in app.router._resources:
        if hasattr(route, '_routes'):
            for r in route._routes:
                if hasattr(r, '_handler'):
                    path = str(route.canonical)
                    routes[path] = r._handler
    
    # 添加兼容路由映射
    compatibility_map = {
        # IO状态查询
        '/api/status/io': routes.get('/api/io/status'),
        '/api/status/do': routes.get('/api/io/status'),
        
        # IO控制
        '/api/control/do': routes.get('/api/io/control'),
        '/api/control/all_do': routes.get('/api/io/all'),
        
        # PTZ控制
        '/api/control/ptz': routes.get('/api/ptz/angle'),
        '/api/status/ptz': routes.get('/api/ptz/angle'),
        
        # 设备状态
        '/api/status/all': routes.get('/api/device/status'),
        
        # 业务保护
        '/api/business/active': routes.get('/api/protection/business_active'),
    }
    
    # 注册兼容路由
    for path, handler in compatibility_map.items():
        if handler:
            # 支持GET和POST
            app.router.add_get(path, handler)
            app.router.add_post(path, handler)
            logger.info(f"Added compatibility route: {path}")


class CompatibilityMiddleware:
    """兼容性中间件 - 处理参数格式转换"""
    
    @web.middleware
    async def middleware(request, handler):
        # 参数转换映射
        if request.path == '/api/control/do' and request.method == 'POST':
            try:
                data = await request.json()
                # 转换参数格式
                if 'do_id' in data:
                    data['do_index'] = data.pop('do_id')
                if 'state' in data:
                    data['status'] = 1 if data.pop('state') else 0
                
                # 创建新的请求
                request._cache['json'] = data
            except:
                pass
        
        # 继续处理请求
        response = await handler(request)
        
        # 响应格式适配
        if request.path in ['/api/status/io', '/api/status/do'] and response.status == 200:
            try:
                data = await response.json()
                # 添加data包装
                if 'success' not in data:
                    data = {
                        'success': True,
                        'data': data
                    }
                response.body = web.json_response(data).body
            except:
                pass
        
        return response