#!/usr/bin/env python3
"""
集成状态管理的硬件控制服务
在原有硬件控制服务基础上集成状态管理器
实现状态自动更新和同步
"""

import asyncio
import aiohttp
from aiohttp import web
import binascii
import logging
import struct
import threading
import time
import yaml
import os
from typing import Dict, Optional, Tuple, Any, List
from datetime import datetime
from enum import Enum

# 导入基础服务
from hardware_control_service_optimized import (
    OptimizedRelayService, OptimizedPTZService, OptimizedHardwareManager,
    NettyServer, RESTServer, CONFIG
)
from state_manager_and_rpc_service import DeviceStateManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class StateAwareRelayService(OptimizedRelayService):
    """具有状态感知的继电器服务"""
    
    def __init__(self, netty_server: NettyServer, state_manager: <PERSON>ceStateManager):
        super().__init__(netty_server)
        self.state_manager = state_manager
    
    async def control_do(self, slave_addr: int, do_index: int, status: int) -> Tuple[bool, str]:
        """控制单个DO - 带状态更新"""
        success, msg = await super().control_do(slave_addr, do_index, status)
        
        # 更新状态管理器
        if success and self.state_manager:
            self.state_manager.update_io_state(do_index, bool(status))
            logger.info(f"状态管理器已更新: DO{do_index} = {bool(status)}")
        
        return success, msg
    
    async def query_do_status(self, slave_addr: int) -> Tuple[bool, Dict]:
        """查询所有DO状态 - 带状态更新"""
        success, data = await super().query_do_status(slave_addr)
        
        # 更新状态管理器
        if success and self.state_manager:
            do_status = data.get('do_status', [])
            for i, state in enumerate(do_status):
                channel = i + 1
                self.state_manager.update_io_state(channel, bool(state))
            logger.info(f"状态管理器已更新所有DO状态: {do_status}")
        
        return success, data


class StateAwarePTZService(OptimizedPTZService):
    """具有状态感知的云台服务"""
    
    def __init__(self, netty_server: NettyServer, state_manager: DeviceStateManager):
        super().__init__(netty_server)
        self.state_manager = state_manager
    
    async def set_angle(self, ptz_addr: int, angle: float, ptz_type) -> Tuple[bool, str]:
        """设置云台角度 - 带状态更新"""
        success, msg = await super().set_angle(ptz_addr, angle, ptz_type)
        
        # 更新状态管理器
        if success and self.state_manager:
            axis = "pan" if ptz_type.name == "PAN" else "tilt"
            self.state_manager.update_ptz_state(
                ptz_addr, axis,
                angle=angle,
                target_angle=angle,
                is_moving=True
            )
            logger.info(f"状态管理器已更新: PTZ {axis} = {angle}°")
        
        return success, msg
    
    async def query_angle(self, ptz_addr: int, ptz_type) -> Tuple[bool, float]:
        """查询云台角度 - 带状态更新"""
        success, angle = await super().query_angle(ptz_addr, ptz_type)
        
        # 更新状态管理器
        if success and self.state_manager:
            axis = "pan" if ptz_type.name == "PAN" else "tilt"
            self.state_manager.update_ptz_state(
                ptz_addr, axis,
                angle=angle,
                is_moving=False
            )
            logger.info(f"状态管理器已更新: PTZ {axis} = {angle}°")
        
        return success, angle
    
    async def set_speed(self, ptz_addr: int, speed: float, ptz_type) -> Tuple[bool, str]:
        """设置云台速度 - 带状态更新"""
        success, msg = await super().set_speed(ptz_addr, speed, ptz_type)
        
        # 更新状态管理器
        if success and self.state_manager:
            axis = "pan" if ptz_type.name == "PAN" else "tilt"
            speed_index = self.speed_manager.get_speed_index(speed, ptz_type)
            self.state_manager.update_ptz_state(
                ptz_addr, axis,
                speed=speed,
                speed_index=speed_index
            )
            logger.info(f"状态管理器已更新: PTZ {axis} 速度 = {speed}")
        
        return success, msg


class StateAwareHardwareManager(OptimizedHardwareManager):
    """具有状态感知的硬件管理器"""
    
    def __init__(self, state_manager: DeviceStateManager = None):
        """初始化状态感知的硬件管理器"""
        # 创建或使用提供的状态管理器
        self.state_manager = state_manager or DeviceStateManager()
        
        # 初始化网络服务器
        host = CONFIG.get('serial_server', {}).get('host', '**************')
        io_port = CONFIG.get('serial_server', {}).get('io_port', 7100)
        
        self.netty_server = NettyServer(host, io_port)
        
        # 使用状态感知的服务
        self.relay_service = StateAwareRelayService(self.netty_server, self.state_manager)
        self.ptz_service = StateAwarePTZService(self.netty_server, self.state_manager)
        
        # 其他初始化
        self.device_power_on_times = {}
        self.business_active = False
        self.business_active_lock = asyncio.Lock()
        self.protection_enabled = True
        self.protection_task = None
        
        # 从配置读取参数
        protection_config = CONFIG.get('protection', {})
        self.PROTECTION_CHECK_INTERVAL = protection_config.get('check_interval', 600)
        self.MAX_POWER_ON_TIME = protection_config.get('max_power_on_time', 600)
        
        # 关键设备DO索引
        io_mapping = CONFIG.get('io_mapping', {}).get('do_channels', {})
        self.LIDAR_DO = next((v['index'] for k, v in io_mapping.items() if v.get('device') == 'lidar'), 1)
        self.PTZ_DO = next((v['index'] for k, v in io_mapping.items() if v.get('device') == 'ptz'), 2)
        self.CRITICAL_DEVICES = [self.LIDAR_DO, self.PTZ_DO]
    
    async def device_power_control(self, device: str, power: bool) -> Tuple[bool, str]:
        """设备电源控制 - 带状态更新"""
        success, msg = await super().device_power_control(device, power)
        
        # 更新业务状态
        if success and self.state_manager:
            if device.lower() == "lidar" and power:
                self.state_manager.update_business_state(
                    scanning=False,  # 准备扫描
                    scan_type=None
                )
        
        return success, msg
    
    async def emergency_shutdown(self) -> Dict[str, Any]:
        """紧急关闭所有设备 - 带状态更新"""
        results = await super().emergency_shutdown()
        
        # 更新业务状态
        if self.state_manager:
            self.state_manager.update_business_state(
                scanning=False,
                emergency_stop=True,
                end_time=datetime.now().isoformat()
            )
        
        return results
    
    def get_state_manager(self) -> DeviceStateManager:
        """获取状态管理器"""
        return self.state_manager


class StateAwareRESTServer(RESTServer):
    """具有状态感知的REST服务器"""
    
    def __init__(self, hardware_manager: StateAwareHardwareManager, 
                 relay_service: StateAwareRelayService,
                 ptz_service: StateAwarePTZService):
        super().__init__(hardware_manager, relay_service, ptz_service)
        self.state_manager = hardware_manager.get_state_manager()
        
        # 添加状态相关的路由
        self.app.router.add_get('/api/state/full', self.handle_get_full_state)
        self.app.router.add_get('/api/state/io', self.handle_get_io_state)
        self.app.router.add_get('/api/state/ptz', self.handle_get_ptz_state)
        self.app.router.add_get('/api/state/business', self.handle_get_business_state)
    
    async def handle_get_full_state(self, request):
        """获取完整状态"""
        try:
            full_state = self.state_manager.get_full_state()
            return web.json_response({
                "success": True,
                "data": full_state
            })
        except Exception as e:
            logger.error(f"获取完整状态失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def handle_get_io_state(self, request):
        """获取IO状态"""
        try:
            full_state = self.state_manager.get_full_state()
            io_states = full_state.get("io_states", {})
            return web.json_response({
                "success": True,
                "data": io_states
            })
        except Exception as e:
            logger.error(f"获取IO状态失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def handle_get_ptz_state(self, request):
        """获取PTZ状态"""
        try:
            full_state = self.state_manager.get_full_state()
            ptz_states = full_state.get("ptz_states", {})
            return web.json_response({
                "success": True,
                "data": ptz_states
            })
        except Exception as e:
            logger.error(f"获取PTZ状态失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def handle_get_business_state(self, request):
        """获取业务状态"""
        try:
            full_state = self.state_manager.get_full_state()
            business_state = full_state.get("business_state", {})
            return web.json_response({
                "success": True,
                "data": business_state
            })
        except Exception as e:
            logger.error(f"获取业务状态失败: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)


async def main(state_manager: DeviceStateManager = None):
    """主函数 - 使用状态感知的组件"""
    # 创建状态感知的硬件管理器
    hardware_manager = StateAwareHardwareManager(state_manager)
    
    # 启动Netty服务器
    await hardware_manager.netty_server.start()
    
    # 等待通道连接
    await hardware_manager.wait_for_channels()
    
    # 启动长期通电保护机制
    await hardware_manager._start_power_protection()
    
    # 创建状态感知的REST服务器
    rest_server = StateAwareRESTServer(
        hardware_manager,
        hardware_manager.relay_service,
        hardware_manager.ptz_service
    )
    
    # 启动REST API
    runner = web.AppRunner(rest_server.app)
    await runner.setup()
    site = web.TCPSite(runner, '0.0.0.0', 7080)
    await site.start()
    
    logger.info("=" * 60)
    logger.info("状态感知硬件控制服务已启动")
    logger.info(f"REST API: http://0.0.0.0:7080")
    logger.info(f"Netty Server: tcp://0.0.0.0:{hardware_manager.netty_server.port}")
    logger.info("新增状态API端点：")
    logger.info("- GET /api/state/full - 获取完整状态")
    logger.info("- GET /api/state/io - 获取IO状态")
    logger.info("- GET /api/state/ptz - 获取PTZ状态")
    logger.info("- GET /api/state/business - 获取业务状态")
    logger.info("=" * 60)
    
    # 保持运行
    try:
        await asyncio.Event().wait()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
    finally:
        # 清理资源
        await hardware_manager.stop_power_protection()
        await runner.cleanup()


if __name__ == "__main__":
    # 可以传入外部的状态管理器
    asyncio.run(main())