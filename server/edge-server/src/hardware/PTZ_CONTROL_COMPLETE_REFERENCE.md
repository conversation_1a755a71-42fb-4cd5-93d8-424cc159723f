# PTZ云台控制完整代码参考文档

本文档包含所有PTZ云台控制相关的核心代码实现，可用于其他服务的集成参考。

## 一、核心数据结构

### 1.1 PTZ类型枚举
```python
from enum import Enum

class PTZType(Enum):
    """PTZ类型枚举 - 从Java PTZEnum提取"""
    PAN = ("4b", "51", "59")    # spinCode, sendSearchCode, reportSearchCode
    TILT = ("4d", "53", "5b")
    
    def __init__(self, spin_code, send_search_code, report_search_code):
        self.spin_code = spin_code
        self.send_search_code = send_search_code  
        self.report_search_code = report_search_code
```

### 1.2 速度表定义
```python
class PTZProtocol:
    # 水平速度表 - 112个级别
    PAN_SPEED_TABLE = [
        0.1, 0.5, 1.0, 2.0, 3.0, 7.0, 7.2, 7.4, 7.7, 8.0, 9.0, 9.5, 10.0, 10.1, 10.2, 10.3, 
        10.4, 10.5, 10.6, 10.7, 10.8, 10.9, 11.0, 11.1, 11.3, 11.5, 11.7, 11.9, 12.1, 12.3, 
        12.5, 12.7, 12.9, 13.1, 13.3, 13.5, 13.7, 13.9, 14.0, 14.2, 14.4, 14.7, 14.9, 15.2, 
        15.5, 15.7, 15.9, 16.0, 16.1, 16.3, 16.5, 16.7, 16.9, 17.0, 17.1, 17.2, 17.3, 17.4, 
        17.5, 17.6, 17.7, 17.8, 17.9, 18.0, 19.0, 20.0, 21.0, 22.0, 23.0, 24.0, 25.0, 26.0, 
        27.0, 28.0, 29.0, 30.0, 31.0, 32.0, 33.0, 34.0, 35.0, 36.0, 37.0, 38.0, 39.0, 40.0,
        42.0, 44.0, 46.0, 48.0, 50.0, 55.0, 60.0, 65.0, 70.0, 75.0, 80.0, 85.0, 90.0, 95.0,
        100.0, 110.0, 120.0, 130.0, 140.0, 150.0, 160.0, 170.0, 180.0, 190.0, 200.0, 220.0,
        240.0, 300.0, 350.0, 400.0
    ]
    
    # 垂直速度表 - 112个级别
    TILT_SPEED_TABLE = [
        0.1, 0.5, 0.8, 1.2, 1.4, 1.6, 1.8, 2.0, 2.2, 2.4, 2.6, 2.8, 3.0, 3.2, 3.4, 3.6, 
        3.8, 4.0, 4.2, 4.4, 4.6, 4.8, 5.0, 5.2, 5.4, 5.6, 5.8, 6.0, 6.2, 6.4, 6.6, 6.8, 
        6.9, 7.0, 7.1, 7.2, 7.3, 7.4, 7.5, 7.6, 7.7, 7.8, 7.9, 8.1, 8.2, 8.3, 8.4, 8.5, 
        8.6, 8.7, 8.8, 8.9, 9.0, 9.1, 9.2, 9.3, 9.4, 9.5, 9.6, 9.7, 9.8, 9.9, 10.0, 10.5,
        11.0, 11.5, 12.0, 12.5, 13.0, 13.5, 14.0, 14.5, 15.0, 16.0, 17.0, 18.0, 19.0, 20.0,
        22.0, 24.0, 26.0, 28.0, 30.0, 32.0, 34.0, 36.0, 38.0, 40.0, 42.0, 44.0, 46.0, 48.0,
        50.0, 55.0, 60.0, 65.0, 70.0, 75.0, 80.0, 85.0, 90.0, 95.0, 100.0, 110.0, 120.0,
        130.0, 140.0, 150.0, 160.0, 170.0, 180.0
    ]
```

## 二、协议实现

### 2.1 CRC校验计算
```python
@staticmethod
def calculate_checksum(data: str) -> str:
    """计算Pelco-D协议校验和"""
    checksum = 0
    for i in range(0, len(data), 2):
        byte_hex = data[i:i+2]
        checksum += int(byte_hex, 16)
    
    checksum = checksum % 256
    return f"{checksum:02x}"
```

### 2.2 角度设置命令
```python
@staticmethod
def build_spin_angle_command(ptz_addr: int, angle: float, ptz_type: PTZType) -> bytes:
    """
    构建角度设置命令
    
    命令格式：
    FF [地址] 00 [4B/4D] [角度高位] [角度低位] [校验和]
    
    参数：
    - ptz_addr: PTZ地址（通常为30）
    - angle: 目标角度
    - ptz_type: PAN或TILT
    """
    # 角度转换为协议格式（乘以100）
    if ptz_type == PTZType.PAN:
        angle_value = int(angle * 100)
    else:  # TILT
        if angle < 0:
            angle_value = int((360 + angle) * 100)
        else:
            angle_value = int(angle * 100)
    
    # 构建命令
    addr_hex = f"{ptz_addr:02x}"
    angle_hex = f"{angle_value:04x}"
    
    # 命令主体（不含起始符和校验和）
    cmd_body = f"{addr_hex}00{ptz_type.spin_code}{angle_hex}"
    
    # 计算校验和
    checksum = PTZProtocol.calculate_checksum(cmd_body)
    
    # 完整命令
    cmd_hex = f"ff{cmd_body}{checksum}"
    
    return bytes.fromhex(cmd_hex)
```

### 2.3 角度查询命令
```python
@staticmethod
def build_query_angle_command(ptz_addr: int, ptz_type: PTZType) -> bytes:
    """
    构建角度查询命令
    
    命令格式：
    FF [地址] 00 [51/53] 00 00 [校验和]
    
    参数：
    - ptz_addr: PTZ地址
    - ptz_type: PAN或TILT
    """
    addr_hex = f"{ptz_addr:02x}"
    cmd_body = f"{addr_hex}00{ptz_type.send_search_code}0000"
    checksum = PTZProtocol.calculate_checksum(cmd_body)
    cmd_hex = f"ff{cmd_body}{checksum}"
    
    return bytes.fromhex(cmd_hex)
```

### 2.4 速度设置命令
```python
@staticmethod
def build_speed_command(speed: float, ptz_addr: int, ptz_type: PTZType) -> bytes:
    """
    构建速度设置命令
    
    命令格式：
    水平速度：E0 81 06 01 0E [地址] [速度索引] [校验和]
    垂直速度：E0 81 06 01 0F [地址] [速度索引] [校验和]
    
    参数：
    - speed: 速度值（度/秒）
    - ptz_addr: PTZ地址
    - ptz_type: PAN或TILT
    """
    # 查找速度索引
    if ptz_type == PTZType.PAN:
        speed_table = PTZProtocol.PAN_SPEED_TABLE
        speed_cmd_prefix = "e08106010e"
    else:  # TILT
        speed_table = PTZProtocol.TILT_SPEED_TABLE
        speed_cmd_prefix = "e08106010f"
    
    # 找到最接近的速度索引
    speed_index = 0
    min_diff = float('inf')
    for i, table_speed in enumerate(speed_table):
        diff = abs(table_speed - speed)
        if diff < min_diff:
            min_diff = diff
            speed_index = i
            if diff < 0.01:
                break
    
    # 构建命令
    addr_hex = f"{ptz_addr:02x}"
    index_hex = f"{speed_index:02x}"
    cmd_body = f"{speed_cmd_prefix}{addr_hex}{index_hex}"
    checksum = PTZProtocol.calculate_checksum(cmd_body)
    cmd_hex = f"{cmd_body}{checksum}"
    
    return bytes.fromhex(cmd_hex)
```

### 2.5 角度响应解析
```python
@staticmethod
def parse_angle_response(response: bytes, ptz_type: PTZType) -> Optional[float]:
    """
    解析角度查询响应
    
    响应格式：
    FF [地址] 00 [59/5B] [角度高位] [角度低位] [校验和]
    
    返回：
    - 角度值（度）或None
    """
    if len(response) < 7:
        return None
    
    hex_str = response.hex()
    
    # 验证响应格式
    if not hex_str.startswith('ff'):
        return None
    
    # 检查响应代码
    if hex_str[6:8] != ptz_type.report_search_code:
        return None
    
    try:
        # 提取角度值
        angle_high = hex_str[8:10]
        angle_low = hex_str[10:12]
        angle_value = int(angle_high + angle_low, 16)
        
        # 转换为度
        angle = angle_value / 100.0
        
        # TILT角度范围调整
        if ptz_type == PTZType.TILT and angle > 300:
            angle = angle - 360
        
        return angle
    except:
        return None
```

## 三、PTZ服务实现

### 3.1 服务类定义
```python
class PTZService:
    """云台控制服务 - 基于Java协议实现"""
    
    def __init__(self, netty_server):
        self.netty_server = netty_server
        self.ptz_channel = None
        
        # 状态缓存
        self.angle_cache = {}
        self.speed_cache = {}
        self.cache_lock = threading.Lock()
```

### 3.2 角度控制实现
```python
async def set_angle(self, ptz_addr: int, angle: float, ptz_type: PTZType) -> Tuple[bool, str]:
    """设置角度（通用方法）"""
    # 构建命令
    cmd = PTZProtocol.build_spin_angle_command(ptz_addr, angle, ptz_type)
    hex_cmd = binascii.hexlify(cmd).decode()
    
    logger.info(f"设置{ptz_type.name}角度: 地址={ptz_addr}, 角度={angle}°, 命令={hex_cmd}")
    
    success = await self.netty_server.send_command(self.ptz_channel, hex_cmd)
    
    if success:
        # 更新缓存
        with self.cache_lock:
            if ptz_addr not in self.angle_cache:
                self.angle_cache[ptz_addr] = {}
            self.angle_cache[ptz_addr][ptz_type.name.lower()] = angle
            
        # 延迟以确保命令执行
        await asyncio.sleep(0.1)
        return True, f"{ptz_type.name}角度设置为{angle}°"
    
    return False, "设置失败"

async def query_angle(self, ptz_addr: int, ptz_type: PTZType) -> Tuple[bool, float]:
    """查询角度（通用方法）"""
    cmd = PTZProtocol.build_query_angle_command(ptz_addr, ptz_type)
    hex_cmd = binascii.hexlify(cmd).decode()
    
    logger.info(f"查询{ptz_type.name}角度: 地址={ptz_addr}, 命令={hex_cmd}")
    
    # 发送命令并等待响应
    response = await self.netty_server.send_command_with_response(
        self.ptz_channel, hex_cmd, timeout=2.0
    )
    
    if response:
        logger.info(f"收到响应: {binascii.hexlify(response).decode()}")
        
        angle = PTZProtocol.parse_angle_response(response, ptz_type)
        if angle is not None:
            # 更新缓存
            with self.cache_lock:
                if ptz_addr not in self.angle_cache:
                    self.angle_cache[ptz_addr] = {}
                self.angle_cache[ptz_addr][ptz_type.name.lower()] = angle
            return True, angle
    
    # 返回缓存值
    with self.cache_lock:
        if ptz_addr in self.angle_cache and ptz_type.name.lower() in self.angle_cache[ptz_addr]:
            return False, self.angle_cache[ptz_addr][ptz_type.name.lower()]
    
    return False, 0.0
```

### 3.3 速度控制实现
```python
async def set_speed(self, ptz_addr: int, speed: float, ptz_type: PTZType) -> Tuple[bool, str]:
    """设置速度（通用方法）"""
    # 构建命令
    cmd = PTZProtocol.build_speed_command(speed, ptz_addr, ptz_type)
    hex_cmd = binascii.hexlify(cmd).decode()
    
    logger.info(f"设置{ptz_type.name}速度: 地址={ptz_addr}, 速度={speed}°/s, 命令={hex_cmd}")
    
    success = await self.netty_server.send_command(self.ptz_channel, hex_cmd)
    
    if success:
        # 更新缓存
        with self.cache_lock:
            if ptz_addr not in self.speed_cache:
                self.speed_cache[ptz_addr] = {}
            self.speed_cache[ptz_addr][ptz_type.name.lower()] = speed
        
        return True, f"{ptz_type.name}速度设置成功"
    
    return False, "设置失败"

def query_speed(self, ptz_addr: int, ptz_type: PTZType) -> Tuple[bool, Dict]:
    """
    查询速度（从缓存）
    
    注意：Pelco-D协议不支持速度查询命令，因此使用缓存机制
    """
    with self.cache_lock:
        if ptz_addr in self.speed_cache and ptz_type.name.lower() in self.speed_cache[ptz_addr]:
            speed = self.speed_cache[ptz_addr][ptz_type.name.lower()]
            
            # 查找对应的速度表索引
            speed_table = PTZProtocol.PAN_SPEED_TABLE if ptz_type == PTZType.PAN else PTZProtocol.TILT_SPEED_TABLE
            speed_index = 0
            for i, table_speed in enumerate(speed_table):
                if abs(table_speed - speed) < 0.01:
                    speed_index = i
                    break
            
            return True, {"speed": speed, "index": speed_index}
    
    # 返回默认值
    default_speed = 10.0 if ptz_type == PTZType.PAN else 5.0
    return False, {"speed": default_speed, "index": 0}
```

### 3.4 停止命令
```python
async def stop(self, ptz_addr: int, stop_type: str = "all") -> Tuple[bool, str]:
    """停止云台运动"""
    cmd = PTZProtocol.build_stop_command(ptz_addr, stop_type)
    hex_cmd = binascii.hexlify(cmd).decode()
    
    logger.info(f"停止云台: 类型={stop_type}, 命令={hex_cmd}")
    
    success = await self.netty_server.send_command(self.ptz_channel, hex_cmd)
    return success, f"停止命令已发送" if success else "停止命令发送失败"

@staticmethod
def build_stop_command(ptz_addr: int, stop_type: str = "all") -> bytes:
    """构建停止命令"""
    addr_hex = f"{ptz_addr:02x}"
    cmd_body = f"{addr_hex}00000000"
    checksum = PTZProtocol.calculate_checksum(cmd_body)
    cmd_hex = f"ff{cmd_body}{checksum}"
    
    return bytes.fromhex(cmd_hex)
```

## 四、REST API接口

### 4.1 角度控制API
```python
# 设置PTZ角度
async def ptz_set_angle(self, request):
    """
    设置PTZ角度
    POST /api/ptz/angle
    {
        "axis": "pan" | "tilt",
        "angle": 90.0,
        "ptz_addr": 30  # 可选，默认30
    }
    """
    try:
        data = await request.json()
        ptz_addr = data.get("ptz_addr", 30)
        axis = data.get("axis")  # "pan" or "tilt"
        angle = data.get("angle")
        
        if axis not in ["pan", "tilt"] or angle is None:
            return web.json_response({"error": "参数错误"}, status=400)
        
        ptz_type = PTZType.PAN if axis == "pan" else PTZType.TILT
        success, msg = await self.ptz_service.set_angle(ptz_addr, angle, ptz_type)
        
        return web.json_response({
            "success": success,
            "message": msg,
            "axis": axis,
            "angle": angle
        })
        
    except Exception as e:
        logger.error(f"设置PTZ角度错误: {e}")
        return web.json_response({"error": str(e)}, status=500)

# 查询PTZ角度
async def ptz_get_angle(self, request):
    """
    查询PTZ角度
    GET /api/ptz/angle?axis=pan|tilt|all&ptz_addr=30
    """
    try:
        ptz_addr = int(request.query.get("ptz_addr", 30))
        axis = request.query.get("axis", "all")
        
        result = {}
        
        if axis in ["pan", "all"]:
            success, angle = await self.ptz_service.query_angle(ptz_addr, PTZType.PAN)
            result["pan"] = {"success": success, "angle": angle}
        
        if axis in ["tilt", "all"]:
            success, angle = await self.ptz_service.query_angle(ptz_addr, PTZType.TILT)
            result["tilt"] = {"success": success, "angle": angle}
        
        return web.json_response({
            "success": True,
            "data": result
        })
        
    except Exception as e:
        logger.error(f"查询PTZ角度错误: {e}")
        return web.json_response({"error": str(e)}, status=500)
```

### 4.2 速度控制API
```python
# 设置PTZ速度
async def ptz_set_speed(self, request):
    """
    设置PTZ速度
    POST /api/ptz/speed
    {
        "axis": "pan" | "tilt",
        "speed": 50.0,  # 度/秒
        "ptz_addr": 30  # 可选
    }
    """
    try:
        data = await request.json()
        ptz_addr = data.get("ptz_addr", 30)
        axis = data.get("axis")
        speed = data.get("speed")
        
        if axis not in ["pan", "tilt"] or speed is None:
            return web.json_response({"error": "参数错误"}, status=400)
        
        ptz_type = PTZType.PAN if axis == "pan" else PTZType.TILT
        success, msg = await self.ptz_service.set_speed(ptz_addr, speed, ptz_type)
        
        return web.json_response({
            "success": success,
            "message": msg,
            "axis": axis,
            "speed": speed
        })
        
    except Exception as e:
        logger.error(f"设置PTZ速度错误: {e}")
        return web.json_response({"error": str(e)}, status=500)

# 查询PTZ速度
async def ptz_get_speed(self, request):
    """
    查询PTZ速度（基于缓存）
    GET /api/ptz/speed?axis=pan|tilt|all&ptz_addr=30
    """
    try:
        ptz_addr = int(request.query.get("ptz_addr", 30))
        axis = request.query.get("axis", "all")
        
        result = {}
        
        if axis in ["pan", "all"]:
            success, speed_data = self.ptz_service.query_speed(ptz_addr, PTZType.PAN)
            result["pan"] = {"success": success, **speed_data}
        
        if axis in ["tilt", "all"]:
            success, speed_data = self.ptz_service.query_speed(ptz_addr, PTZType.TILT)
            result["tilt"] = {"success": success, **speed_data}
        
        return web.json_response({
            "success": True,
            "data": result
        })
        
    except Exception as e:
        logger.error(f"查询PTZ速度错误: {e}")
        return web.json_response({"error": str(e)}, status=500)
```

## 五、使用示例

### 5.1 Python调用示例
```python
import asyncio
import aiohttp

async def test_ptz():
    async with aiohttp.ClientSession() as session:
        # 设置水平角度
        async with session.post('http://localhost:8080/api/ptz/angle',
                              json={'axis': 'pan', 'angle': 90}) as resp:
            result = await resp.json()
            print(f"设置水平角度: {result}")
        
        # 查询所有角度
        async with session.get('http://localhost:8080/api/ptz/angle?axis=all') as resp:
            result = await resp.json()
            print(f"当前角度: {result}")
        
        # 设置垂直速度
        async with session.post('http://localhost:8080/api/ptz/speed',
                              json={'axis': 'tilt', 'speed': 15.5}) as resp:
            result = await resp.json()
            print(f"设置垂直速度: {result}")

asyncio.run(test_ptz())
```

### 5.2 cURL调用示例
```bash
# 设置水平角度到180度
curl -X POST http://localhost:8080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"axis": "pan", "angle": 180}'

# 查询所有角度
curl http://localhost:8080/api/ptz/angle?axis=all

# 设置垂直速度为30度/秒
curl -X POST http://localhost:8080/api/ptz/speed \
  -H "Content-Type: application/json" \
  -d '{"axis": "tilt", "speed": 30}'

# 查询所有速度
curl http://localhost:8080/api/ptz/speed?axis=all
```

## 六、重要说明

### 6.1 速度查询机制
- **协议限制**：Pelco-D协议不支持速度查询命令
- **实现方案**：使用应用层缓存记录最后设置的速度值
- **准确性**：缓存值准确反映最后的速度设置

### 6.2 角度查询机制
- **实时查询**：支持发送查询命令获取云台实际角度
- **响应时间**：典型响应时间 < 100ms
- **精度**：0.1度

### 6.3 默认参数
- **PTZ地址**：30（可通过参数修改）
- **默认水平速度**：10.0度/秒
- **默认垂直速度**：5.0度/秒
- **角度范围**：水平0-360度，垂直-90到+90度

### 6.4 线程安全
- 所有缓存操作都使用锁保护
- 支持并发请求
- 命令队列化执行

## 七、文件位置

所有相关代码位于：`/home/<USER>/edge-server/modules/hardware/final/`

- `hardware_control_service.py` - 完整的硬件控制服务实现
- `test_ptz_complete.py` - PTZ功能测试脚本
- `PTZ_SPEED_QUERY_EXPLANATION.md` - 速度查询机制详细说明

---

此文档包含了PTZ云台控制的所有核心代码实现，可直接用于其他服务的集成参考。