#!/usr/bin/env python3
"""
相机控制服务 - 修复版本
基于RECORDING_SOLUTIONS.md的建议，实现可靠的录像功能
"""

import os
import asyncio
import aiohttp
from aiohttp import web
import logging
import threading
import ctypes
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path
import json
import base64
import subprocess

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class HikvisionSDK:
    """海康威视SDK封装"""
    
    def __init__(self):
        self.sdk = None
        self.user_id = -1
        self._lock = threading.Lock()
        self.recording_handles = {}  # 存储录像句柄
        self.recording_files = {}    # 存储录像文件句柄
        self.callbacks = {}          # 存储回调函数引用
        
        # NVR配置
        self.nvr_config = {
            'ip': os.environ.get('NVR_IP', '*************'),
            'port': int(os.environ.get('NVR_PORT', '8000')),
            'username': os.environ.get('NVR_USERNAME', 'admin'),
            'password': os.environ.get('NVR_PASSWORD', 'Dhdjktsz')
        }
        
        # 数据目录
        self.data_dir = Path('/data/camera')
        self.data_dir.mkdir(exist_ok=True)
        
    def initialize(self) -> bool:
        """初始化SDK"""
        try:
            # 加载SDK
            sdk_paths = [
                '/home/<USER>/server/edge-server/camera-service/camera/hikvision/库文件/libhcnetsdk.so',
                './camera/hikvision/库文件/libhcnetsdk.so',
                '/home/<USER>/server/edge-server/src/modules/camera/hikvision/库文件/libhcnetsdk.so',
                './libhcnetsdk.so',
                '/usr/lib/libhcnetsdk.so'
            ]
            
            for path in sdk_paths:
                if os.path.exists(path):
                    self.sdk = ctypes.CDLL(path)
                    logger.info(f"加载SDK成功: {path}")
                    break
            else:
                logger.error("未找到SDK库文件")
                return False
            
            # 初始化SDK
            self.sdk.NET_DVR_Init.restype = ctypes.c_bool
            if not self.sdk.NET_DVR_Init():
                logger.error("SDK初始化失败")
                return False
            
            # 设置超时和重连
            self.sdk.NET_DVR_SetConnectTime(5000, 3)
            self.sdk.NET_DVR_SetReconnect(10000, True)
            
            # 登录NVR
            if not self._login():
                return False
                
            logger.info("海康威视SDK初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"SDK初始化异常: {e}")
            return False
    
    def _login(self) -> bool:
        """登录NVR"""
        try:
            device_info = ctypes.c_byte * 40
            device_info_ref = device_info()
            
            self.sdk.NET_DVR_Login_V30.restype = ctypes.c_long
            self.user_id = self.sdk.NET_DVR_Login_V30(
                self.nvr_config['ip'].encode('utf-8'),
                self.nvr_config['port'],
                self.nvr_config['username'].encode('utf-8'),
                self.nvr_config['password'].encode('utf-8'),
                ctypes.byref(device_info_ref)
            )
            
            if self.user_id < 0:
                error_code = self.sdk.NET_DVR_GetLastError()
                logger.error(f"登录NVR失败，错误码: {error_code}")
                return False
                
            logger.info(f"登录NVR成功，用户ID: {self.user_id}")
            return True
            
        except Exception as e:
            logger.error(f"登录NVR异常: {e}")
            return False
    
    def cleanup(self):
        """清理SDK资源"""
        try:
            # 停止所有录像
            for channel in list(self.recording_handles.keys()):
                self._stop_manual_recording(channel)
            
            # 登出
            if self.user_id >= 0:
                self.sdk.NET_DVR_Logout(self.user_id)
                
            # 清理SDK
            if self.sdk:
                self.sdk.NET_DVR_Cleanup()
                
            logger.info("SDK资源清理完成")
            
        except Exception as e:
            logger.error(f"清理SDK资源异常: {e}")
    
    def _stop_manual_recording(self, channel: int):
        """停止手动录像"""
        try:
            if channel in self.recording_handles:
                play_handle = self.recording_handles[channel]
                
                # 关闭文件
                if channel in self.recording_files:
                    self.recording_files[channel].close()
                    del self.recording_files[channel]
                
                # 停止预览
                self.sdk.NET_DVR_StopRealPlay(play_handle)
                
                # 清理句柄和回调
                del self.recording_handles[channel]
                if channel in self.callbacks:
                    del self.callbacks[channel]
                    
        except Exception as e:
            logger.error(f"停止录像异常: {e}")


class CameraService:
    """相机服务 - 使用可靠的录像方法"""
    
    def __init__(self, sdk: HikvisionSDK):
        self.sdk = sdk
        self.stream_handles = {}  # 存储实时流句柄
        
    async def take_snapshot(self, channel: int = 1) -> Tuple[bool, Dict[str, Any]]:
        """抓拍照片"""
        try:
            with self.sdk._lock:
                if self.sdk.user_id < 0:
                    return False, {"error": "未登录NVR"}
                
                # 构建文件路径
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"snapshot_{channel}_{timestamp}.jpg"
                filepath = self.sdk.data_dir / filename
                
                # 设置JPEG图片质量
                jpeg_param = ctypes.c_uint32 * 3
                jpeg_param_ref = jpeg_param()
                jpeg_param_ref[0] = channel  # 通道号
                jpeg_param_ref[1] = 2        # 图片质量 0-最好 1-较好 2-一般
                jpeg_param_ref[2] = 0        # 图片大小 0-CIF 1-QCIF 2-D1
                
                # 抓拍
                self.sdk.sdk.NET_DVR_CaptureJPEGPicture.restype = ctypes.c_bool
                success = self.sdk.sdk.NET_DVR_CaptureJPEGPicture(
                    self.sdk.user_id,
                    channel + 32,  # NVR数字通道号 = 通道号 + 32
                    ctypes.byref(jpeg_param_ref),
                    str(filepath).encode('utf-8')
                )
                
                if success and filepath.exists():
                    # 读取文件并转为base64
                    with open(filepath, 'rb') as f:
                        image_data = f.read()
                        image_base64 = base64.b64encode(image_data).decode('utf-8')
                    
                    return True, {
                        "channel": channel,
                        "filename": filename,
                        "filepath": str(filepath),
                        "size": len(image_data),
                        "timestamp": timestamp,
                        "image_base64": image_base64
                    }
                else:
                    error_code = self.sdk.sdk.NET_DVR_GetLastError()
                    return False, {"error": f"抓拍失败，错误码: {error_code}"}
                    
        except Exception as e:
            logger.error(f"抓拍异常: {e}")
            return False, {"error": str(e)}
    
    async def start_recording(self, channel: int = 1, duration: int = 60, method: str = "auto") -> Tuple[bool, Dict[str, Any]]:
        """
        开始录像 - 智能选择最佳方法
        method: "auto" | "callback" | "playback" | "rtsp"
        """
        if method == "auto":
            # 自动选择：优先使用回调方法
            method = "callback"
        
        if method == "callback":
            return await self._start_callback_recording(channel, duration)
        elif method == "playback":
            # 使用历史回放方式（延迟1分钟）
            return await self._start_playback_recording(channel, duration)
        elif method == "rtsp":
            # 使用RTSP流录制
            return await self._start_rtsp_recording(channel, duration)
        else:
            return False, {"error": f"不支持的录像方法: {method}"}
    
    async def _start_callback_recording(self, channel: int, duration: int) -> Tuple[bool, Dict[str, Any]]:
        """使用回调函数手动保存流数据"""
        try:
            with self.sdk._lock:
                if self.sdk.user_id < 0:
                    return False, {"error": "未登录NVR"}
                
                # 检查是否已在录像
                if channel in self.sdk.recording_handles:
                    return False, {"error": f"通道{channel}已在录像中"}
                
                # 构建文件路径
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"record_{channel}_{timestamp}.h264"
                filepath = self.sdk.data_dir / filename
                
                # 打开文件用于写入
                try:
                    file_handle = open(filepath, 'wb')
                    self.sdk.recording_files[channel] = file_handle
                except Exception as e:
                    logger.error(f"创建录像文件失败: {e}")
                    return False, {"error": f"创建录像文件失败: {e}"}
                
                # 定义回调函数类型
                REALDATACALLBACK = ctypes.CFUNCTYPE(
                    None,
                    ctypes.c_long,
                    ctypes.c_uint32,
                    ctypes.POINTER(ctypes.c_ubyte),
                    ctypes.c_uint32,
                    ctypes.c_void_p
                )
                
                # 创建回调函数，手动保存数据
                def real_data_callback(lRealHandle, dwDataType, pBuffer, dwBufSize, pUser):
                    try:
                        # dwDataType: 0-系统头，1-流数据，2-音频数据，3-私有数据
                        if dwDataType in [0, 1] and dwBufSize > 0:
                            # 将数据写入文件
                            if channel in self.sdk.recording_files:
                                data = ctypes.string_at(pBuffer, dwBufSize)
                                self.sdk.recording_files[channel].write(data)
                                
                                if not hasattr(real_data_callback, 'logged'):
                                    logger.info(f"通道{channel}: 开始保存视频数据")
                                    real_data_callback.logged = True
                    except Exception as e:
                        logger.error(f"回调函数错误: {e}")
                    return
                
                # 转换为C函数
                c_callback = REALDATACALLBACK(real_data_callback)
                
                # 定义NET_DVR_PREVIEWINFO结构体
                class NET_DVR_PREVIEWINFO(ctypes.Structure):
                    _fields_ = [
                        ("lChannel", ctypes.c_long),
                        ("dwStreamType", ctypes.c_uint32),
                        ("dwLinkMode", ctypes.c_uint32),
                        ("hPlayWnd", ctypes.c_void_p),
                        ("bBlocked", ctypes.c_bool),
                        ("bPassbackRecord", ctypes.c_bool),
                        ("byPreviewMode", ctypes.c_ubyte),
                        ("byStreamID", ctypes.c_ubyte * 32),
                        ("byProtoType", ctypes.c_ubyte),
                        ("byRes1", ctypes.c_ubyte * 2),
                        ("dwDisplayBufNum", ctypes.c_uint32),
                        ("byNPQMode", ctypes.c_ubyte),
                        ("byRes", ctypes.c_ubyte * 215)
                    ]
                
                preview_info = NET_DVR_PREVIEWINFO()
                preview_info.lChannel = channel + 32
                preview_info.dwStreamType = 0
                preview_info.dwLinkMode = 0
                preview_info.hPlayWnd = None
                preview_info.bBlocked = 1
                
                # 获取实时流句柄
                self.sdk.sdk.NET_DVR_RealPlay_V40.restype = ctypes.c_long
                play_handle = self.sdk.sdk.NET_DVR_RealPlay_V40(
                    self.sdk.user_id,
                    ctypes.byref(preview_info),
                    c_callback,
                    None
                )
                
                if play_handle < 0:
                    error_code = self.sdk.sdk.NET_DVR_GetLastError()
                    file_handle.close()
                    del self.sdk.recording_files[channel]
                    return False, {"error": f"获取实时流失败，错误码: {error_code}"}
                
                # 保存句柄和回调
                self.sdk.recording_handles[channel] = play_handle
                self.sdk.callbacks[channel] = c_callback
                
                # 设置自动停止
                if duration > 0:
                    asyncio.create_task(self._auto_stop_recording(channel, duration))
                
                return True, {
                    "channel": channel,
                    "method": "callback",
                    "filename": filename,
                    "filepath": str(filepath),
                    "start_time": timestamp,
                    "duration": duration,
                    "status": "recording"
                }
                
        except Exception as e:
            logger.error(f"开始录像异常: {e}")
            # 清理资源
            if channel in self.sdk.recording_files:
                self.sdk.recording_files[channel].close()
                del self.sdk.recording_files[channel]
            return False, {"error": str(e)}
    
    async def _start_rtsp_recording(self, channel: int, duration: int) -> Tuple[bool, Dict[str, Any]]:
        """使用RTSP流+FFmpeg录制"""
        try:
            # 构建RTSP URL
            username = self.sdk.nvr_config['username']
            password = self.sdk.nvr_config['password']
            ip = self.sdk.nvr_config['ip']
            rtsp_url = f"rtsp://{username}:{password}@{ip}:554/Streaming/Channels/{channel + 32}01"
            
            # 构建文件路径
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"record_rtsp_{channel}_{timestamp}.mp4"
            filepath = self.sdk.data_dir / filename
            
            # 构建FFmpeg命令
            cmd = [
                'ffmpeg',
                '-rtsp_transport', 'tcp',
                '-i', rtsp_url,
                '-c', 'copy',
                '-t', str(duration),
                str(filepath)
            ]
            
            # 启动FFmpeg进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 保存进程引用
            if not hasattr(self, 'ffmpeg_processes'):
                self.ffmpeg_processes = {}
            self.ffmpeg_processes[channel] = process
            
            return True, {
                "channel": channel,
                "method": "rtsp",
                "filename": filename,
                "filepath": str(filepath),
                "rtsp_url": rtsp_url,
                "duration": duration,
                "status": "recording"
            }
            
        except Exception as e:
            logger.error(f"RTSP录像异常: {e}")
            return False, {"error": str(e)}
    
    async def _start_playback_recording(self, channel: int, duration: int) -> Tuple[bool, Dict[str, Any]]:
        """使用延迟回放方式录像（1分钟延迟）"""
        # 获取1分钟前的时间
        end_time = datetime.now() - timedelta(minutes=1)
        start_time = end_time - timedelta(seconds=duration)
        
        return await self.playback_recording(channel, start_time, end_time)
    
    async def _auto_stop_recording(self, channel: int, duration: int):
        """自动停止录像"""
        await asyncio.sleep(duration)
        await self.stop_recording(channel)
    
    async def stop_recording(self, channel: int = 1) -> Tuple[bool, Dict[str, Any]]:
        """停止录像"""
        try:
            with self.sdk._lock:
                # 停止回调录像
                if channel in self.sdk.recording_handles:
                    self.sdk._stop_manual_recording(channel)
                    return True, {
                        "channel": channel,
                        "status": "stopped",
                        "stop_time": datetime.now().strftime('%Y%m%d_%H%M%S')
                    }
                
                # 停止FFmpeg录像
                if hasattr(self, 'ffmpeg_processes') and channel in self.ffmpeg_processes:
                    process = self.ffmpeg_processes[channel]
                    process.terminate()
                    process.wait(timeout=5)
                    del self.ffmpeg_processes[channel]
                    return True, {
                        "channel": channel,
                        "status": "stopped",
                        "stop_time": datetime.now().strftime('%Y%m%d_%H%M%S')
                    }
                
                return False, {"error": f"通道{channel}未在录像中"}
                
        except Exception as e:
            logger.error(f"停止录像异常: {e}")
            return False, {"error": str(e)}
    
    async def get_status(self) -> Dict[str, Any]:
        """获取相机状态"""
        try:
            recording_channels = list(self.sdk.recording_handles.keys())
            if hasattr(self, 'ffmpeg_processes'):
                recording_channels.extend(list(self.ffmpeg_processes.keys()))
            
            status = {
                "sdk_initialized": self.sdk.sdk is not None,
                "nvr_connected": self.sdk.user_id >= 0,
                "nvr_config": {
                    "ip": self.sdk.nvr_config['ip'],
                    "port": self.sdk.nvr_config['port']
                },
                "recording_channels": recording_channels,
                "data_directory": str(self.sdk.data_dir),
                "timestamp": datetime.now().isoformat()
            }
            
            return status
            
        except Exception as e:
            logger.error(f"获取状态异常: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def get_stream_url(self, channel: int = 1, stream_type: str = "main") -> Tuple[bool, Dict[str, Any]]:
        """获取实时流地址"""
        try:
            # 构建RTSP URL
            username = self.sdk.nvr_config['username']
            password = self.sdk.nvr_config['password']
            ip = self.sdk.nvr_config['ip']
            
            # 主码流或子码流
            stream_id = 1 if stream_type == "main" else 2
            
            rtsp_url = f"rtsp://{username}:{password}@{ip}:554/Streaming/Channels/{channel + 32}0{stream_id}"
            
            return True, {
                "channel": channel,
                "stream_type": stream_type,
                "rtsp_url": rtsp_url,
                "protocol": "rtsp"
            }
            
        except Exception as e:
            logger.error(f"获取流地址异常: {e}")
            return False, {"error": str(e)}
    
    async def playback_recording(self, channel: int, start_time: datetime, end_time: datetime) -> Tuple[bool, Dict[str, Any]]:
        """历史回放录制（从NVR获取已存储的录像）"""
        try:
            with self.sdk._lock:
                if self.sdk.user_id < 0:
                    return False, {"error": "未登录NVR"}
                
                # 构建文件路径
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"playback_{channel}_{timestamp}.mp4"
                filepath = self.sdk.data_dir / filename
                
                # 定义NET_DVR_TIME结构体
                class NET_DVR_TIME(ctypes.Structure):
                    _fields_ = [
                        ("dwYear", ctypes.c_uint32),
                        ("dwMonth", ctypes.c_uint32),
                        ("dwDay", ctypes.c_uint32),
                        ("dwHour", ctypes.c_uint32),
                        ("dwMinute", ctypes.c_uint32),
                        ("dwSecond", ctypes.c_uint32)
                    ]
                
                # 转换时间
                start_struct = NET_DVR_TIME()
                start_struct.dwYear = start_time.year
                start_struct.dwMonth = start_time.month
                start_struct.dwDay = start_time.day
                start_struct.dwHour = start_time.hour
                start_struct.dwMinute = start_time.minute
                start_struct.dwSecond = start_time.second
                
                end_struct = NET_DVR_TIME()
                end_struct.dwYear = end_time.year
                end_struct.dwMonth = end_time.month
                end_struct.dwDay = end_time.day
                end_struct.dwHour = end_time.hour
                end_struct.dwMinute = end_time.minute
                end_struct.dwSecond = end_time.second
                
                # 获取回放句柄
                self.sdk.sdk.NET_DVR_PlayBackByTime.restype = ctypes.c_long
                play_handle = self.sdk.sdk.NET_DVR_PlayBackByTime(
                    self.sdk.user_id,
                    channel + 32,  # NVR数字通道
                    ctypes.byref(start_struct),
                    ctypes.byref(end_struct),
                    0  # 窗口句柄，0表示不显示
                )
                
                if play_handle < 0:
                    error_code = self.sdk.sdk.NET_DVR_GetLastError()
                    logger.error(f"创建回放句柄失败，错误码: {error_code}")
                    return False, {"error": f"创建回放句柄失败，错误码: {error_code}"}
                
                logger.info(f"创建回放句柄成功: {play_handle}")
                
                # 设置录像保存路径
                self.sdk.sdk.NET_DVR_PlayBackSaveData.restype = ctypes.c_bool
                if not self.sdk.sdk.NET_DVR_PlayBackSaveData(
                    play_handle,
                    str(filepath).encode('utf-8')
                ):
                    error_code = self.sdk.sdk.NET_DVR_GetLastError()
                    self.sdk.sdk.NET_DVR_StopPlayBack(play_handle)
                    logger.error(f"设置保存路径失败，错误码: {error_code}")
                    return False, {"error": f"设置保存路径失败，错误码: {error_code}"}
                
                # 开始回放下载
                self.sdk.sdk.NET_DVR_PlayBackControl.restype = ctypes.c_bool
                if not self.sdk.sdk.NET_DVR_PlayBackControl(
                    play_handle,
                    1,  # NET_DVR_PLAYSTART
                    0,
                    None
                ):
                    error_code = self.sdk.sdk.NET_DVR_GetLastError()
                    self.sdk.sdk.NET_DVR_StopPlayBack(play_handle)
                    logger.error(f"开始回放失败，错误码: {error_code}")
                    return False, {"error": f"开始回放失败，错误码: {error_code}"}
                
                # 保存句柄（用于查询进度）
                if not hasattr(self, 'playback_handles'):
                    self.playback_handles = {}
                self.playback_handles[channel] = play_handle
                
                # 计算视频时长
                duration = (end_time - start_time).total_seconds()
                
                return True, {
                    "channel": channel,
                    "method": "playback",
                    "filename": filename,
                    "filepath": str(filepath),
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "duration_seconds": duration,
                    "play_handle": play_handle,
                    "status": "downloading"
                }
                
        except Exception as e:
            logger.error(f"回放录制异常: {e}")
            return False, {"error": str(e)}


class RESTServer:
    """REST API服务器"""
    
    def __init__(self, camera_service: CameraService):
        self.camera_service = camera_service
        self.app = web.Application()
        self.setup_routes()
        
    def setup_routes(self):
        """设置路由"""
        # 基础路由
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/', self.index)
        
        # 相机控制API
        self.app.router.add_post('/api/camera/snapshot', self.snapshot)
        self.app.router.add_post('/api/camera/record/start', self.start_recording)
        self.app.router.add_post('/api/camera/record/stop', self.stop_recording)
        self.app.router.add_get('/api/camera/status', self.get_status)
        self.app.router.add_post('/api/camera/stream', self.get_stream)
        
        # 回放录制API
        self.app.router.add_post('/api/camera/playback/start', self.start_playback_recording)
        
    async def health_check(self, request):
        """健康检查"""
        status = await self.camera_service.get_status()
        
        return web.json_response({
            "status": "healthy" if status.get("nvr_connected") else "unhealthy",
            "service": "Camera Control Service",
            "version": "2.0",
            "timestamp": datetime.now().isoformat(),
            "nvr_connected": status.get("nvr_connected", False)
        })
    
    async def index(self, request):
        """首页"""
        return web.json_response({
            "service": "相机控制服务API",
            "description": "海康威视相机控制服务（修复版）",
            "version": "2.0",
            "endpoints": {
                "health": "GET /health - 健康检查",
                "camera": {
                    "snapshot": "POST /api/camera/snapshot - 抓拍照片",
                    "record_start": "POST /api/camera/record/start - 开始录像",
                    "record_stop": "POST /api/camera/record/stop - 停止录像",
                    "status": "GET /api/camera/status - 查询状态",
                    "stream": "POST /api/camera/stream - 获取实时流",
                    "playback": "POST /api/camera/playback/start - 历史回放录制"
                }
            },
            "recording_methods": {
                "callback": "使用回调函数手动保存（默认）",
                "rtsp": "使用RTSP流+FFmpeg录制",
                "playback": "历史回放录制（1分钟延迟）"
            }
        })
    
    async def snapshot(self, request):
        """抓拍接口"""
        try:
            data = await request.json()
            channel = data.get("channel", 1)
            
            success, result = await self.camera_service.take_snapshot(channel)
            
            if success:
                return web.json_response({
                    "success": True,
                    "data": result
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": result.get("error", "抓拍失败")
                }, status=400)
                
        except Exception as e:
            logger.error(f"抓拍接口错误: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def start_recording(self, request):
        """开始录像接口"""
        try:
            data = await request.json()
            channel = data.get("channel", 1)
            duration = data.get("duration", 60)
            method = data.get("method", "auto")  # 可选：auto, callback, rtsp, playback
            
            success, result = await self.camera_service.start_recording(channel, duration, method)
            
            if success:
                return web.json_response({
                    "success": True,
                    "data": result
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": result.get("error", "开始录像失败")
                }, status=400)
                
        except Exception as e:
            logger.error(f"开始录像接口错误: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def stop_recording(self, request):
        """停止录像接口"""
        try:
            data = await request.json()
            channel = data.get("channel", 1)
            
            success, result = await self.camera_service.stop_recording(channel)
            
            if success:
                return web.json_response({
                    "success": True,
                    "data": result
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": result.get("error", "停止录像失败")
                }, status=400)
                
        except Exception as e:
            logger.error(f"停止录像接口错误: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_status(self, request):
        """查询状态接口"""
        try:
            status = await self.camera_service.get_status()
            
            return web.json_response({
                "success": True,
                "data": status
            })
            
        except Exception as e:
            logger.error(f"查询状态接口错误: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_stream(self, request):
        """获取实时流接口"""
        try:
            data = await request.json()
            channel = data.get("channel", 1)
            stream_type = data.get("stream_type", "main")
            
            success, result = await self.camera_service.get_stream_url(channel, stream_type)
            
            if success:
                return web.json_response({
                    "success": True,
                    "data": result
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": result.get("error", "获取流地址失败")
                }, status=400)
                
        except Exception as e:
            logger.error(f"获取流接口错误: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def start_playback_recording(self, request):
        """开始回放录制接口"""
        try:
            data = await request.json()
            channel = data.get("channel", 1)
            
            # 获取时间参数
            start_time_str = data.get("start_time")
            end_time_str = data.get("end_time")
            
            if not start_time_str or not end_time_str:
                # 如果没有指定时间，默认获取最近10分钟的录像
                end_time = datetime.now()
                start_time = end_time - timedelta(minutes=10)
            else:
                # 解析时间字符串
                start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
            
            success, result = await self.camera_service.playback_recording(channel, start_time, end_time)
            
            if success:
                return web.json_response({
                    "success": True,
                    "data": result
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": result.get("error", "回放录制失败")
                }, status=400)
                
        except Exception as e:
            logger.error(f"回放录制接口错误: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)


async def main():
    """主函数"""
    # 创建SDK实例
    sdk = HikvisionSDK()
    
    # 初始化SDK
    if not sdk.initialize():
        logger.error("SDK初始化失败，服务退出")
        return
    
    # 创建相机服务
    camera_service = CameraService(sdk)
    
    # 创建REST服务器
    rest_server = RESTServer(camera_service)
    
    # 启动REST服务器
    runner = web.AppRunner(rest_server.app)
    await runner.setup()
    site = web.TCPSite(runner, '0.0.0.0', 7090)
    await site.start()
    
    logger.info("=" * 60)
    logger.info("相机控制服务已启动 (v2.0 - 修复版)")
    logger.info("REST API: http://0.0.0.0:7090")
    logger.info("海康威视SDK已初始化")
    logger.info("支持的录像方法: callback, rtsp, playback")
    logger.info("=" * 60)
    
    try:
        # 保持运行
        await asyncio.Future()
    except KeyboardInterrupt:
        logger.info("收到停止信号")
    finally:
        # 清理资源
        sdk.cleanup()
        await runner.cleanup()
        logger.info("服务已停止")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序已停止")