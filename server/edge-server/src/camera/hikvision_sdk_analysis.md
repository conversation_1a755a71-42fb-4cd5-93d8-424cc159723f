# Hikvision SDK Analysis Report

## SDK Directory Structure

```
/home/<USER>/server/third-party/hikvision-sdk/
├── CH-HCNetSDKV6.1.9.48_build20230410_linux64_20250312150736 (1)/
│   └── linux/
│       ├── Demo示例/
│       │   ├── 1-C++开发示例/
│       │   │   ├── QtDemo/           # Qt GUI demo with recording button
│       │   │   ├── consoleDemo/      # Console demo with streaming examples
│       │   │   └── psdatacall_demo/  # PS data callback demo with file writing
│       │   ├── 2-Java开发示例/
│       │   │   └── 1-预览回放下载/    # Java preview/playback/download demo
│       │   └── 3-Python开发示例/
│       │       └── 1-预览取流解码Demo/ # Python streaming and decoding demo
│       ├── 头文件/                    # Header files
│       │   └── HCNetSDK.h           # Main SDK header
│       ├── 库文件/                    # Library files
│       │   ├── libhcnetsdk.so       # Main SDK library
│       │   └── HCNetSDKCom/         # Component libraries
│       └── 开发文档/                  # Development documentation
```

## Recording Function Signatures

### From HCNetSDK.h header file:
```cpp
// Basic recording functions
NET_DVR_API BOOL __stdcall NET_DVR_SaveRealData(LONG lRealHandle, char *sFileName);
NET_DVR_API BOOL __stdcall NET_DVR_StopSaveRealData(LONG lRealHandle);

// V30 version with transport type
NET_DVR_API BOOL __stdcall NET_DVR_SaveRealData_V30(LONG lRealHandle, DWORD dwTransType, char *sFileName);
```

## Recording Examples Found

### 1. Qt Demo Recording Implementation (C++)
**File**: QtDemo/src/RealPlay/realplay.cpp

```cpp
// Start recording
void RealPlay::on_pushButton_savefile_clicked()
{
    if (m_rpsavestopflag == 0)  // Not recording
    {
        // Create directory structure with date
        QString filepath(SAVE_REALDATA_FILEPATH);
        QString currDate = QDateTime::currentDateTime().toString("yyyy-MM-dd");
        QString currTime = QDateTime::currentDateTime().toString("hh_mm_ss");
        
        filepath.append(currDate);
        filepath.append("/");
        directory.mkpath(filepath);
        filepath.append(currTime);
        filepath.append(".mp4");

        // Start recording
        if (!NET_DVR_SaveRealData(m_rpcurrentrealhandle, filepath.toLatin1().data()))
        {
            QMessageBox::information(this,tr("NET_DVR_SaveRealData Error"),
                tr("SDK_LASTERROR=%1").arg(NET_DVR_GetLastError()));
        }
        else
        {
            m_rpsavestopflag = 1;
            ui.pushButton_savefile->setText("stop record");
        }
    }
    else  // Currently recording, stop it
    {   
        if (!NET_DVR_StopSaveRealData(m_rpcurrentrealhandle))
        {
            QMessageBox::information(this,tr("NET_DVR_StopSaveRealData Error"),
                tr("SDK_LASTERROR=%1").arg(NET_DVR_GetLastError()));
        }
        else
        {
            m_rpsavestopflag = 0;
            ui.pushButton_savefile->setText("record");
        }
    }
}
```

### 2. PS Data Callback Demo (C++)
**File**: psdatacall_demo/main.cpp

```cpp
// Manual file writing approach using PS data callback
FILE *g_pFile = NULL;

void PsDataCallBack(LONG lRealHandle, DWORD dwDataType, BYTE *pPacketBuffer, 
                    DWORD nPacketSize, void* pUser)
{
    if (dwDataType == NET_DVR_SYSHEAD)
    {	
        // Write header data
        g_pFile = fopen("./record/ps.dat", "wb");
        if (g_pFile == NULL)
        {
            printf("CreateFileHead fail\n");
            return;
        }
        fwrite(pPacketBuffer, sizeof(unsigned char), nPacketSize, g_pFile);
        printf("write head len=%d\n", nPacketSize);
    }
    else
    {
        if(g_pFile != NULL)
        {
            fwrite(pPacketBuffer, sizeof(unsigned char), nPacketSize, g_pFile);
            printf("write data len=%d\n", nPacketSize);
        }
    }	
}

// In main streaming function:
int iRealPlayHandle = NET_DVR_RealPlay_V40(iUserID, &struPreviewInfo, 
                                          PsDataCallBack, NULL);
```

### 3. Python Recording Example
**File**: 3-Python开发示例/1-预览取流解码Demo/test_main.py

```python
def RealDataCallBack_V30(self, lPlayHandle, dwDataType, pBuffer, dwBufSize, pUser):
    # Linux platform recording
    if sys_platform == 'linux':
        if dwDataType == NET_DVR_SYSHEAD:
            # Create new file with timestamp
            from datetime import datetime
            current_time = datetime.now()
            timestamp_str = current_time.strftime('%Y%m%d_%H%M%S')
            self.preview_file = f'./previewVideo{timestamp_str}.mp4'
        elif dwDataType == NET_DVR_STREAMDATA:
            # Write stream data to file
            self.writeFile(self.preview_file, pBuffer, dwBufSize)

def writeFile(self, filePath, pBuffer, dwBufSize):
    # Convert pointer data to byte array
    data_array = (c_byte * dwBufSize)()
    memmove(data_array, pBuffer, dwBufSize)
    
    # Create file if not exists
    if not os.path.exists(filePath):
        open(filePath, "w").close()
    
    # Append data to file
    preview_file_output = open(filePath, 'ab')
    preview_file_output.write(data_array)
    preview_file_output.close()
```

### 4. Java Recording Example
**File**: ClientDemo/src/com/NetSDKDemo/VideoDemo.java

```java
// Using NET_DVR_SaveRealData_V30 with transport type
public void SaveRealData(int PlayHandle) {
    String path = System.getProperty("user.dir") + "\\Download\\SaveRealData.mp4";
    
    // Convert path to byte array for SDK
    byte[] byPath = path.getBytes("GBK");
    HCNetSDK.BYTE_ARRAY byArray = new HCNetSDK.BYTE_ARRAY(byPath.length);
    System.arraycopy(byPath, 0, byArray.byValue, 0, byPath.length);
    byArray.write();
    
    // Start recording (0x2 = save as MP4)
    Boolean bSaveVideo = hCNetSDK.NET_DVR_SaveRealData_V30(PlayHandle, 0x2, 
                                                          byArray.getPointer());
    if (bSaveVideo == false) {
        int iErr = hCNetSDK.NET_DVR_GetLastError();
        System.err.println("NET_DVR_SaveRealData_V30 failed" + iErr);
        return;
    }
    System.out.println("NET_DVR_SaveRealData_V30 success");
}

// Stop recording
public void StopSaveRealData(int PlayHandle) {
    if(!hCNetSDK.NET_DVR_StopSaveRealData(PlayHandle)) {
        System.err.println("NET_DVR_StopSaveRealData failed");
    }
}
```

## Key Findings

1. **Two Recording Approaches**:
   - **Direct SDK Recording**: Use `NET_DVR_SaveRealData()` or `NET_DVR_SaveRealData_V30()`
   - **Manual Recording**: Use data callbacks to write stream data manually

2. **File Formats**:
   - Basic `NET_DVR_SaveRealData()`: Saves in device's native format
   - `NET_DVR_SaveRealData_V30()`: Allows specifying format (0x2 for MP4)

3. **Prerequisites**:
   - Must have an active real play handle from `NET_DVR_RealPlay_V40()`
   - Recording starts/stops on the same handle used for preview

4. **Error Handling**:
   - Always check return values
   - Use `NET_DVR_GetLastError()` for error codes

5. **File Management**:
   - Qt demo creates date-based directory structure
   - Files are named with timestamps to avoid conflicts