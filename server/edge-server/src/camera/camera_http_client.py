"""
优化的相机HTTP客户端
支持连接池、重试机制、熔断器等高级特性
"""

import asyncio
import aiohttp
import logging
import time
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from urllib.parse import urljoin
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """熔断器状态"""
    CLOSED = "closed"  # 正常状态
    OPEN = "open"      # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5  # 失败阈值
    recovery_timeout: int = 60  # 恢复超时(秒)
    expected_exception: type = Exception  # 预期异常类型


class CircuitBreaker:
    """熔断器实现"""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED
    
    def call(self, func):
        """装饰器方法"""
        async def wrapper(*args, **kwargs):
            if self.state == CircuitState.OPEN:
                if self._should_attempt_reset():
                    self.state = CircuitState.HALF_OPEN
                else:
                    raise Exception("Circuit breaker is OPEN")
            
            try:
                result = await func(*args, **kwargs)
                self._on_success()
                return result
            except self.config.expected_exception as e:
                self._on_failure()
                raise e
        
        return wrapper
    
    def _should_attempt_reset(self) -> bool:
        """检查是否应该尝试重置"""
        return (self.last_failure_time and 
                datetime.now() - self.last_failure_time > 
                timedelta(seconds=self.config.recovery_timeout))
    
    def _on_success(self):
        """成功时重置"""
        self.failure_count = 0
        self.state = CircuitState.CLOSED
    
    def _on_failure(self):
        """失败时增加计数"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.failure_count >= self.config.failure_threshold:
            self.state = CircuitState.OPEN


class CameraHTTPClient:
    """优化的相机HTTP客户端"""
    
    def __init__(self, base_url: str, config: Optional[Dict] = None):
        """
        初始化客户端
        
        Args:
            base_url: 相机服务基础URL
            config: 配置参数
        """
        self.base_url = base_url.rstrip('/')
        self.config = config or {}
        
        # 连接池配置
        self.max_connections = self.config.get('max_connections', 100)
        self.max_keepalive = self.config.get('max_keepalive', 30)
        
        # 重试配置
        self.max_retries = self.config.get('max_retries', 3)
        self.retry_delay = self.config.get('retry_delay', 1.0)
        
        # 超时配置
        self.timeout = aiohttp.ClientTimeout(
            total=self.config.get('timeout', 30),
            connect=self.config.get('connect_timeout', 5),
            sock_read=self.config.get('read_timeout', 20)
        )
        
        # 熔断器
        breaker_config = CircuitBreakerConfig(
            failure_threshold=self.config.get('breaker_failure_threshold', 5),
            recovery_timeout=self.config.get('breaker_recovery_timeout', 60)
        )
        self.circuit_breaker = CircuitBreaker(breaker_config)
        
        # 会话管理
        self._session: Optional[aiohttp.ClientSession] = None
        self._connector: Optional[aiohttp.TCPConnector] = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        await self.close()
    
    async def connect(self):
        """建立连接"""
        if self._session:
            return
        
        # 创建连接器
        self._connector = aiohttp.TCPConnector(
            limit=self.max_connections,
            limit_per_host=self.max_connections,
            keepalive_timeout=self.max_keepalive,
            force_close=False
        )
        
        # 创建会话
        self._session = aiohttp.ClientSession(
            connector=self._connector,
            timeout=self.timeout,
            headers={
                'User-Agent': 'EdgeServer-CameraClient/2.0',
                'Connection': 'keep-alive'
            }
        )
        
        logger.info(f"相机客户端已连接到: {self.base_url}")
    
    async def close(self):
        """关闭连接"""
        if self._session:
            await self._session.close()
            self._session = None
        if self._connector:
            await self._connector.close()
            self._connector = None
        
        logger.info("相机客户端连接已关闭")
    
    @circuit_breaker.call
    async def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        发送HTTP请求（带重试和熔断）
        
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 其他请求参数
            
        Returns:
            响应数据
        """
        if not self._session:
            await self.connect()
        
        url = urljoin(self.base_url, endpoint)
        
        for attempt in range(self.max_retries):
            try:
                async with self._session.request(method, url, **kwargs) as response:
                    data = await response.json()
                    
                    if response.status >= 400:
                        error_msg = data.get('error', f'HTTP {response.status}')
                        raise aiohttp.ClientResponseError(
                            request_info=response.request_info,
                            history=response.history,
                            status=response.status,
                            message=error_msg
                        )
                    
                    return data
                    
            except (aiohttp.ClientError, asyncio.TimeoutError) as e:
                if attempt == self.max_retries - 1:
                    logger.error(f"请求失败 {method} {url}: {e}")
                    raise
                
                wait_time = self.retry_delay * (2 ** attempt)  # 指数退避
                logger.warning(f"请求失败，{wait_time}秒后重试 ({attempt + 1}/{self.max_retries})")
                await asyncio.sleep(wait_time)
    
    # === API方法 ===
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return await self._request('GET', '/health')
    
    async def get_channels(self) -> List[Dict[str, Any]]:
        """获取通道列表"""
        result = await self._request('GET', '/api/v2/channels')
        return result.get('channels', [])
    
    async def get_channel_info(self, channel_id: int) -> Dict[str, Any]:
        """获取通道信息"""
        result = await self._request('GET', f'/api/v2/channels/{channel_id}')
        return result.get('channel', {})
    
    async def capture_image(self, channel_id: int, quality: int = 0) -> Dict[str, Any]:
        """抓图"""
        params = {'quality': quality}
        return await self._request('POST', f'/api/v2/channels/{channel_id}/capture', params=params)
    
    async def start_recording(self, channel_id: int, duration: int = 60) -> Dict[str, Any]:
        """开始录像"""
        data = {'duration': duration}
        return await self._request('POST', f'/api/v2/channels/{channel_id}/record/start', json=data)
    
    async def stop_recording(self, channel_id: int) -> Dict[str, Any]:
        """停止录像"""
        return await self._request('POST', f'/api/v2/channels/{channel_id}/record/stop')
    
    async def get_recording_status(self, channel_id: int) -> Dict[str, Any]:
        """获取录像状态"""
        return await self._request('GET', f'/api/v2/channels/{channel_id}/record/status')
    
    async def search_recordings(self, channel_id: int, start_time: str, end_time: str) -> List[Dict[str, Any]]:
        """搜索录像"""
        params = {
            'start_time': start_time,
            'end_time': end_time
        }
        result = await self._request('GET', f'/api/v2/channels/{channel_id}/recordings', params=params)
        return result.get('recordings', [])
    
    async def download_recording(self, channel_id: int, start_time: str, end_time: str) -> Dict[str, Any]:
        """下载录像"""
        data = {
            'start_time': start_time,
            'end_time': end_time
        }
        return await self._request('POST', f'/api/v2/channels/{channel_id}/recordings/download', json=data)
    
    async def control_ptz(self, channel_id: int, command: str, speed: int = 5) -> Dict[str, Any]:
        """云台控制"""
        data = {
            'command': command,
            'speed': speed
        }
        return await self._request('POST', f'/api/v2/channels/{channel_id}/ptz/control', json=data)
    
    async def goto_preset(self, channel_id: int, preset_id: int) -> Dict[str, Any]:
        """转到预置点"""
        return await self._request('POST', f'/api/v2/channels/{channel_id}/ptz/presets/{preset_id}/goto')
    
    async def get_service_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return await self._request('GET', '/api/v2/status')
    
    async def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return await self._request('GET', '/api/v2/statistics')


async def create_camera_client(base_url: str, config: Optional[Dict] = None) -> CameraHTTPClient:
    """
    创建相机客户端实例
    
    Args:
        base_url: 相机服务基础URL
        config: 配置参数
        
    Returns:
        相机客户端实例
    """
    client = CameraHTTPClient(base_url, config)
    await client.connect()
    return client