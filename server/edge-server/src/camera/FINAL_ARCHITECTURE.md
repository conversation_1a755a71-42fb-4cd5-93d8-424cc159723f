# 相机模块最终架构

## 架构概览

```
┌─────────────────┐         HTTP API          ┌──────────────────┐
│   edge-server   │ ◄─────────────────────────► │  camera-service  │
│  (HTTP Bridge)  │         Port 7090          │  (SDK Control)   │
└─────────────────┘                            └──────────────────┘
        │                                               │
        │                                               │
        ▼                                               ▼
  ┌────────────┐                              ┌─────────────────┐
  │  Task Bus  │                              │  Hikvision NVR  │
  └────────────┘                              └─────────────────┘
```

## 模块职责

### edge-server/src/camera/
- **职责**: HTTP桥接，任务总线集成
- **核心文件**:
  - `camera_module.py`: TaskBusModule实现，处理相机任务
  - `camera_client.py`: HTTP客户端，调用camera-service API
- **不包含**: 任何SDK代码或硬件控制逻辑

### camera-service/
- **职责**: 相机硬件控制，SDK封装
- **核心文件**:
  - `camera_control_service.py`: Flask服务，提供RESTful API
- **功能**: 连接管理、抓拍、录像、回放

## API端点

### camera-service提供的API
- `GET /health` - 健康检查
- `GET /api/camera/status` - 获取状态
- `POST /api/camera/connect` - 连接NVR
- `POST /api/camera/disconnect` - 断开连接
- `POST /api/camera/snapshot` - 抓拍
- `POST /api/camera/record/start` - 开始录像
- `POST /api/camera/record/stop` - 停止录像
- `POST /api/camera/playback/start` - 开始回放录像

## 通信流程

1. **任务接收**: edge-server从任务总线接收相机任务
2. **HTTP调用**: camera_module通过camera_client调用camera-service
3. **SDK控制**: camera-service使用海康SDK控制硬件
4. **结果返回**: 结果通过HTTP响应返回给edge-server
5. **数据上传**: edge-server将结果上传到MinIO

## 配置

### edge-server配置
```yaml
camera:
  service_url: "http://localhost:7090"
```

### camera-service配置
```json
{
  "nvr": {
    "ip": "*************",
    "port": 8000,
    "username": "admin",
    "password": "******"
  },
  "server": {
    "host": "0.0.0.0",
    "port": 7090
  }
}
```

## 部署注意事项

1. **启动顺序**: 先启动camera-service，再启动edge-server
2. **端口**: 确保7090端口未被占用
3. **SDK依赖**: camera-service需要海康SDK库文件
4. **网络**: 确保能访问NVR的IP地址