#!/usr/bin/env python3
"""
相机服务HTTP客户端
用于与独立的相机控制服务通信
支持多种HTTP客户端实现和代理修复
"""

import aiohttp
import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime
import base64
import os
import json
import subprocess
import urllib.request
import urllib.parse
import urllib.error
import requests
from enum import Enum

logger = logging.getLogger(__name__)

# 设置环境变量以禁用localhost代理
os.environ['NO_PROXY'] = 'localhost,127.0.0.1,0.0.0.0'
os.environ['no_proxy'] = 'localhost,127.0.0.1,0.0.0.0'


class ClientType(Enum):
    """客户端类型"""
    AIOHTTP = "aiohttp"
    REQUESTS = "requests"
    URLLIB = "urllib"
    CURL = "curl"


class CameraServiceClient:
    """相机服务客户端"""
    
    def __init__(self, base_url: str = None, client_type: ClientType = ClientType.AIOHTTP):
        """
        初始化客户端
        Args:
            base_url: 相机服务基础URL，默认从环境变量获取
            client_type: 客户端类型，默认使用aiohttp
        """
        self.base_url = base_url or os.environ.get('CAMERA_SERVICE_URL', 'http://localhost:7090')
        self.client_type = client_type
        self.session: Optional[aiohttp.ClientSession] = None
        
        # 创建requests会话并禁用代理
        self.requests_session = requests.Session()
        self.requests_session.trust_env = False  # 忽略系统代理设置
        self.requests_session.proxies = {
            'http': None,
            'https': None
        }
        
        logger.info(f"相机服务客户端初始化，服务地址: {self.base_url}，客户端类型: {client_type.value}")
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        if self.client_type == ClientType.AIOHTTP:
            # 创建aiohttp会话，禁用代理
            connector = aiohttp.TCPConnector(
                force_close=True,
                enable_cleanup_closed=True
            )
            self.session = aiohttp.ClientSession(
                connector=connector,
                trust_env=False  # 忽略代理环境变量
            )
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
        if self.requests_session:
            self.requests_session.close()
            
    async def _request(self, method: str, endpoint: str, **kwargs) -> Dict[str, Any]:
        """
        发送HTTP请求，支持多种客户端实现
        Args:
            method: HTTP方法
            endpoint: API端点
            **kwargs: 请求参数
        Returns:
            响应数据
        """
        url = f"{self.base_url}{endpoint}"
        
        # 根据客户端类型选择实现
        if self.client_type == ClientType.AIOHTTP:
            return await self._request_aiohttp(method, url, **kwargs)
        elif self.client_type == ClientType.REQUESTS:
            return await self._request_requests(method, url, **kwargs)
        elif self.client_type == ClientType.URLLIB:
            return await self._request_urllib(method, url, **kwargs)
        elif self.client_type == ClientType.CURL:
            return await self._request_curl(method, url, **kwargs)
        else:
            raise ValueError(f"不支持的客户端类型: {self.client_type}")
    
    async def _request_aiohttp(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """使用aiohttp发送请求"""
        if not self.session:
            connector = aiohttp.TCPConnector(
                force_close=True,
                enable_cleanup_closed=True
            )
            self.session = aiohttp.ClientSession(
                connector=connector,
                trust_env=False
            )
        
        try:
            # 确保不使用代理
            kwargs['proxy'] = None
            
            async with self.session.request(method, url, **kwargs) as response:
                data = await response.json()
                
                if response.status >= 400:
                    logger.error(f"请求失败: {method} {url} - {response.status} - {data}")
                    raise Exception(data.get('error', f'HTTP {response.status}'))
                    
                return data
                
        except aiohttp.ClientError as e:
            logger.error(f"aiohttp网络请求错误: {method} {url} - {e}")
            raise Exception(f"网络错误: {str(e)}")
    
    async def _request_requests(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """使用requests库发送请求（同步转异步）"""
        def sync_request():
            try:
                # 转换aiohttp参数到requests格式
                if 'json' in kwargs:
                    kwargs['json'] = kwargs.get('json')
                if 'data' in kwargs:
                    kwargs['data'] = kwargs.get('data')
                
                # 确保不使用代理
                kwargs['proxies'] = {'http': None, 'https': None}
                
                response = self.requests_session.request(method, url, **kwargs)
                data = response.json()
                
                if response.status_code >= 400:
                    logger.error(f"请求失败: {method} {url} - {response.status_code} - {data}")
                    raise Exception(data.get('error', f'HTTP {response.status_code}'))
                    
                return data
                
            except requests.RequestException as e:
                logger.error(f"requests网络请求错误: {method} {url} - {e}")
                raise Exception(f"网络错误: {str(e)}")
        
        # 在线程池中运行同步请求
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, sync_request)
    
    async def _request_urllib(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """使用urllib发送请求（同步转异步）"""
        def sync_request():
            try:
                # 准备请求数据
                data = None
                headers = kwargs.get('headers', {})
                
                if 'json' in kwargs:
                    data = json.dumps(kwargs['json']).encode('utf-8')
                    headers['Content-Type'] = 'application/json'
                elif 'data' in kwargs:
                    data = kwargs['data']
                    if isinstance(data, dict):
                        data = urllib.parse.urlencode(data).encode('utf-8')
                
                # 创建请求对象
                request = urllib.request.Request(url, data=data, headers=headers, method=method)
                
                # 创建不使用代理的opener
                no_proxy_handler = urllib.request.ProxyHandler({})
                opener = urllib.request.build_opener(no_proxy_handler)
                
                # 发送请求
                with opener.open(request) as response:
                    response_data = response.read().decode('utf-8')
                    data = json.loads(response_data)
                    
                    if response.status >= 400:
                        logger.error(f"请求失败: {method} {url} - {response.status} - {data}")
                        raise Exception(data.get('error', f'HTTP {response.status}'))
                        
                    return data
                    
            except urllib.error.URLError as e:
                logger.error(f"urllib网络请求错误: {method} {url} - {e}")
                raise Exception(f"网络错误: {str(e)}")
        
        # 在线程池中运行同步请求
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, sync_request)
    
    async def _request_curl(self, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """使用curl命令发送请求"""
        def sync_request():
            try:
                # 构建curl命令
                cmd = ['curl', '-X', method, '--noproxy', '*']
                
                # 添加头部
                headers = kwargs.get('headers', {})
                for key, value in headers.items():
                    cmd.extend(['-H', f'{key}: {value}'])
                
                # 添加数据
                if 'json' in kwargs:
                    cmd.extend(['-H', 'Content-Type: application/json'])
                    cmd.extend(['-d', json.dumps(kwargs['json'])])
                elif 'data' in kwargs:
                    if isinstance(kwargs['data'], dict):
                        for key, value in kwargs['data'].items():
                            cmd.extend(['-d', f'{key}={value}'])
                    else:
                        cmd.extend(['-d', str(kwargs['data'])])
                
                # 添加URL
                cmd.append(url)
                
                # 执行命令
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                
                if result.returncode != 0:
                    logger.error(f"curl命令执行失败: {result.stderr}")
                    raise Exception(f"curl错误: {result.stderr}")
                
                # 解析响应
                data = json.loads(result.stdout)
                return data
                
            except subprocess.TimeoutExpired:
                logger.error(f"curl请求超时: {method} {url}")
                raise Exception("请求超时")
            except Exception as e:
                logger.error(f"curl请求错误: {method} {url} - {e}")
                raise Exception(f"curl错误: {str(e)}")
        
        # 在线程池中运行同步请求
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, sync_request)
            
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        return await self._request('GET', '/health')
        
    async def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        response = await self._request('GET', '/api/camera/status')
        return response.get('data', {})
        
    async def take_snapshot(self, channel: int = 1) -> Dict[str, Any]:
        """
        抓拍照片
        Args:
            channel: 通道号
        Returns:
            包含图片数据的字典
        """
        response = await self._request('POST', '/api/camera/snapshot', json={
            'channel': channel
        })
        
        if response.get('success'):
            return response.get('data', {})
        else:
            raise Exception(response.get('error', '抓拍失败'))
            
    async def start_recording(self, channel: int = 1, duration: int = 60) -> Dict[str, Any]:
        """
        开始录像
        Args:
            channel: 通道号
            duration: 录制时长（秒）
        Returns:
            录像信息
        """
        response = await self._request('POST', '/api/camera/record/start', json={
            'channel': channel,
            'duration': duration
        })
        
        if response.get('success'):
            return response.get('data', {})
        else:
            raise Exception(response.get('error', '开始录像失败'))
            
    async def stop_recording(self, channel: int = 1) -> Dict[str, Any]:
        """
        停止录像
        Args:
            channel: 通道号
        Returns:
            停止信息
        """
        response = await self._request('POST', '/api/camera/record/stop', json={
            'channel': channel
        })
        
        if response.get('success'):
            return response.get('data', {})
        else:
            raise Exception(response.get('error', '停止录像失败'))
            
    async def get_stream_url(self, channel: int = 1, stream_type: str = 'main') -> Dict[str, Any]:
        """
        获取实时流地址
        Args:
            channel: 通道号
            stream_type: 流类型（main/sub）
        Returns:
            流地址信息
        """
        response = await self._request('POST', '/api/camera/stream', json={
            'channel': channel,
            'stream_type': stream_type
        })
        
        if response.get('success'):
            return response.get('data', {})
        else:
            raise Exception(response.get('error', '获取流地址失败'))
            
    async def save_snapshot_to_file(self, channel: int, filepath: str) -> bool:
        """
        抓拍并保存到文件
        Args:
            channel: 通道号
            filepath: 保存路径
        Returns:
            是否成功
        """
        try:
            # 抓拍
            snapshot_data = await self.take_snapshot(channel)
            
            # 解码base64图片数据
            if 'image_base64' in snapshot_data:
                image_data = base64.b64decode(snapshot_data['image_base64'])
                
                # 保存到文件
                os.makedirs(os.path.dirname(filepath), exist_ok=True)
                with open(filepath, 'wb') as f:
                    f.write(image_data)
                    
                logger.info(f"快照已保存: {filepath}")
                return True
            else:
                logger.error("抓拍响应中没有图片数据")
                return False
                
        except Exception as e:
            logger.error(f"保存快照失败: {e}")
            return False
    
    async def ptz_control(self, channel: int = 1, command: str = 'stop', speed: int = 5) -> Dict[str, Any]:
        """
        PTZ控制
        Args:
            channel: 通道号
            command: 控制命令 (up/down/left/right/zoom_in/zoom_out/stop)
            speed: 速度(1-7)
        Returns:
            控制结果
        """
        response = await self._request('POST', '/api/camera/ptz/control', json={
            'channel': channel,
            'command': command,
            'params': {
                'speed': speed
            }
        })
        
        if response.get('success'):
            return response.get('data', {})
        else:
            raise Exception(response.get('error', 'PTZ控制失败'))
    
    async def ptz_preset(self, channel: int = 1, action: str = 'call', preset_id: int = 1) -> Dict[str, Any]:
        """
        PTZ预置位操作
        Args:
            channel: 通道号
            action: 操作类型 (set/call/delete)
            preset_id: 预置位ID
        Returns:
            操作结果
        """
        response = await self._request('POST', '/api/camera/ptz/preset', json={
            'channel': channel,
            'action': action,
            'preset_id': preset_id
        })
        
        if response.get('success'):
            return response.get('data', {})
        else:
            raise Exception(response.get('error', '预置位操作失败'))


# 便捷函数
async def quick_snapshot(channel: int = 1, client_type: ClientType = ClientType.REQUESTS) -> Dict[str, Any]:
    """快速抓拍（默认使用requests客户端避免代理问题）"""
    async with CameraServiceClient(client_type=client_type) as client:
        return await client.take_snapshot(channel)
        

async def quick_recording(channel: int = 1, duration: int = 60, client_type: ClientType = ClientType.REQUESTS) -> Dict[str, Any]:
    """快速录像（默认使用requests客户端避免代理问题）"""
    async with CameraServiceClient(client_type=client_type) as client:
        return await client.start_recording(channel, duration)


async def test_all_clients(base_url: str = None) -> Dict[str, Any]:
    """测试所有客户端实现"""
    results = {}
    
    for client_type in ClientType:
        try:
            logger.info(f"测试客户端: {client_type.value}")
            async with CameraServiceClient(base_url=base_url, client_type=client_type) as client:
                status = await client.get_status()
                results[client_type.value] = {
                    "success": True,
                    "status": status
                }
                logger.info(f"{client_type.value} 客户端测试成功")
        except Exception as e:
            results[client_type.value] = {
                "success": False,
                "error": str(e)
            }
            logger.error(f"{client_type.value} 客户端测试失败: {e}")
    
    return results


if __name__ == "__main__":
    # 测试脚本
    import sys
    
    async def main():
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 测试所有客户端
        print("测试所有HTTP客户端实现...")
        results = await test_all_clients()
        
        print("\n测试结果:")
        for client_type, result in results.items():
            print(f"\n{client_type}:")
            if result['success']:
                print(f"  ✓ 成功")
                print(f"  状态: {json.dumps(result['status'], indent=2, ensure_ascii=False)}")
            else:
                print(f"  ✗ 失败")
                print(f"  错误: {result['error']}")
        
        # 找出成功的客户端
        successful_clients = [ct for ct, r in results.items() if r['success']]
        if successful_clients:
            print(f"\n推荐使用的客户端: {successful_clients[0]}")
            
            # 使用成功的客户端进行快照测试
            client_type = ClientType(successful_clients[0])
            print(f"\n使用 {client_type.value} 进行快照测试...")
            try:
                snapshot = await quick_snapshot(client_type=client_type)
                print(f"快照成功: {snapshot}")
            except Exception as e:
                print(f"快照失败: {e}")
        else:
            print("\n错误: 所有客户端都失败了！")
            sys.exit(1)
    
    # 运行测试
    asyncio.run(main())