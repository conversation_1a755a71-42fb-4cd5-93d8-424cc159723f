# /fix-realtime-recording

## 任务概述
从海康威视官方SDK中找到正确的实时录制实现，修复当前的NET_DVR_SaveRealData问题。

## 核心信息
- **官方SDK路径**: `/home/<USER>/server/third-party/hikvision-sdk`
- **已知事实**: test_recording.py之前测试成功，能录制H.264视频
- **目标**: 找到并实现正确的实时流录制方法

## 代理任务分配

### Agent 1: SDK探索代理
**任务**: 深入分析官方SDK目录结构和示例代码
**关键问题**:
- Demo示例中是否有录制功能的实现？
- 头文件中SaveRealData的正确用法？
- 是否有回调函数的示例？
**输出**:
- SDK目录结构图
- 录制相关的示例代码
- API函数签名和参数说明

### Agent 2: 历史代码考古代理
**任务**: 查找之前成功的test_recording.py实现
**关键问题**:
- test_recording.py在哪里？
- 它是如何实现录制的？
- 使用了哪些API调用？
**输出**:
- test_recording.py的完整代码
- 成功录制的关键代码片段
- 与当前实现的差异分析

### Agent 3: SDK文档研究代理
**任务**: 研究开发文档中的录制章节
**依赖**: Agent 1的SDK路径信息
**关键问题**:
- 实时流保存的正确流程是什么？
- 有哪些前置条件？
- 常见错误和解决方案？
**输出**:
- 录制功能的官方文档摘要
- 正确的API调用顺序
- 注意事项和限制

### Agent 4: 对比分析代理
**任务**: 对比当前实现与官方示例的差异
**依赖**: Agent 1、2、3的输出
**关键问题**:
- 当前代码哪里错了？
- 缺少了什么步骤？
- 参数设置是否正确？
**输出**:
- 差异分析报告
- 修复建议
- 具体的代码修改方案

### Agent 5: 实现修复代理
**任务**: 根据分析结果修复代码
**依赖**: Agent 4的分析结果
**输出**:
- 修复后的代码
- 测试脚本
- 验证结果

### Agent 6: 集成测试代理
**任务**: 全面测试修复后的录制功能
**依赖**: Agent 5完成修复
**输出**:
- 测试报告
- 性能对比
- 最终建议

## 执行策略

### 阶段1: 信息收集（Agent 1-3并行）
```python
critical_paths = {
    "sdk_demos": "/home/<USER>/server/third-party/hikvision-sdk/*/Demo示例/",
    "headers": "/home/<USER>/server/third-party/hikvision-sdk/头文件/",
    "docs": "/home/<USER>/server/third-party/hikvision-sdk/开发文档/",
    "test_recording": "查找test_recording.py的位置"
}
```

### 阶段2: 分析对比（Agent 4）
- 找出当前实现与成功案例的差异
- 识别缺失的关键步骤

### 阶段3: 修复实施（Agent 5-6）
- 实现正确的录制方法
- 充分测试验证

## 失败处理
如果代理遇到以下情况，立即报告并重新定义任务：
- 找不到test_recording.py
- SDK目录结构与预期不符
- 文档缺失关键信息
- 示例代码不包含录制功能

## 成功标准
1. 找到官方的录制实现示例
2. 理解之前test_recording.py的成功原因
3. 修复当前的SaveRealData问题
4. 实时录制功能正常工作
5. 生成的H.264文件可播放