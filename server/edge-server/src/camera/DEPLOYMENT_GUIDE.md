# 相机服务部署指南

## 服务概述
相机服务是一个基于海康威视SDK的HTTP API服务，提供相机控制、快照、录像、回放等功能。

## 前置要求

### 1. 硬件要求
- 海康威视NVR设备
- 网络连接到NVR（默认IP: *************）

### 2. 软件要求
- Python 3.8+
- 海康威视SDK库文件
- 必需的Python包：aiohttp, asyncio

### 3. 环境配置
```bash
# NVR配置（可通过环境变量覆盖）
export NVR_IP=*************
export NVR_PORT=8000
export NVR_USERNAME=admin
export NVR_PASSWORD=your_password
```

## 部署步骤

### 1. 准备工作
```bash
# 进入服务目录
cd /home/<USER>/server/edge-server

# 确保SDK库文件存在
ls camera-service/camera/hikvision/库文件/libhcnetsdk.so

# 创建数据目录
mkdir -p /tmp/camera_data
```

### 2. 启动服务

#### 方法1：直接启动
```bash
python3 camera-service/camera_control_service.py
```

#### 方法2：后台启动
```bash
nohup python3 camera-service/camera_control_service.py > camera-service/service.log 2>&1 &
```

#### 方法3：使用systemd（推荐用于生产环境）
创建服务文件 `/etc/systemd/system/camera-service.service`:
```ini
[Unit]
Description=Camera Control Service
After=network.target

[Service]
Type=simple
User=app
WorkingDirectory=/home/<USER>/server/edge-server
ExecStart=/usr/bin/python3 /home/<USER>/server/edge-server/camera-service/camera_control_service.py
Restart=always
RestartSec=10
StandardOutput=append:/var/log/camera-service.log
StandardError=append:/var/log/camera-service.log

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable camera-service
sudo systemctl start camera-service
```

### 3. 验证服务状态
```bash
# 检查进程
ps aux | grep camera_control_service

# 检查端口
netstat -tlnp | grep 7090

# 测试健康检查
curl http://localhost:7090/health
```

## API端点说明

### 基础端点
- `GET /health` - 健康检查
- `GET /` - 服务首页

### 相机控制API
- `POST /api/camera/snapshot` - 拍摄快照
  ```bash
  curl -X POST http://localhost:7090/api/camera/snapshot \
    -H "Content-Type: application/json" \
    -d '{"channel": 1}'
  ```

- `POST /api/camera/stream` - 获取流地址
  ```bash
  curl -X POST http://localhost:7090/api/camera/stream \
    -H "Content-Type: application/json" \
    -d '{"channel": 1}'
  ```

- `POST /api/camera/record/start` - 开始录像（已知问题）
- `POST /api/camera/record/stop` - 停止录像
- `GET /api/camera/status` - 获取状态
- `POST /api/camera/playback/start` - 开始回放录像

## 故障排除

### 1. HTTP 503错误
**问题**: Python请求返回503错误
**原因**: 系统设置了HTTP代理
**解决方案**:
```bash
# 临时解决
unset HTTP_PROXY
unset http_proxy

# 永久解决（添加到.bashrc）
export NO_PROXY=localhost,127.0.0.1
```

### 2. SDK加载失败
**问题**: 无法加载海康威视SDK
**解决方案**:
```bash
# 检查库文件
ldd camera-service/camera/hikvision/库文件/libhcnetsdk.so

# 安装缺失的依赖
sudo apt-get install libssl1.1
```

### 3. NVR连接失败
**问题**: 无法连接到NVR
**解决方案**:
```bash
# 测试网络连接
ping *************

# 检查NVR端口
telnet ************* 8000

# 验证凭据
# 确保用户名密码正确
```

### 4. 实时录像失败
**问题**: 实时录像功能返回错误码0
**状态**: 已知问题，需要进一步调试SDK回调机制
**临时方案**: 使用回放录像功能代替

## 性能优化建议

1. **连接池**: 考虑实现SDK连接池，避免频繁登录/登出
2. **缓存**: 对流地址等静态信息添加缓存
3. **异步处理**: 对耗时操作使用异步任务队列
4. **日志级别**: 生产环境设置为WARNING级别

## 监控建议

1. **健康检查**: 定期调用 `/health` 端点
2. **日志监控**: 监控错误日志中的异常
3. **性能指标**: 记录API响应时间
4. **资源使用**: 监控内存和CPU使用率

## 安全建议

1. **访问控制**: 使用防火墙限制访问端口
2. **认证**: 添加API认证机制
3. **HTTPS**: 使用nginx反向代理提供HTTPS
4. **日志脱敏**: 避免在日志中记录敏感信息

## 维护建议

1. **日志轮转**: 配置日志文件自动轮转
2. **定期重启**: 每周重启一次服务释放资源
3. **备份**: 定期备份录像文件
4. **更新**: 及时更新海康威视SDK