# /fix-camera-service

## 任务概述
修复相机服务的关键问题：Python HTTP客户端503错误和录像功能SDK调用错误，清理冗余代码。

## 代理任务分配

### Agent 1: 问题诊断代理
**任务**: 深入分析Python 503错误和SDK调用问题
**输入**: 
- 相机服务日志
- 错误堆栈追踪
- 网络配置
**输出**:
- 503错误根本原因
- SDK版本和函数映射
- 环境变量和代理设置

### Agent 2: 文档研究代理
**任务**: 查找海康威视SDK文档中的录像方案
**依赖**: Agent 1的SDK版本信息
**输出**:
- 可用的录像函数列表
- 之前测试通过的两个方案
- 最佳实践建议

### Agent 3: HTTP客户端修复代理
**任务**: 实现503错误的完美解决方案
**依赖**: Agent 1的诊断结果
**输出**:
- 修复后的HTTP客户端代码
- 多种客户端实现（aiohttp, httpx, subprocess+curl）
- 性能对比

### Agent 4: 录像功能修复代理
**任务**: 修复录像功能的SDK调用
**依赖**: Agent 2的文档研究
**输出**:
- 修复后的录像功能实现
- 两种录像方案的代码
- 单元测试

### Agent 5: 代码清理代理
**任务**: 删除所有冗余代码
**依赖**: Agent 3和4完成修复
**输出**:
- 清理后的代码结构
- 删除的文件列表
- 代码质量报告

### Agent 6: 集成验证代理
**任务**: 验证所有修复和测试相机服务
**依赖**: 所有代理完成
**输出**:
- 完整的测试报告
- 性能基准
- 部署建议

## 执行策略

### 阶段1: 诊断与研究
```python
critical_questions = {
    "http_503": {
        "proxy_settings": "检查HTTP_PROXY, HTTPS_PROXY环境变量",
        "service_binding": "验证服务监听地址（0.0.0.0 vs 127.0.0.1）",
        "firewall": "检查防火墙规则",
        "curl_vs_python": "为什么curl成功但Python失败"
    },
    "sdk_recording": {
        "deprecated_functions": ["NET_DVR_RealPlay_V40", "NET_DVR_RealPlay_V50"],
        "working_alternatives": "查找文档中的替代方案",
        "test_history": "查找之前测试通过的实现"
    }
}
```

### 阶段2: 实施修复
1. 首先修复HTTP客户端问题
2. 然后修复录像功能
3. 最后清理冗余代码

### 阶段3: 验证
- 单独测试相机服务
- 不依赖边缘服务器
- 完整的功能验证

## 失败处理
如果任何代理遇到以下情况，立即停止并重新定义任务：
- 找不到关键文件
- SDK版本不匹配
- 依赖项缺失
- 测试环境不可用

## 成功标准
1. Python客户端能成功调用所有API
2. 录像功能使用正确的SDK函数
3. 所有冗余代码被移除
4. 通过完整的测试套件