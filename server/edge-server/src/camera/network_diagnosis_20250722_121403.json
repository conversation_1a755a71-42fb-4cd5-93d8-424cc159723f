{"test_time": "2025-07-22T12:13:52.003586", "ping_tests": [{"host": "*************", "ping_success": true, "output": "PING ************* (*************) 56(84) bytes of data.\n64 bytes from *************: icmp_seq=1 ttl=63 time=0.322 ms\n64 bytes from *************: icmp_seq=2 ttl=63 time=0.282 ms\n64 bytes from *************: icmp_seq=3 ttl=63 time=0.300 ms\n64 bytes from *************: icmp_seq=4 ttl=63 time=0.298 ms\n\n--- ************* ping statistics ---\n4 packets transmitted, 4 received, 0% packet loss, time 3052ms\nrtt min/avg/max/mdev = 0.282/0.300/0.322/0.014 ms\n"}, {"host": "*************", "ping_success": true, "output": "PING ************* (*************) 56(84) bytes of data.\n64 bytes from *************: icmp_seq=1 ttl=63 time=1.15 ms\n64 bytes from *************: icmp_seq=2 ttl=63 time=1.06 ms\n64 bytes from *************: icmp_seq=3 ttl=63 time=1.10 ms\n64 bytes from *************: icmp_seq=4 ttl=63 time=1.29 ms\n\n--- ************* ping statistics ---\n4 packets transmitted, 4 received, 0% packet loss, time 3003ms\nrtt min/avg/max/mdev = 1.061/1.147/1.288/0.086 ms\n"}, {"host": "*************", "ping_success": true, "output": "PING ************* (*************) 56(84) bytes of data.\n64 bytes from *************: icmp_seq=1 ttl=63 time=0.630 ms\n64 bytes from *************: icmp_seq=2 ttl=63 time=0.352 ms\n64 bytes from *************: icmp_seq=3 ttl=63 time=0.257 ms\n64 bytes from *************: icmp_seq=4 ttl=63 time=0.404 ms\n\n--- ************* ping statistics ---\n4 packets transmitted, 4 received, 0% packet loss, time 3071ms\nrtt min/avg/max/mdev = 0.257/0.410/0.630/0.137 ms\n"}], "port_tests": [{"host": "*************", "port": 8000, "port_open": true}, {"host": "*************", "port": 8000, "port_open": true}, {"host": "*************", "port": 8000, "port_open": true}], "http_tests": [{"host": "*************", "port": 8000, "http_reachable": true, "status_code": 503, "reason": "Service Unavailable"}, {"host": "*************", "port": 8000, "http_reachable": true, "status_code": 503, "reason": "Service Unavailable"}, {"host": "*************", "port": 8000, "http_reachable": true, "status_code": 503, "reason": "Service Unavailable"}], "network_info": {"ip_addresses": "1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000\n    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00\n    inet 127.0.0.1/8 scope host lo\n       valid_lft forever preferred_lft forever\n    inet6 ::1/128 scope host \n       valid_lft forever preferred_lft forever\n2: ens34: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000\n    link/ether 00:0c:29:f1:0f:90 brd ff:ff:ff:ff:ff:ff\n    altname enp34s2\n    inet *************/24 brd ************* scope global ens34\n       valid_lft forever preferred_lft forever\n    inet6 fe80::20c:29ff:fef1:f90/64 scope link \n       valid_lft forever preferred_lft forever\n3: docker0: <NO-CARRIER,BROADCAST,MULTICAST,UP> mtu 1500 qdisc noqueue state DOWN group default \n    link/ether be:75:70:3f:d9:98 brd ff:ff:ff:ff:ff:ff\n    inet **********/16 brd ************** scope global docker0\n       valid_lft forever preferred_lft forever\n", "routes": "default via ************* dev ens34 proto static \n172.17.0.0/16 dev docker0 proto kernel scope link src ********** linkdown \n192.168.1.0/24 dev ens34 proto kernel scope link src ************* \n", "arp_table": "Address                  HWtype  HWaddress           Flags Mask            Iface\n192.168.1.64                     (incomplete)                              ens34\n192.168.1.201                    (incomplete)                              ens34\n*************            ether   3c:c7:86:9d:36:ac   C                     ens34\n"}}