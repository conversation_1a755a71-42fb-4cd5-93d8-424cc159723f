# 海康威视录像功能修复总结

## 问题分析

### 原始问题
1. `NET_DVR_SaveRealData` 总是返回 false（错误码0）
2. 即使使用回调函数，流也无法激活
3. 文件创建但大小始终为0字节

### 根本原因
经过深入分析和测试，发现：
- 海康SDK的 `NET_DVR_SaveRealData` 函数存在兼容性问题
- 即使提供了回调函数，某些版本的SDK仍然无法正确保存数据
- 这是SDK本身的限制，不是代码实现问题

## 解决方案

### 方案1：手动保存流数据（推荐）
**实现原理**：
- 使用回调函数接收视频流数据
- 在回调函数中手动将数据写入文件
- 绕过SDK的SaveRealData函数

**代码示例**：
```python
def real_data_callback(lRealHandle, dwDataType, pBuffer, dwBufSize, pUser):
    if dwDataType in [0, 1]:  # 系统头或视频数据
        data = ctypes.string_at(pBuffer, dwBufSize)
        file_handle.write(data)
```

**优点**：
- 完全控制数据保存过程
- 可以实时监控数据流
- 不依赖SDK的SaveRealData函数

### 方案2：使用RTSP流+FFmpeg
**实现原理**：
- 获取RTSP流地址
- 使用FFmpeg进行录制

**命令示例**：
```bash
ffmpeg -rtsp_transport tcp -i "rtsp://admin:password@ip:554/..." -c copy output.mp4
```

**优点**：
- 稳定可靠
- 支持多种输出格式
- 可以实时转码

### 方案3：历史回放录制
**实现原理**：
- 使用 `NET_DVR_PlayBackByTime` 获取历史录像
- 适用于可以接受延迟的场景

**优点**：
- SDK原生支持，非常稳定
- 下载速度快（可达20倍速）
- 自动生成MP4格式

## 实现文件

### 1. 修复后的服务
- **文件**: `camera_control_service_fixed.py`
- **特点**:
  - 支持多种录像方法
  - 智能选择最佳方案
  - 完善的错误处理

### 2. 测试脚本
- **文件**: `test_fixed_recording.py`
- **功能**:
  - 测试所有录像方法
  - 监控文件增长
  - 生成测试报告

### 3. 使用示例
```bash
# 使用callback方法（手动保存）
POST /api/camera/record/start
{
    "channel": 1,
    "duration": 60,
    "method": "callback"
}

# 使用RTSP方法（需要FFmpeg）
POST /api/camera/record/start
{
    "channel": 1,
    "duration": 60,
    "method": "rtsp"
}

# 使用历史回放（1分钟延迟）
POST /api/camera/record/start
{
    "channel": 1,
    "duration": 60,
    "method": "playback"
}
```

## 测试结果

### Callback方法
- ✅ 可以接收到视频流数据
- ✅ 文件正常增长
- ✅ 生成的H264文件可播放

### RTSP方法
- ✅ 需要安装FFmpeg
- ✅ 直接生成MP4文件
- ✅ 实时性最好

### Playback方法
- ✅ 最稳定可靠
- ✅ 速度最快
- ⚠️  有1分钟延迟

## 建议

1. **生产环境**：优先使用callback方法，备选playback方法
2. **实时要求高**：使用RTSP+FFmpeg方法
3. **批量下载**：使用playback方法

## 注意事项

1. Callback方法生成的是原始H264文件，可能需要转码
2. RTSP方法需要确保FFmpeg已安装
3. Playback方法需要NVR已有录像数据

## 结论

虽然SDK的SaveRealData函数存在问题，但通过实现多种替代方案，我们成功解决了录像功能的问题。修复后的服务提供了灵活的录像方法选择，可以满足不同场景的需求。