#!/usr/bin/env python3
"""
相机HTTP桥接模块
通过HTTP API与独立的相机控制服务通信
使用优化的HTTP客户端，支持连接池、重试和熔断
"""

import os
import sys
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Optional
import logging

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
from src.modules.task_bus.src.base_module import BaseModule
from src.modules.task_bus.src.task_bus import Task
from .camera_http_client import CameraHTTPClient

logger = logging.getLogger(__name__)


class CameraHTTPBridgeModule(BaseModule):
    """相机HTTP桥接模块"""
    
    def __init__(self, config: Dict):
        super().__init__("camera", ["camera_snapshot", "camera_record", "camera_cruise", "camera_tracking"])
        self.config = config
        
        # 从配置中获取相机服务URL
        camera_service_url = config.get('service_url', 'http://localhost:7090')
        
        # 客户端配置
        client_config = {
            'max_connections': config.get('max_connections', 100),
            'max_retries': config.get('max_retries', 3),
            'timeout': config.get('timeout', 30),
            'breaker_failure_threshold': config.get('breaker_failure_threshold', 5),
            'breaker_recovery_timeout': config.get('breaker_recovery_timeout', 60)
        }
        
        # 使用优化的HTTP客户端
        self.camera_client = CameraHTTPClient(camera_service_url, client_config)
        
        logger.info(f"相机HTTP桥接模块已初始化，服务地址: {camera_service_url}")
        
    async def handle_task(self, task: Task) -> Dict:
        """处理任务"""
        try:
            self.logger.info(f"处理相机任务: {task.id} - {task.type}")
            
            # 确保客户端已连接
            if not self.camera_client._session:
                await self.camera_client.connect()
            
            # 提取参数
            channel = int(task.parameters.get('channel', 1))
            
            # 根据任务类型调用相应的API
            if task.type == 'camera_snapshot':
                result = await self._handle_snapshot(channel, task.parameters)
            elif task.type == 'camera_record':
                result = await self._handle_record(channel, task.parameters)
            elif task.type == 'camera_cruise':
                result = await self._handle_cruise(channel, task.parameters)
            elif task.type == 'camera_tracking':
                result = await self._handle_tracking(channel, task.parameters)
            else:
                return {
                    "status": "error",
                    "error": f"未知的任务类型: {task.type}"
                }
            
            # 上传数据到MinIO
            if result.get('status') == 'success':
                await self._upload_data(result.get('data', {}))
                
            return result
            
        except Exception as e:
            self.logger.error(f"处理任务失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
            
    async def _handle_snapshot(self, channel: int, params: Dict) -> Dict:
        """处理抓拍任务"""
        try:
            count = params.get('count', 1)
            interval = params.get('interval', 1)
            
            snapshots = []
            for i in range(count):
                # 调用相机服务API
                result = await self.camera_client.capture_image(channel)
                snapshot_data = {
                    'filename': result.get('filename'),
                    'path': result.get('path'),
                    'timestamp': result.get('timestamp')
                }
                snapshots.append(snapshot_data)
                
                self.logger.info(f"抓拍 {i+1}/{count} 完成: {snapshot_data.get('filename')}")
                
                if i < count - 1:
                    await asyncio.sleep(interval)
                    
            return {
                "status": "success",
                "data": {
                    "type": "snapshot",
                    "channel": channel,
                    "count": count,
                    "snapshots": snapshots,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            self.logger.error(f"抓拍失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
            
    async def _handle_record(self, channel: int, params: Dict) -> Dict:
        """处理录像任务"""
        try:
            duration = params.get('duration', 60)
            
            # 开始录像
            result = await self.camera_client.start_recording(channel, duration)
            record_data = {
                'filename': result.get('filename'),
                'path': result.get('path'),
                'start_time': result.get('start_time')
            }
            
            self.logger.info(f"开始录像: {record_data.get('filename')}")
            
            # 如果需要等待录像完成
            if params.get('wait_completion', False):
                await asyncio.sleep(duration)
                stop_result = await self.camera_client.stop_recording(channel)
                record_data['stop_time'] = stop_result.get('stop_time')
                record_data['status'] = 'completed'
                record_data['file_size'] = stop_result.get('file_size')
            
            return {
                "status": "success",
                "data": {
                    "type": "record",
                    "channel": channel,
                    "record_info": record_data,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            self.logger.error(f"录像失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
            
    async def _handle_cruise(self, channel: int, params: Dict) -> Dict:
        """处理巡航任务"""
        try:
            # 巡航模式实际上是带有预置位的录像
            duration = params.get('duration', 120)
            preset_positions = params.get('preset_positions', [])
            
            # 开始巡航录像
            result = await self.camera_client.start_recording(channel, duration)
            record_data = {
                'filename': result.get('filename'),
                'path': result.get('path'),
                'start_time': result.get('start_time')
            }
            
            # 如果有预置位，进行云台控制
            if preset_positions:
                # TODO: 实现预置位巡航逻辑
                pass
            
            return {
                "status": "success",
                "data": {
                    "type": "cruise",
                    "channel": channel,
                    "mode": "cruise",
                    "record_info": record_data,
                    "preset_positions": preset_positions,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            self.logger.error(f"巡航任务失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
            
    async def _handle_tracking(self, channel: int, params: Dict) -> Dict:
        """处理追踪任务"""
        try:
            # 追踪模式需要配合图像识别
            duration = params.get('duration', 60)
            target_info = params.get('target', {})
            
            # 开始追踪录像
            result = await self.camera_client.start_recording(channel, duration)
            record_data = {
                'filename': result.get('filename'),
                'path': result.get('path'),
                'start_time': result.get('start_time')
            }
            
            # TODO: 实现目标追踪逻辑
            # 需要集成图像识别和云台控制
            
            return {
                "status": "success",
                "data": {
                    "type": "tracking",
                    "channel": channel,
                    "mode": "tracking",
                    "record_info": record_data,
                    "target_info": target_info,
                    "timestamp": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            self.logger.error(f"追踪任务失败: {e}")
            return {
                "status": "error",
                "error": str(e)
            }
            
    async def _upload_data(self, result: Dict):
        """上传数据到MinIO"""
        try:
            # 构造上传任务
            upload_task = Task(
                id=f"upload_{uuid.uuid4().hex}",
                type="minio_upload",
                parameters={
                    "source_type": "camera",
                    "data": result,
                    "channel": result.get("channel"),
                    "timestamp": result.get("timestamp")
                }
            )
            
            # 提交到任务总线
            await self.task_bus.submit_task(upload_task)
            self.logger.info(f"数据上传任务已提交: {upload_task.id}")
            
        except Exception as e:
            self.logger.error(f"提交上传任务失败: {e}")
            
    async def get_status(self) -> Dict:
        """获取模块状态"""
        try:
            # 检查相机服务状态
            service_status = await self.camera_client.get_service_status()
            
            return {
                "camera_service_connected": service_status.get('nvr_connected', False),
                "camera_service_url": self.camera_client.base_url,
                "sdk_initialized": service_status.get('sdk_initialized', False),
                "recording_channels": service_status.get('recording_channels', []),
                "device_name": service_status.get('device_name', 'Unknown'),
                "circuit_breaker_state": self.camera_client.circuit_breaker.state.value
            }
        except Exception as e:
            return {
                "camera_service_connected": False,
                "camera_service_url": self.camera_client.base_url,
                "circuit_breaker_state": self.camera_client.circuit_breaker.state.value,
                "error": str(e)
            }
            
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await super().__aenter__()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        # 关闭HTTP客户端
        await self.camera_client.close()
        await super().__aexit__(exc_type, exc_val, exc_tb)