# Python HTTP 503错误修复方案

## 问题描述
当系统设置了HTTP代理（如`HTTP_PROXY=http://**************:30000`）时，Python的requests库会对所有HTTP请求使用代理，包括localhost请求。这导致访问本地服务时出现503 Service Unavailable错误。

## 根本原因
1. 系统环境变量设置了`HTTP_PROXY`
2. Python requests库默认使用系统代理设置（`trust_env=True`）
3. 代理服务器无法访问localhost，返回503错误

## 解决方案

### 1. 环境变量方案
在代码开始处设置NO_PROXY环境变量：
```python
import os
os.environ['NO_PROXY'] = 'localhost,127.0.0.1,0.0.0.0'
os.environ['no_proxy'] = 'localhost,127.0.0.1,0.0.0.0'
```

### 2. Requests配置方案
创建requests会话并禁用代理：
```python
import requests

session = requests.Session()
session.trust_env = False  # 忽略环境代理设置
session.proxies = {'http': None, 'https': None}  # 清空代理
response = session.get('http://localhost:7090/health')
```

### 3. 多客户端实现方案
提供多种HTTP客户端实现作为备选：
- **aiohttp**: 异步客户端，设置`trust_env=False`
- **requests**: 同步客户端，配置session禁用代理
- **urllib**: 标准库，使用空ProxyHandler
- **curl**: 外部命令，使用`--noproxy '*'`参数

## 实现详情

### camera_client.py增强
1. 添加`ClientType`枚举，支持多种HTTP客户端
2. 设置NO_PROXY环境变量
3. 为每种客户端实现代理禁用逻辑
4. 提供统一的API接口

### 使用示例
```python
from camera_client import CameraServiceClient, ClientType

# 使用requests客户端（推荐）
async with CameraServiceClient(client_type=ClientType.REQUESTS) as client:
    health = await client.health_check()
    
# 使用aiohttp客户端
async with CameraServiceClient(client_type=ClientType.AIOHTTP) as client:
    status = await client.get_status()
```

## 测试验证

运行测试脚本：
```bash
python test_proxy_fix.py  # 完整测试所有客户端
python verify_fix.py      # 快速验证修复效果
```

## 推荐配置
1. 在生产环境中使用`ClientType.REQUESTS`或`ClientType.AIOHTTP`
2. 在camera_module.py中已配置使用REQUESTS客户端
3. 保持NO_PROXY环境变量设置

## 故障排除
如果仍然遇到问题：
1. 检查相机服务是否运行（端口7090）
2. 验证防火墙设置
3. 使用`curl --noproxy '*' http://localhost:7090/health`测试连接
4. 检查日志文件：`/home/<USER>/server/edge-server/logs/camera_service.log`