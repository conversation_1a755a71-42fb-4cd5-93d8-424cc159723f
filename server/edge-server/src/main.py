"""
边缘服务器主程序
集成任务总线和MinIO数据同步
"""

import os
import sys
import logging
import signal
import asyncio
import threading
import argparse
from typing import Optional
from concurrent import futures
from aiohttp import web

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import get_config
from modules.task_bus.src.task_bus import TaskBus
from hardware.hardware_module import HardwareControlModule
# from lidar.lidar_module import LidarModule  # 暂时注释，稍后修复
# from modules.minio_client.src.minio_module import MinIOModule  # 暂时注释
# from modules.task_processor import TaskProcessor  # 暂时注释
from health_check import get_health_service
from api.health_api import HealthAPI


class EdgeServer:
    """边缘服务器主类"""
    
    def __init__(self, use_http_camera=False):
        self.config = get_config()
        self.task_bus = None
        self.modules = {}
        self.center_connector = None
        self.task_processor = None
        self.running = False
        self.logger = None
        self.use_http_camera = use_http_camera
        self.health_service = None
        self.health_api_app = None
        self.health_api_runner = None
        self.health_api_site = None
        
    def setup_logging(self):
        """设置日志系统"""
        log_dir = self.config.get('system.log_dir', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        log_level = self.config.get('system.log_level', 'INFO')
        
        # 配置根日志
        logging.basicConfig(
            level=getattr(logging, log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.path.join(log_dir, 'edge-server.log')),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger("EdgeServer")
        self.logger.info(f"Edge Server {self.config.get('system.edge_id')} starting...")
        
    def initialize_task_bus(self):
        """初始化任务总线"""
        self.logger.info("Initializing task bus...")
        
        bus_config = self.config.get_task_bus_config()
        self.task_bus = TaskBus(bus_config)
        
        self.logger.info("Task bus initialized")
        
    def initialize_modules(self):
        """初始化并注册所有模块"""
        self.logger.info("Initializing modules...")
        
        # 硬件控制模块
        try:
            hardware_config = self.config.get_module_config('hardware')
            self.modules['hardware'] = HardwareControlModule(hardware_config)
            self.task_bus.register_module(self.modules['hardware'])
            self.logger.info("Hardware module registered")
        except Exception as e:
            self.logger.error(f"Failed to initialize hardware module: {e}")
            
        # 激光雷达模块 - 暂时注释
        # try:
        #     lidar_config = self.config.get_module_config('lidar')
        #     self.modules['lidar'] = LidarModule(lidar_config)
        #     self.task_bus.register_module(self.modules['lidar'])
        #     self.logger.info("Lidar module registered")
        # except Exception as e:
        #     self.logger.error(f"Failed to initialize lidar module: {e}")
            
        # 摄像头模块 - 暂时注释
        # try:
        #     camera_config = self.config.get_module_config('camera')
        #
        #     # 检查是否使用HTTP相机客户端
        #     if self.use_http_camera:
        #         # 使用HTTP桥接模块
        #         from camera.camera_module import CameraHTTPBridgeModule
        #         self.modules['camera'] = CameraHTTPBridgeModule(camera_config)
        #         self.task_bus.register_module(self.modules['camera'])
        #         self.logger.info("Camera HTTP bridge module registered (using external camera service)")
        #     else:
        #         # 不使用HTTP时，记录警告
        #         self.logger.warning("Camera SDK integration is no longer available in edge-server")
        #         self.logger.warning("Please use --use-http-camera flag to use the external camera service")
        #
        # except Exception as e:
        #     self.logger.error(f"Failed to initialize camera module: {e}")
        #     import traceback
        #     self.logger.error(traceback.format_exc())
        #     if 'camera' in self.modules:
        #         del self.modules['camera']

        # MinIO客户端模块 - 暂时注释
        # try:
        #     minio_config = self.config.get_module_config('minio')
        #     self.modules['minio'] = MinIOModule(minio_config)
        #     self.task_bus.register_module(self.modules['minio'])
        #     self.logger.info("MinIO module registered")
        # except Exception as e:
        #     self.logger.error(f"Failed to initialize minio module: {e}")
            
        # 中央服务器连接器已移除 - 现在专注于MinIO数据同步
        self.center_connector = None
        self.logger.info("Center connector disabled - using MinIO for data synchronization")
            
    # 边缘服务器作为gRPC客户端，不需要启动gRPC服务器
    # 所有gRPC通信通过CenterConnectorGRPC处理
        
    def initialize_health_service(self):
        """初始化健康检查服务"""
        try:
            self.logger.info("Initializing health check service...")
            self.health_service = get_health_service()
            
            # 创建健康检查API应用
            self.health_api_app = web.Application()
            health_api = HealthAPI()
            self.health_api_app.add_routes(health_api.get_routes())
            
            self.logger.info("Health check service initialized")
        except Exception as e:
            self.logger.error(f"Failed to initialize health service: {e}")
            
    async def start_health_api(self):
        """启动健康检查API服务"""
        try:
            # 获取健康检查API端口（默认7088，在7080-7100范围内）
            monitoring_config = self.config.get_section('monitoring') or {}
            health_api_port = monitoring_config.get('health_api_port', 7088)
            
            self.logger.info(f"Starting health check API on port {health_api_port}")
            
            # 创建runner并启动
            self.health_api_runner = web.AppRunner(self.health_api_app)
            await self.health_api_runner.setup()
            
            self.health_api_site = web.TCPSite(
                self.health_api_runner,
                '0.0.0.0',
                health_api_port
            )
            await self.health_api_site.start()
            
            self.logger.info(f"Health check API started on http://0.0.0.0:{health_api_port}")
            
            # 启动健康检查服务
            await self.health_service.start()
            
        except Exception as e:
            self.logger.error(f"Failed to start health API: {e}")
        
    def start_hardware_service(self):
        """启动硬件控制服务"""
        try:
            # 检查硬件服务是否已经在运行
            import requests
            try:
                response = requests.get("http://localhost:7080/health", timeout=2)
                if response.status_code == 200:
                    self.logger.info("Hardware service already running")
                    return True
            except:
                pass
            
            # 启动硬件服务
            self.logger.info("Starting hardware control service...")
            hardware_service_path = os.path.join(
                os.path.dirname(__file__), 
                "modules", "hardware", "hardware_control_service.py"
            )
            
            if os.path.exists(hardware_service_path):
                import subprocess
                self.hardware_service_process = subprocess.Popen(
                    ["python", hardware_service_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    cwd=os.path.dirname(hardware_service_path)
                )
                
                # 等待服务启动
                import time
                for i in range(10):
                    time.sleep(1)
                    try:
                        response = requests.get("http://localhost:7080/health", timeout=2)
                        if response.status_code == 200:
                            self.logger.info("Hardware service started successfully")
                            return True
                    except:
                        continue
                        
                self.logger.warning("Hardware service failed to start properly")
            else:
                self.logger.warning(f"Hardware service script not found: {hardware_service_path}")
                
        except Exception as e:
            self.logger.error(f"Failed to start hardware service: {e}")
            
        return False
        
    def start(self):
        """启动边缘服务器"""
        try:
            # 设置日志
            self.setup_logging()
            
            # 验证配置
            if not self.config.validate():
                self.logger.error("Configuration validation failed")
                return False
                
            # 启动硬件控制服务 - 暂时禁用避免段错误
            # self.start_hardware_service()
                
            # 初始化任务总线
            self.initialize_task_bus()
            
            # 初始化模块
            self.initialize_modules()
            
            # 初始化健康检查服务
            self.initialize_health_service()
            
            # 启动任务总线（同步方法）
            self.task_bus.start()
            self.logger.info("Task bus started")
            
            # 启动健康检查API（异步方法需要在事件循环中运行）
            asyncio.create_task(self.start_health_api())
            
            # 初始化任务处理器 - 暂时禁用
            # if 'minio' in self.modules:
            #     self.task_processor = TaskProcessor(
            #         self.task_bus, 
            #         self.modules['minio']
            #     )
            #     self.logger.info("Task processor initialized")
            
            # 边缘服务器专注于MinIO数据同步
            self.logger.info("Edge server configured for MinIO data synchronization")
            
            self.running = True
            self.logger.info("Edge server started successfully")
            
            # 显示系统信息
            self.print_system_info()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start edge server: {e}")
            return False
            
    def stop(self):
        """停止边缘服务器"""
        self.logger.info("Stopping edge server...")
        self.running = False
        
        # 停止健康检查服务
        if self.health_service:
            try:
                # 创建新的事件循环来运行异步停止方法
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self.health_service.stop())
                if self.health_api_runner:
                    loop.run_until_complete(self.health_api_runner.cleanup())
                loop.close()
                self.logger.info("Health check service stopped")
            except Exception as e:
                self.logger.warning(f"Failed to stop health service: {e}")
        
        # 中央服务器连接器已移除
        
        # 停止任务总线（同步方法）
        if self.task_bus:
            self.task_bus.stop()
            
        # 停止硬件服务
        if hasattr(self, 'hardware_service_process'):
            try:
                self.logger.info("Stopping hardware service...")
                self.hardware_service_process.terminate()
                self.hardware_service_process.wait(timeout=5)
            except Exception as e:
                self.logger.warning(f"Failed to stop hardware service: {e}")
                try:
                    self.hardware_service_process.kill()
                except:
                    pass
            
        self.logger.info("Edge server stopped")
        
    def print_system_info(self):
        """打印系统信息"""
        monitoring_config = self.config.get_section('monitoring') or {}
        health_api_port = monitoring_config.get('health_api_port', 7088)
        info = f"""
========================================
Edge Server Information
========================================
Edge ID: {self.config.get('system.edge_id')}
Name: {self.config.get('system.name')}
Location: {self.config.get('system.location')}
Data Sync: MinIO-based data synchronization
Health API Port: {health_api_port}

Registered Modules:
"""
        for name, module in self.modules.items():
            # 安全地获取模块状态
            status = getattr(module.status, 'value', str(module.status)) if hasattr(module, 'status') else 'Unknown'
            info += f"  - {name}: {status}\n"
            
        info += f"""
Health Check Service: Running
  - Basic check interval: 30s
  - API endpoint: http://localhost:{health_api_port}/health

Status: Running
========================================
        """
        print(info)
        
    def get_status(self) -> dict:
        """获取服务器状态"""
        if self.task_bus:
            return self.task_bus.get_status()
        return {}


def signal_handler(signum, frame):
    """信号处理函数"""
    logging.info(f"Received signal {signum}")
    server.stop()
    sys.exit(0)


async def async_main():
    """异步主函数"""
    parser = argparse.ArgumentParser(description='Edge Server')
    parser.add_argument('--config', help='Config file path')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--use-http-camera', action='store_true', 
                       help='Use HTTP camera client instead of SDK integration')
    
    args = parser.parse_args()
    
    # 创建服务器实例
    global server
    server = EdgeServer(use_http_camera=args.use_http_camera)
    
    # 设置信号处理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 启动服务器
    if server.start():
        try:
            # 保持运行
            while server.running:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            server.stop()
    else:
        sys.exit(1)


def main():
    """主函数"""
    # 运行异步主函数
    asyncio.run(async_main())


if __name__ == "__main__":
    main()