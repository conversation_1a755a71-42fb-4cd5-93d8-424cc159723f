"""
中央服务器连接器
负责与中央服务器通信，接收命令并执行
"""

import grpc
import asyncio
import time
import json
import threading
from concurrent import futures
from google.protobuf import empty_pb2
import logging
import os
import sys

# 添加proto路径
proto_path = os.path.join(os.path.dirname(__file__), 'grpc_proto')
sys.path.insert(0, proto_path)

import center_server_pb2
import center_server_pb2_grpc


class CenterConnectorGRPC:
    def __init__(self, task_bus, config):
        self.task_bus = task_bus
        self.config = config
        self.logger = logging.getLogger('CenterConnectorGRPC')
        
        self.channel = None
        self.stub = None
        self.session_id = None
        self.is_running = False
        self.heartbeat_thread = None
        
        # 连接参数
        self.edge_id = config.get('system', {}).get('edge_id', 'edge-001')
        self.center_host = config.get('center_server', {}).get('host', 'localhost')
        self.center_port = config.get('center_server', {}).get('port', 51001)
        
    def start(self):
        """启动连接器"""
        self.is_running = True
        self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
        self.heartbeat_thread.start()
        self.logger.info("Center connector started")
        
    def stop(self):
        """停止连接器"""
        self.is_running = False
        if self.heartbeat_thread:
            self.heartbeat_thread.join(timeout=5)
        if self.channel:
            self.channel.close()
        self.logger.info("Center connector stopped")
        
    def _connect(self):
        """连接到中央服务器"""
        try:
            target = f"{self.center_host}:{self.center_port}"
            self.channel = grpc.insecure_channel(target)
            self.stub = center_server_pb2_grpc.CenterServerStub(self.channel)
            
            # 测试连接
            edge_list = self.stub.GetEdgeServerList(empty_pb2.Empty(), timeout=5)
            self.logger.info(f"Connected to center server, online servers: {len(edge_list.servers)}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to center server: {e}")
            return False
            
    def _register(self):
        """注册到中央服务器"""
        try:
            # 获取模块信息
            modules = self.task_bus.get_module_list()
            capabilities = [module['name'] for module in modules]
            
            # 创建注册请求
            edge_info = center_server_pb2.EdgeServerInfo(
                edge_id=self.edge_id,
                name=self.config.get('system', {}).get('name', '边缘服务器'),
                ip_address=self.config.get('system', {}).get('ip', '127.0.0.1'),
                grpc_port=self.config.get('grpc', {}).get('server', {}).get('port', 51011),
                capabilities=capabilities,
                metadata={
                    "version": "1.0",
                    "location": self.config.get('system', {}).get('location', '未知'),
                    "hardware": "激光雷达+摄像头"
                }
            )
            
            # 发送注册请求
            response = self.stub.RegisterEdgeServer(edge_info, timeout=10)
            
            if response.success:
                self.session_id = response.session_id
                self.logger.info(f"Registered successfully, session_id: {self.session_id}")
                return True
            else:
                self.logger.error(f"Registration failed: {response.message}")
                return False
                
        except Exception as e:
            self.logger.error(f"Registration error: {e}")
            return False
            
    def _heartbeat_loop(self):
        """心跳循环"""
        retry_count = 0
        
        while self.is_running:
            try:
                # 连接和注册
                if not self.stub or not self.session_id:
                    self.logger.info("Connecting and registering...")
                    if not self._connect():
                        time.sleep(5)
                        continue
                        
                    if not self._register():
                        time.sleep(5)
                        continue
                        
                    retry_count = 0
                
                # 发送心跳
                self.logger.debug("Sending heartbeat...")
                self._send_heartbeat()
                
                # 心跳间隔
                time.sleep(5)
                
            except Exception as e:
                self.logger.error(f"Heartbeat loop error: {e}")
                import traceback
                self.logger.error(traceback.format_exc())
                retry_count += 1
                
                if retry_count > 5:
                    self.logger.warning("Too many failures, resetting connection")
                    self.stub = None
                    self.session_id = None
                    retry_count = 0
                    
                time.sleep(5)
                
    def _send_heartbeat(self):
        """发送心跳"""
        try:
            self.logger.debug(f"Preparing heartbeat for edge_id={self.edge_id}, session_id={self.session_id}")
            
            # 构建系统状态
            system_status = center_server_pb2.SystemStatus(
                cpu_usage=10.0,  # TODO: 获取真实CPU使用率
                memory_usage=20.0,  # TODO: 获取真实内存使用率
                disk_usage=30.0,  # TODO: 获取真实磁盘使用率
                is_healthy=True
            )
            
            # 添加模块状态
            modules = self.task_bus.get_module_list()
            for module in modules:
                module_status = center_server_pb2.ModuleStatus(
                    name=module['name'],
                    is_active=module['active'],
                    status=module['status']
                )
                system_status.modules[module['name']].CopyFrom(module_status)
            
            # 获取任务进度
            task_progress = []
            # TODO: 从task_bus获取任务进度
            
            # 发送心跳
            request = center_server_pb2.HeartBeatRequest(
                edge_id=self.edge_id,
                session_id=self.session_id,
                status=system_status,
                task_progress=task_progress
            )
            
            response = self.stub.HeartBeat(request, timeout=5)
            
            if response.success:
                # 处理命令
                if response.commands:
                    self.logger.info(f"Received {len(response.commands)} commands")
                    for command in response.commands:
                        self._process_command(command)
                        
            else:
                self.logger.error("Heartbeat failed")
                self.session_id = None
                
        except grpc.RpcError as e:
            if e.code() == grpc.StatusCode.UNAUTHENTICATED:
                self.logger.warning("Session expired, re-registering")
                self.session_id = None
            else:
                self.logger.error(f"Heartbeat error: {e}")
                
    def _process_command(self, command):
        """处理命令"""
        try:
            self.logger.info(f"Processing command: {command.command_id}, type: {command.type}")
            
            if command.type == "execute_task":
                # 解析任务参数
                task_data = json.loads(command.parameters.get('data', '{}'))
                
                # 转换任务类型
                task_type_map = {
                    'hardware_control': 'hardware',
                    'camera_capture': 'camera', 
                    'lidar_scan': 'lidar'
                }
                
                module_name = task_type_map.get(task_data.get('type'), 'hardware')
                
                # 提交任务到任务总线
                local_task_id = self.task_bus.submit_task(
                    task_type=task_data.get('type', 'unknown'),
                    target=module_name,
                    action=task_data.get('parameters', {}).get('action', 'unknown'),
                    params=task_data.get('parameters', {})
                )
                
                self.logger.info(f"Task submitted to bus: {local_task_id}")
                
                # TODO: 建立任务ID映射关系
                
            elif command.type == "cancel_task":
                task_id = command.parameters.get('task_id')
                self.task_bus.cancel_task(task_id)
                
            elif command.type == "update_config":
                # TODO: 实现配置更新
                pass
                
            else:
                self.logger.warning(f"Unknown command type: {command.type}")
                
        except Exception as e:
            self.logger.error(f"Failed to process command: {e}")