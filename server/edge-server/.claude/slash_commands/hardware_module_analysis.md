# Hardware Module Deep Analysis Command

## 任务定义
对硬件控制模块进行全面深度分析，包括文件架构、功能实现、模块交互和测试方案制定。

## 代理分工

### Agent 1: 文件架构分析师
**任务**: 深入分析硬件控制模块的文件结构
- 列出所有相关文件及其路径
- 分析每个文件的主要职责
- 识别核心类和函数
- 绘制文件间的依赖关系图
**输出要求**: 详细的文件清单和职责说明

### Agent 2: 功能实现分析师
**任务**: 分析模块实现的具体功能
- IO控制功能详解（6个DO口）
- PTZ云台控制功能
- 通信协议分析（HTTP/TCP）
- 紧急关闭机制
- 状态管理和错误处理
**输出要求**: 功能列表和实现细节

### Agent 3: 接口与通信专家
**任务**: 分析模块的对外接口和通信机制
- HTTP API接口定义（端口7080）
- Netty TCP服务（端口7100）
- 与TaskBus的集成方式
- 与串口服务器的通信协议
- 请求/响应格式分析
**输出要求**: 接口文档和通信流程图

### Agent 4: 代码质量审查员
**任务**: 评估代码质量和潜在问题
- 识别代码异味和潜在bug
- 检查错误处理是否完善
- 评估并发安全性
- 分析性能瓶颈
- 建议优化方向
**输出要求**: 代码质量报告和优化建议

### Agent 5: 测试方案设计师
**任务**: 制定全面的测试方案
- 单元测试策略
- 集成测试方案
- 压力测试设计
- 故障注入测试
- 测试环境搭建指南
**输出要求**: 详细的分步测试方案

### Agent 6: 架构优化顾问
**任务**: 提出架构优化建议
- 分析当前架构的优缺点
- 提出模块化改进方案
- 建议性能优化策略
- 设计扩展性改进
- 制定重构路线图
**输出要求**: 架构优化方案和实施步骤

## 协作规则

1. **信息共享**: 每个代理完成初步分析后，共享关键发现
2. **问题澄清**: 如发现假设或不确定信息，立即提出并重新定义任务
3. **交叉验证**: 代理间相互验证发现，确保准确性
4. **迭代优化**: 基于新发现不断优化分析结果

## 任务执行流程

1. **初始扫描** (所有代理并行)
   - 快速浏览相关文件
   - 识别关键信息
   - 列出需要深入分析的点

2. **深度分析** (按依赖顺序)
   - Agent 1 → Agent 2 → Agent 3 (并行)
   - Agent 4 和 Agent 5 基于前三者结果
   - Agent 6 综合所有分析结果

3. **结果整合**
   - 汇总所有代理的分析结果
   - 生成综合报告
   - 提出具体行动建议

## 质量控制

- 每个代理必须基于实际代码，不得猜测
- 发现矛盾信息时，立即核实
- 所有建议必须具体可执行
- 测试方案必须包含具体命令和预期结果

## 最终交付物

1. **硬件控制模块完整文档**
   - 架构图
   - 功能说明
   - 接口文档

2. **测试方案文档**
   - 环境准备
   - 测试步骤
   - 验证标准

3. **优化建议报告**
   - 当前问题
   - 改进方案
   - 实施计划