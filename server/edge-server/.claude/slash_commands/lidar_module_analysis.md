# LiDAR Module Deep Analysis Command

## 任务定义
对激光雷达（LiDAR）模块进行全面深度分析，为后续与硬件控制模块的协调测试做准备。

## 代理分工

### Agent 1: 文件架构分析师
**任务**: 深入分析LiDAR模块的文件结构和组织
- 列出所有LiDAR相关文件及路径
- 分析每个文件的职责和功能
- 识别核心类、接口和方法
- 绘制模块间依赖关系图
- 特别关注与硬件控制的接口
**输出要求**: 详细的文件清单、职责说明和依赖关系图

### Agent 2: 功能实现分析师
**任务**: 分析LiDAR模块的具体功能实现
- 扫描模式分析（定点扫描、地形扫描等）
- 数据采集机制（频率、格式、缓冲）
- SDK集成方式（华为LiDAR SDK）
- 数据处理流程
- 错误处理和恢复机制
**输出要求**: 功能详解、实现细节和数据流程图

### Agent 3: 接口与集成专家
**任务**: 分析LiDAR模块的对外接口和集成机制
- TaskBus集成方式和任务定义
- 与硬件控制模块的交互（电源控制、PTZ协调）
- 数据输出接口（MinIO存储）
- 配置管理和参数设置
- 状态报告和监控接口
**输出要求**: 接口文档、集成流程和交互序列图

### Agent 4: 代码质量审查员
**任务**: 评估LiDAR模块的代码质量
- 异步编程实践检查
- 资源管理（内存、文件句柄）
- 错误处理完整性
- 性能瓶颈识别
- 代码可维护性评估
**输出要求**: 代码质量报告和改进建议

### Agent 5: 测试方案设计师
**任务**: 制定LiDAR模块的测试方案
- 单元测试（SDK模拟、数据处理）
- 集成测试（与硬件控制协调）
- 性能测试（数据采集速率、处理延迟）
- 稳定性测试（长时间运行）
- 故障恢复测试
**输出要求**: 完整的测试方案和验证标准

### Agent 6: 硬件协调专家
**任务**: 分析LiDAR与硬件控制的协调关系
- 启动顺序和依赖关系
- PTZ云台与扫描的同步机制
- 电源管理协调（DO1控制）
- 紧急停止流程
- 状态同步机制
**输出要求**: 协调流程图和时序图

### Agent 7: 数据流分析师
**任务**: 分析数据采集、处理和存储流程
- 原始数据格式和结构
- 数据预处理和过滤
- 缓冲和队列管理
- 文件存储格式（PCD、CSV等）
- MinIO上传机制
**输出要求**: 数据流程图和格式规范

## 协作规则

1. **零假设原则**: 所有分析必须基于实际代码，发现假设立即停止并重新定义任务
2. **交叉验证**: 代理间共享发现，相互验证信息准确性
3. **问题上报**: 发现不确定信息立即上报，集体讨论解决方案
4. **迭代优化**: 基于新发现不断调整分析深度和方向

## 任务执行流程

### 第一轮：快速扫描（所有代理并行）
1. 定位LiDAR相关文件
2. 识别关键配置和入口点
3. 列出需要深入分析的要点
4. 发现潜在的问题和风险

### 第二轮：深度分析（按依赖顺序）
1. Agent 1 完成文件架构分析
2. Agent 2、3、6、7 并行深入分析
3. Agent 4 基于前述结果进行质量审查
4. Agent 5 综合所有信息设计测试方案

### 第三轮：协调整合
1. Agent 6 重点分析与硬件控制的协调点
2. 所有代理共同制定集成测试策略
3. 生成协调测试检查清单

## 质量控制标准

1. **准确性**: 所有信息必须可在代码中验证
2. **完整性**: 覆盖所有关键功能和接口
3. **实用性**: 输出必须对协调测试有直接价值
4. **可追溯性**: 所有发现标注具体代码位置

## 特别关注点

1. **硬件依赖**:
   - LiDAR电源控制（DO1）
   - PTZ协调扫描
   - 启动时序要求

2. **数据一致性**:
   - 扫描参数与PTZ位置同步
   - 时间戳对齐
   - 坐标系转换

3. **异常处理**:
   - 硬件故障时的降级
   - 数据采集中断恢复
   - 紧急停止响应

4. **性能指标**:
   - 数据采集延迟
   - CPU/内存占用
   - 网络带宽使用

## 最终交付物

1. **LiDAR模块技术文档**
   - 架构说明
   - 功能详解
   - 接口规范
   - 数据格式

2. **协调测试方案**
   - 硬件-LiDAR联调步骤
   - 集成测试用例
   - 性能基准测试
   - 故障场景测试

3. **优化建议报告**
   - 当前问题汇总
   - 性能优化方向
   - 架构改进建议
   - 风险控制措施

4. **运维指南**
   - 部署检查清单
   - 监控指标配置
   - 故障排查手册
   - 性能调优指南