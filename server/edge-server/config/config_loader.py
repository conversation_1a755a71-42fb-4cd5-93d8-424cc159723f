"""
配置加载器
负责加载和管理配置文件
"""

import os
import re
import yaml
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from dotenv import load_dotenv

class ConfigLoader:
    """配置加载器"""
    
    def __init__(self, config_path: str = None):
        self.logger = logging.getLogger("ConfigLoader")
        
        # 加载.env文件
        env_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), '.env')
        if os.path.exists(env_path):
            load_dotenv(env_path)
            self.logger.info(f"Loaded environment variables from {env_path}")
        
        # 默认配置文件路径
        if config_path is None:
            config_path = os.path.join(
                os.path.dirname(os.path.dirname(__file__)),
                'config',
                'config.yaml'
            )
            
        self.config_path = config_path
        self.config = {}
        self._load_config()
        
    def _load_config(self):
        """加载配置文件"""
        try:
            if not os.path.exists(self.config_path):
                raise FileNotFoundError(f"Config file not found: {self.config_path}")
                
            with open(self.config_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # 替换环境变量
                content = self._substitute_env_vars(content)
                self.config = yaml.safe_load(content)
                
            self.logger.info(f"Configuration loaded from {self.config_path}")
            
            # 处理环境变量覆盖
            self._process_env_overrides()
            
        except Exception as e:
            self.logger.error(f"Failed to load config: {e}")
            raise
            
    def _process_env_overrides(self):
        """处理环境变量覆盖"""
        # 允许通过环境变量覆盖某些配置
        env_mappings = {
            'EDGE_ID': 'system.edge_id',
            'GRPC_PORT': 'grpc.server.port',
            'MINIO_ENDPOINT': 'minio.endpoint',
            'MINIO_ACCESS_KEY': 'minio.access_key',
            'MINIO_SECRET_KEY': 'minio.secret_key',
            'MINIO_BUCKET': 'minio.bucket_name',
            'LOG_LEVEL': 'system.log_level',
            'DEBUG_MODE': 'development.debug_mode',
            'CAMERA_USERNAME': 'camera.nvr.username',
            'CAMERA_PASSWORD': 'camera.nvr.password',
            'NVR_IP': 'camera.nvr.ip',
            'NVR_PORT': 'camera.nvr.port',
            'LIDAR_IP': 'lidar.ip',
            'LIDAR_PORT': 'lidar.port',
            'HARDWARE_API_URL': 'hardware.base_url',
            'IO_CHANNEL': 'hardware.channel_id'
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.environ.get(env_var)
            if value:
                self._set_nested_value(config_path, value)
                self.logger.info(f"Override {config_path} with env var {env_var}")
                
    def _set_nested_value(self, path: str, value: Any):
        """设置嵌套配置值"""
        keys = path.split('.')
        current = self.config
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
            
        # 转换类型
        if isinstance(value, str):
            if value.lower() in ['true', 'false']:
                value = value.lower() == 'true'
            elif value.isdigit():
                value = int(value)
                
        current[keys[-1]] = value
        
    def get(self, path: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = path.split('.')
        current = self.config
        
        try:
            for key in keys:
                current = current[key]
            return current
        except (KeyError, TypeError):
            return default
            
    def get_section(self, section: str) -> Dict:
        """获取配置段"""
        return self.config.get(section, {})
        
    def reload(self):
        """重新加载配置"""
        self._load_config()
        self.logger.info("Configuration reloaded")
        
    def save(self, path: str = None):
        """保存配置"""
        save_path = path or self.config_path
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, sort_keys=False)
                         
            self.logger.info(f"Configuration saved to {save_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to save config: {e}")
            raise
            
    def validate(self) -> bool:
        """验证配置"""
        required_sections = ['system', 'grpc', 'task_bus', 'hardware', 
                           'lidar', 'camera', 'minio']
        
        for section in required_sections:
            if section not in self.config:
                self.logger.error(f"Missing required section: {section}")
                return False
                
        # 验证关键配置
        if not self.get('system.edge_id'):
            self.logger.error("Missing system.edge_id")
            return False
            
        if not self.get('grpc.server.port'):
            self.logger.error("Missing grpc.server.port")
            return False
            
        return True
        
    def get_module_config(self, module_name: str) -> Dict:
        """获取模块配置"""
        module_configs = {
            'hardware': self.get_section('hardware'),
            'lidar': self.get_section('lidar'),
            'camera': self.get_section('camera'),
            'minio': self.get_section('minio')
        }
        
        return module_configs.get(module_name, {})
        
    def get_task_bus_config(self) -> Dict:
        """获取任务总线配置"""
        return self.get_section('task_bus')
        
    def get_grpc_config(self) -> Dict:
        """获取gRPC配置"""
        return self.get_section('grpc')
    
    def get_server_config(self) -> Dict:
        """获取服务器配置"""
        return self.get_section('system')
    
    def get_minio_config(self) -> Dict:
        """获取MinIO配置"""
        return self.get_section('minio')
        
    def _substitute_env_vars(self, content: str) -> str:
        """替换配置文件中的环境变量"""
        # 匹配 ${VAR_NAME} 或 ${VAR_NAME:default_value} 格式
        pattern = r'\$\{([A-Z_]+)(:[^}]+)?\}'
        
        def replacer(match):
            var_name = match.group(1)
            default_value = match.group(2)
            
            # 获取环境变量值
            value = os.environ.get(var_name)
            
            if value is None and default_value:
                # 使用默认值（移除冒号）
                value = default_value[1:]
            elif value is None:
                # 如果没有默认值，保持原样
                return match.group(0)
                
            return value
            
        return re.sub(pattern, replacer, content)
        
    def to_dict(self) -> Dict:
        """转换为字典"""
        return self.config.copy()
        
    def __str__(self):
        return f"ConfigLoader({self.config_path})"
        
    def __repr__(self):
        return self.__str__()


# 全局配置实例
_config_instance: Optional[ConfigLoader] = None

def get_config(config_path: str = None) -> ConfigLoader:
    """获取全局配置实例"""
    global _config_instance
    
    if _config_instance is None:
        _config_instance = ConfigLoader(config_path)
        
    return _config_instance

def reload_config():
    """重新加载配置"""
    global _config_instance
    
    if _config_instance:
        _config_instance.reload()
# 创建全局配置实例
config = ConfigLoader()
