# 边缘服务器配置文件

# 系统基本配置
system:
  edge_id: "${EDGE_ID:edge-001}"
  name: "边缘服务器-生产环境02"
  location: "激光雷达生产站点"
  log_level: "INFO"
  log_dir: "/data/logs/edge-server"

# gRPC配置已移除 - 使用MinIO进行数据同步

# 任务总线配置
task_bus:
  max_queue_size: 1000
  max_workers: 10
  monitor_interval: 5
  task_timeout: 300
  retry_max_attempts: 3
  retry_delay: 5

# 硬件控制模块配置
hardware:
  # 硬件控制服务配置
  service:
    base_url: "${HARDWARE_API_URL:http://localhost:7080}"
    netty_tcp_port: 7100
    version: "3.0"
    state_aware: true    # 启用状态感知模式
  
  # IO控制配置
  io_control:
    channel_id: "${IO_CHANNEL:d4ad2070b92f0000}"
    serial_server_ip: "*************"
    devices:
      lidar:
        do_port: 1
        startup_delay: 30
        default_status: 0  # 激光雷达电源关闭
      ptz:
        do_port: 2
        startup_delay: 5
        default_status: 0  # 云台电源关闭
    # 预留IO端口DO3-DO6（摄像头不通过此模块控制）
    reserved_ports:
      do3: 0  # 预留端口3
      do4: 0  # 预留端口4
      do5: 0  # 预留端口5
      do6: 0  # 预留端口6

  # PTZ云台控制配置  
  ptz_control:
    channel_id: "${PTZ_CHANNEL:0000000000000000}"
    protocol: "Pelco-D"
    serial_config:
      address: 30        # 485地址码
      baud_rate: 9600    # 波特率
      data_bits: 8
      stop_bits: 1
      parity: "none"
    control_params:
      default_speed: 10.0      # 默认速度（度/秒）
      angle_tolerance: 1       # 角度容差（度）
      # 角度范围（根据实际云台规格）
      angle_range:
        pan:                   # 水平角度
          min: 0               # 最小水平角度
          max: 359             # 最大水平角度（0-359度）
        tilt:                  # 俯仰角度  
          min: -60             # 最小俯仰角度
          max: 60              # 最大俯仰角度（-60到60度）
      # 速度表配置（基于硬件速度表）
      speed_tables:
        # 水平速度表 - 64个级别（0-63索引）
        pan_speeds: 64
        pan_speed_range:       # 水平速度范围（度/秒）
          min: 0.1             # 最小速度
          max: 400.0           # 最大速度
        # 俯仰速度表 - 64个级别（0-63索引）
        tilt_speeds: 64
        tilt_speed_range:      # 俯仰速度范围（度/秒）
          min: 0.1             # 最小速度
          max: 180.0           # 最大速度
        # 常用速度预设（度/秒）
        common_speeds:
          slow: 3.0            # 慢速
          normal: 10.0         # 正常速度
          fast: 15.0           # 快速
          max_pan: 19.1        # 水平最大推荐速度
          max_tilt: 10.0       # 俯仰最大推荐速度
        # 速度表说明
        speed_table_info: |
          云台支持112个水平速度级别和112个俯仰速度级别
          水平速度表：0.1-400.0度/秒，共112个精确速度值
          俯仰速度表：0.1-180.0度/秒，共112个精确速度值
          系统会自动选择最接近的速度表索引
          max: 19.1            # 最大速度（水平）

# 激光雷达模块配置（华为LiDAR SDK标准配置）
lidar:
  # 网络配置（华为SDK标准参数）
  sdk_config:
    ip_address: "${LIDAR_IP:*************}"    # 激光雷达IP地址
    local_ip: "*************"                  # 本地绑定IP（边缘服务器IP）
    local_port: 38000                          # 本地监听端口
    multicast_ip: "***********"               # 多播IP
    point_cloud_port: 2368                     # 点云数据端口
    management_port: 58000                     # 管理端口（非8080）
    frame_rate: 10                             # 帧率（5, 10, 20 Hz可选）
    echo_mode: "SINGLE_STRONGEST"              # 回波模式
    buffer_size: 10                            # SDK缓冲区大小
    timeout: 1.0                               # SDK超时时间（秒）
  
  # 业务层配置（边缘服务器特定）
  business_config:
    data_dir: "/data/lidar"  # 数据存储目录
    scan_modes:
      point_scan:
        default_duration: 60                   # 定点扫描默认时长（秒）
        points_threshold: 50000                # 点数阈值
      terrain_scan:
        default_duration: 300                  # 地形扫描默认时长（秒）
        default_resolution: 1.0                # 地形扫描分辨率
        coverage_angles:                       # 扫描角度范围
          horizontal: [-45, 45]                # 水平扫描范围（度）
          vertical: [-30, 30]                  # 垂直扫描范围（度）

# 摄像头模块配置
camera:
  # 相机控制服务URL（独立进程）
  service_url: "${CAMERA_SERVICE_URL:http://localhost:7090}"
  nvr:
    ip: "${NVR_IP:*************}"
    port: ${NVR_PORT:8000}
    username: "${CAMERA_USERNAME:admin}"
    password: "${CAMERA_PASSWORD}"
  data_dir: "/data/camera"
  channels:
    ptz1:
      id: 33
      type: "ptz"
      name: "PTZ Camera 1"
      capabilities: ["pan", "tilt", "zoom"]
    fixed1:
      id: 34
      type: "fixed"
      name: "Fixed Camera 1"
      capabilities: ["record", "snapshot"]
    ptz2:
      id: 35
      type: "ptz"
      name: "PTZ Camera 2"
      capabilities: ["pan", "tilt", "zoom"]
    fixed2:
      id: 36
      type: "fixed"
      name: "Fixed Camera 2"
      capabilities: ["record", "snapshot"]
  recording:
    default_duration: 60
    format: "mp4"
    codec: "h264"

# MinIO存储配置 - 简化版本
minio:
  # 基本连接配置
  endpoint: "localhost:7096"
  access_key: "admin"
  secret_key: "scdx@2024minio"
  secure: false

  # 存储配置
  bucket_name: "sensor-data"
  edge_id: "edge-001"

  # 简化的上传配置
  upload:
    timeout: 300        # 5分钟超时
    max_retries: 3      # 最大重试3次

  # 数据同步配置（连接到中央服务器的MinIO）
  sync:
    enabled: true
    interval: 60        # 每分钟同步一次

# 中央服务器连接配置
center_server:
  enabled: true
  host: "**************"  # 中央服务器IP
  port: 7095  # 中央服务器端口
  heartbeat_interval: 30
  reconnect_interval: 60
  auto_register: true

# 硬件状态同步配置
state_sync:
  enabled: true
  collection_interval: 10  # 状态采集间隔（秒）
  change_threshold: 0.1    # 状态变化阈值（角度变化度数）
  offline_timeout: 30      # 设备离线超时（秒）
  report_on_change: true   # 状态变化时立即上报
  batch_report: false      # 批量上报模式
  
# RPC服务配置
rpc:
  enabled: true
  port: 8090              # RPC服务端口
  websocket_enabled: true # 启用WebSocket支持
  max_connections: 100    # 最大连接数
  
# Redis配置（用于状态持久化）
redis:
  enabled: false          # 默认使用内存存储
  host: "localhost"
  port: 6379
  db: 0
  password: ""
  key_prefix: "edge_hardware:"
  expire_time: 3600       # 状态过期时间（秒）

# 串口服务器配置
serial_server:
  host: "*************"  # 串口服务器IP
  io_port: 7100          # IO控制端口
  reconnect_delay: 5     # 重连延迟（秒）

# 系统监控配置
monitoring:
  health_api_port: 7082  # 健康检查API端口 (改用7082)
  metrics_retention: 1000
  alert_thresholds:
    cpu_percent: 90
    memory_percent: 90
    disk_percent: 90
    queue_usage: 80

# 安全配置
security:
  emergency_shutdown:
    enabled: true
    power_off_all_devices: true
    stop_all_tasks: true
    notify_center: true   # 紧急停止时通知中央服务器

# 硬件保护配置
protection:
  enabled: true
  check_interval: 600     # 保护检查间隔：10分钟（600秒）
  max_power_on_time: 600  # 最大通电时间：10分钟无业务活动自动断电
  auto_shutdown: true     # 超时自动关闭
  critical_devices:
    - "lidar"    # 激光雷达（DO1口）
    - "ptz"      # 云台（DO2口）

  # 条件性断电规则
  conditional_shutdown:
    enabled: true
    rules:
      ptz_home_required: true    # 云台必须归零后才能断电
      home_verification:
        enabled: true            # 启用归零位置验证
        max_attempts: 3          # 最大归零尝试次数
        angle_tolerance: 1.0     # 归零角度容差（度）
        timeout_per_attempt: 10  # 每次尝试超时时间（秒）
      emergency_override:
        enabled: true            # 紧急情况下允许强制断电
        timeout: 30              # 紧急断电超时时间（秒）

  # 设备启动时序控制
  startup_sequence:
    enabled: true
    lidar:
      startup_delay: 30          # 激光雷达启动等待时间（秒）
      power_stabilization: 5     # 电源稳定等待时间（秒）
    ptz:
      startup_delay: 5           # 云台启动等待时间（秒）
      power_stabilization: 2     # 电源稳定等待时间（秒）
      auto_home_on_startup: true # 启动后自动归零

# 开发/测试配置
development:
  debug_mode: false
  mock_hardware: false
  test_data_dir: "/data/test"
