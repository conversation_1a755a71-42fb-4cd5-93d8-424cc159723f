# 边缘服务器环境变量模板
# 复制此文件为 .env 并根据实际环境修改

# 服务器标识
EDGE_ID=edge-001
EDGE_NAME=边缘服务器-生产站点

# Python环境
PYTHONPATH=/home/<USER>/server/edge-server

# gRPC配置
GRPC_PORT=51011
CENTRAL_SERVER_HOST=中央服务器IP
CENTRAL_SERVER_PORT=51001

# 硬件控制配置
HARDWARE_API_URL=http://localhost:7080
IO_CHANNEL=d4ad2070b92f0000

# 激光雷达配置
LIDAR_IP=*************
LIDAR_PORT=2368

# 相机配置
NVR_IP=*************
NVR_PORT=8000
CAMERA_USERNAME=admin
CAMERA_PASSWORD=请设置密码

# MinIO存储配置
# 边缘MinIO
MINIO_ENDPOINT=localhost:51021
MINIO_ACCESS_KEY=admin
MINIO_SECRET_KEY=changeme123
MINIO_BUCKET=sensor-data

# 中央MinIO（数据同步）
CENTRAL_MINIO_ENDPOINT=中央服务器IP:51021
CENTRAL_MINIO_ACCESS_KEY=admin
CENTRAL_MINIO_SECRET_KEY=changeme123
CENTRAL_MINIO_BUCKET=sensor-data

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=/home/<USER>/server/edge-server/logs

# 数据目录
DATA_DIR=/home/<USER>/server/edge-server/data