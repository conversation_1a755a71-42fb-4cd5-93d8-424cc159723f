package com.shdatalink.cautious.doodle.lidar.edge.hardware.ptz;

import com.shdatalink.cautious.doodle.lidar.edge.hardware.lidar.PTZEnum;
import java.util.Arrays;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: dg-lidar-collect-edge.jar:BOOT-INF/classes/com/shdatalink/cautious/doodle/lidar/edge/hardware/ptz/SerialPortPTZAPI.class */
public class SerialPortPTZAPI {
    private static final int ANGLE_REPLY_MSG_BYTE_SIZE = 7;
    private static final String ANGLE_REPLY_MAGIC_NUMBER = "ff";
    private static final Logger logger = LoggerFactory.getLogger((Class<?>) SerialPortPTZAPI.class);
    static float[] PAN_SPEED_TABLE = {0.1f, 0.5f, 1.0f, 2.0f, 3.0f, 7.0f, 7.2f, 7.4f, 7.7f, 8.0f, 9.0f, 9.5f, 10.0f, 10.1f, 10.2f, 10.3f, 10.4f, 10.5f, 10.6f, 10.7f, 10.8f, 10.9f, 11.0f, 11.1f, 11.3f, 11.5f, 11.7f, 11.9f, 12.1f, 12.3f, 12.5f, 12.7f, 12.9f, 13.1f, 13.3f, 13.5f, 13.7f, 13.9f, 14.0f, 14.2f, 14.4f, 14.7f, 14.9f, 15.2f, 15.5f, 15.7f, 15.9f, 16.0f, 16.1f, 16.3f, 16.5f, 16.7f, 16.9f, 17.0f, 17.1f, 17.2f, 17.3f, 17.4f, 17.5f, 17.6f, 17.7f, 17.8f, 17.9f, 18.0f};
    static float[] TILT_SPEED_TABLE = {0.1f, 0.5f, 0.8f, 1.2f, 1.4f, 1.6f, 1.8f, 2.0f, 2.2f, 2.4f, 2.6f, 2.8f, 3.0f, 3.2f, 3.4f, 3.6f, 3.8f, 4.0f, 4.2f, 4.4f, 4.6f, 4.8f, 5.0f, 5.2f, 5.4f, 5.6f, 5.8f, 6.0f, 6.2f, 6.4f, 6.6f, 6.8f, 6.9f, 7.0f, 7.1f, 7.2f, 7.3f, 7.4f, 7.5f, 7.6f, 7.7f, 7.8f, 7.9f, 8.1f, 8.2f, 8.3f, 8.4f, 8.5f, 8.6f, 8.7f, 8.8f, 8.9f, 9.0f, 9.1f, 9.2f, 9.3f, 9.4f, 9.5f, 9.6f, 9.7f, 9.8f, 9.9f, 10.0f, 10.0f};

    public static String calcCheckSum(String data) {
        int dSum = 0;
        int length = data.length();
        int i = 0;
        while (true) {
            int index = i;
            if (index < length) {
                String s = data.substring(index, index + 2);
                dSum += Integer.parseInt(s, 16);
                i = index + 2;
            } else {
                int mod = dSum % 256;
                String checkSumHex = StringUtils.leftPad(Integer.toHexString(mod), 2, '0');
                return checkSumHex;
            }
        }
    }

    public static String calcSpinAngleCmd(int angle, int slaveAddress, PTZEnum directionEnum) {
        int number = PTZEnum.PAN == directionEnum ? angle * 100 : angle >= 0 ? angle * 100 : (360 + angle) * 100;
        String addressHexStr = StringUtils.leftPad(Integer.toHexString(slaveAddress), 2, '0');
        String angleHexStr = StringUtils.leftPad(Integer.toHexString(number), 4, '0');
        String checkSumStr = addressHexStr + "00" + directionEnum.getSpinCode() + angleHexStr;
        String spinCmd = "ff" + checkSumStr + calcCheckSum(checkSumStr);
        return spinCmd;
    }

    public static String calcSearchAngleCmd(int slaveAddress, PTZEnum directionEnum) {
        String address = StringUtils.leftPad(Integer.toHexString(slaveAddress), 2, '0');
        String csStr = address + "00" + directionEnum.getSendSearchCode() + "0000";
        String searchCmd = "ff" + csStr + calcCheckSum(csStr);
        return searchCmd;
    }

    public static String calcSetupSpeedCmd(float speedPerSecond, int slaveAddress, PTZEnum directionEnum) {
        if (directionEnum == PTZEnum.PAN) {
            int index = Arrays.binarySearch(PAN_SPEED_TABLE, speedPerSecond);
            if (index <= -1) {
                throw new IllegalArgumentException("水平速度值不在速度表中,请核对.");
            }
            String address = StringUtils.leftPad(Integer.toHexString(slaveAddress), 2, '0');
            String speedIndex = StringUtils.leftPad(Integer.toHexString(index), 2, '0');
            String speedCmd = "e08106010e" + address + speedIndex;
            String panSpeedCmd = speedCmd + calcCheckSum(speedCmd);
            return panSpeedCmd;
        }
        int index2 = Arrays.binarySearch(TILT_SPEED_TABLE, speedPerSecond);
        if (index2 <= -1) {
            throw new IllegalArgumentException("俯仰速度值不在速度表中,请核对.");
        }
        String addressHexStr = StringUtils.leftPad(Integer.toHexString(slaveAddress), 2, '0');
        String speedIndex2 = StringUtils.leftPad(Integer.toHexString(index2), 2, '0');
        String speedCmd2 = "e08106010f" + addressHexStr + speedIndex2;
        String tiltSpeedCmd = speedCmd2 + calcCheckSum(speedCmd2);
        return tiltSpeedCmd;
    }

    public static PTZ resolvePTZAngle(String msg) {
        try {
            if (StringUtils.isBlank(msg)) {
                return null;
            }
            String[] msgArr = StringUtils.split(msg, " ");
            if (msgArr.length != 7 || !ANGLE_REPLY_MAGIC_NUMBER.equals(msgArr[0])) {
                return null;
            }
            int angle = Integer.parseInt(msgArr[4] + msgArr[5], 16);
            int slaveAddress = Integer.parseInt(msgArr[1], 16);
            int realAngle = angle / 100;
            if (PTZEnum.PAN.getReportSearchCode().equals(msgArr[3])) {
                return new PTZ(slaveAddress, realAngle, PTZEnum.PAN);
            }
            if (PTZEnum.TILT.getReportSearchCode().equals(msgArr[3])) {
                if (realAngle >= 300 && realAngle <= 359) {
                    realAngle -= 360;
                }
                return new PTZ(slaveAddress, realAngle, PTZEnum.TILT);
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("云台控制器解析数据异常", (Throwable) e);
            return null;
        }
    }

    public static String leftSpin(int slaveAddress) {
        String address = StringUtils.leftPad(Integer.toHexString(slaveAddress), 2, '0');
        String cmd = address + "00042000";
        String crc = calcCheckSum(cmd);
        return "ff" + cmd + crc;
    }

    public static String rightSpin(int slaveAddress) {
        String address = StringUtils.leftPad(Integer.toHexString(slaveAddress), 2, '0');
        String cmd = address + "00022000";
        String crc = calcCheckSum(cmd);
        return "ff" + cmd + crc;
    }

    public static String stop(int slaveAddress) {
        String address = StringUtils.leftPad(Integer.toHexString(slaveAddress), 2, '0');
        String cmd = address + "00000000";
        String crc = calcCheckSum(cmd);
        return "ff" + cmd + crc;
    }
}
