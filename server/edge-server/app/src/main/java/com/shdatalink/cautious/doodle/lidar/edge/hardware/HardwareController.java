package com.shdatalink.cautious.doodle.lidar.edge.hardware;

import com.fasterxml.jackson.module.kotlin.KotlinModule;
import com.shdatalink.cautious.doodle.lidar.edge.boot.netty.NetSerialPortServer;
import com.shdatalink.cautious.doodle.lidar.edge.client.http.huawei.HuaweiHttpClient;
import com.shdatalink.cautious.doodle.lidar.edge.hardware.lidar.MyQueue;
import com.shdatalink.cautious.doodle.lidar.edge.hardware.lidar.PTZEnum;
import com.shdatalink.cautious.doodle.lidar.edge.hardware.ptz.PTZService;
import com.shdatalink.cautious.doodle.lidar.edge.hardware.ptz.SerialPortPTZAPI;
import com.shdatalink.cautious.doodle.lidar.edge.hardware.relay.RelayService;
import com.shdatalink.cautious.doodle.lidar.edge.hardware.relay.SerialPortRelayAPI;
import io.netty.handler.traffic.AbstractTrafficShapingHandler;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jmx.export.naming.IdentityNamingStrategy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/* compiled from: HardwareController.kt */
@RequestMapping({"/test"})
@Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"��d\n\u0002\u0018\u0002\n\u0002\u0010��\n��\n\u0002\u0018\u0002\n��\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n��\n\u0002\u0018\u0002\n��\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\t\n\u0002\u0018\u0002\n��\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\b\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n��\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0007\b\u0017\u0018��2\u00020\u0001:\u0003456B+\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\f\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005\u0012\u0006\u0010\u0007\u001a\u00020\b\u0012\u0006\u0010\t\u001a\u00020\n¢\u0006\u0002\u0010\u000bJ\u0012\u0010\u0017\u001a\u00020\u00062\b\b\u0001\u0010\u0018\u001a\u00020\u0019H\u0017J\u0012\u0010\u001a\u001a\u00020\u001b2\b\b\u0001\u0010\u001c\u001a\u00020\u0006H\u0017J\u001c\u0010\u001d\u001a\u00020\u001b2\b\b\u0001\u0010\u001e\u001a\u00020\u001f2\b\b\u0001\u0010 \u001a\u00020\u001fH\u0017J\u0012\u0010!\u001a\u00020\u001b2\b\b\u0001\u0010\u001c\u001a\u00020\u0006H\u0017J\u001c\u0010\"\u001a\u00020\u001f2\b\b\u0001\u0010#\u001a\u00020\u001f2\b\b\u0001\u0010$\u001a\u00020%H\u0017J\u0012\u0010&\u001a\u00020\u001f2\b\b\u0001\u0010\u001c\u001a\u00020\u0006H\u0017J\u0012\u0010'\u001a\u00020\u00062\b\b\u0001\u0010(\u001a\u00020)H\u0017J&\u0010*\u001a\u00020\u001b2\b\b\u0001\u0010\u001e\u001a\u00020+2\b\b\u0001\u0010,\u001a\u00020\u00062\b\b\u0001\u0010 \u001a\u00020\u001fH\u0017J\u001c\u0010-\u001a\u00020\u001b2\b\b\u0001\u0010\u001e\u001a\u00020\u001f2\b\b\u0001\u0010,\u001a\u00020\u0006H\u0017J\u0012\u0010.\u001a\u00020\u001b2\b\b\u0001\u0010/\u001a\u000200H\u0017J\u0012\u00101\u001a\u00020\u001b2\b\b\u0001\u0010\u0018\u001a\u00020\u0019H\u0017J\u001c\u00102\u001a\u00020\u001b2\b\b\u0001\u00103\u001a\u00020\u00062\b\b\u0001\u0010,\u001a\u00020\u0006H\u0017R\u0014\u0010\u0002\u001a\u00020\u0003X\u0096\u0004¢\u0006\b\n��\u001a\u0004\b\f\u0010\rR\u0016\u0010\u000e\u001a\n \u0010*\u0004\u0018\u00010\u000f0\u000fX\u0092\u0004¢\u0006\u0002\n��R\u001a\u0010\u0004\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005X\u0096\u0004¢\u0006\b\n��\u001a\u0004\b\u0011\u0010\u0012R\u0014\u0010\t\u001a\u00020\nX\u0096\u0004¢\u0006\b\n��\u001a\u0004\b\u0013\u0010\u0014R\u0014\u0010\u0007\u001a\u00020\bX\u0096\u0004¢\u0006\b\n��\u001a\u0004\b\u0015\u0010\u0016¨\u00067"}, d2 = {"Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/HardwareController;", "", "huaweiHttpClient", "Lcom/shdatalink/cautious/doodle/lidar/edge/client/http/huawei/HuaweiHttpClient;", "myQueue", "Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/lidar/MyQueue;", "", "relayService", "Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/relay/RelayService;", "ptzService", "Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/ptz/PTZService;", "(Lcom/shdatalink/cautious/doodle/lidar/edge/client/http/huawei/HuaweiHttpClient;Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/lidar/MyQueue;Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/relay/RelayService;Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/ptz/PTZService;)V", "getHuaweiHttpClient", "()Lcom/shdatalink/cautious/doodle/lidar/edge/client/http/huawei/HuaweiHttpClient;", "logger", "Lorg/slf4j/Logger;", "kotlin.jvm.PlatformType", "getMyQueue", "()Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/lidar/MyQueue;", "getPtzService", "()Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/ptz/PTZService;", "getRelayService", "()Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/relay/RelayService;", "calcPTZCommandSts", "spin", "Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/HardwareController$PTZSpinCommand;", "deQueue", "", "v", "doCacheSearch", "address", "", "doNumber", "enQueue", "getAngleValue", "slaveAddress", "direction", "Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/lidar/PTZEnum;", "queuePosition", "relayControl", "rtudo", "Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/HardwareController$RTUDO;", "relayDoSearch", "Ljava/lang/Integer;", "channelName", "repairKaka", "sendSetupSpeedCmd", "spinSpeed", "Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/HardwareController$PTZSpinSpeedCommand;", "sendSpinCmd", "serialPortServer", "data", "PTZSpinCommand", "PTZSpinSpeedCommand", "RTUDO", "lidar-collect-edge"})
@RestController
@CrossOrigin(value = {"*"}, allowedHeaders = {"*"})
/* loaded from: dg-lidar-collect-edge.jar:BOOT-INF/classes/com/shdatalink/cautious/doodle/lidar/edge/hardware/HardwareController.class */
public class HardwareController {

    @NotNull
    private final HuaweiHttpClient huaweiHttpClient;

    @NotNull
    private final MyQueue<String> myQueue;

    @NotNull
    private final RelayService relayService;

    @NotNull
    private final PTZService ptzService;
    private final Logger logger;

    public HardwareController(@NotNull HuaweiHttpClient huaweiHttpClient, @NotNull MyQueue<String> myQueue, @NotNull RelayService relayService, @NotNull PTZService ptzService) {
        Intrinsics.checkNotNullParameter(huaweiHttpClient, "huaweiHttpClient");
        Intrinsics.checkNotNullParameter(myQueue, "myQueue");
        Intrinsics.checkNotNullParameter(relayService, "relayService");
        Intrinsics.checkNotNullParameter(ptzService, "ptzService");
        this.huaweiHttpClient = huaweiHttpClient;
        this.myQueue = myQueue;
        this.relayService = relayService;
        this.ptzService = ptzService;
        this.logger = LoggerFactory.getLogger((Class<?>) HardwareController.class);
    }

    @NotNull
    public HuaweiHttpClient getHuaweiHttpClient() {
        return this.huaweiHttpClient;
    }

    @NotNull
    public MyQueue<String> getMyQueue() {
        return this.myQueue;
    }

    @NotNull
    public RelayService getRelayService() {
        return this.relayService;
    }

    @NotNull
    public PTZService getPtzService() {
        return this.ptzService;
    }

    /* compiled from: HardwareController.kt */
    @Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"��\u001e\n\u0002\u0018\u0002\n\u0002\u0010��\n��\n\u0002\u0010\u000e\n��\n\u0002\u0018\u0002\n��\n\u0002\u0018\u0002\n\u0002\b\u000e\u0018��2\u00020\u0001B-\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\b\u0010\b\u001a\u0004\u0018\u00010\u0007¢\u0006\u0002\u0010\tR\u0018\u0010\b\u001a\u0004\u0018\u00010\u00078\u0006X\u0087\u0004¢\u0006\b\n��\u001a\u0004\b\n\u0010\u000bR\u0018\u0010\u0006\u001a\u0004\u0018\u00010\u00078\u0006X\u0087\u0004¢\u0006\b\n��\u001a\u0004\b\f\u0010\u000bR \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e¢\u0006\u000e\n��\u001a\u0004\b\r\u0010\u000e\"\u0004\b\u000f\u0010\u0010R \u0010\u0004\u001a\u0004\u0018\u00010\u00058\u0006@\u0006X\u0087\u000e¢\u0006\u000e\n��\u001a\u0004\b\u0011\u0010\u0012\"\u0004\b\u0013\u0010\u0014¨\u0006\u0015"}, d2 = {"Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/HardwareController$PTZSpinCommand;", "", "channelName", "", "direction", "Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/lidar/PTZEnum;", "angle", "Ljava/lang/Integer;", "address", "(Ljava/lang/String;Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/lidar/PTZEnum;Ljava/lang/Integer;Ljava/lang/Integer;)V", "getAddress", "()Ljava/lang/Integer;", "getAngle", "getChannelName", "()Ljava/lang/String;", "setChannelName", "(Ljava/lang/String;)V", "getDirection", "()Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/lidar/PTZEnum;", "setDirection", "(Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/lidar/PTZEnum;)V", "lidar-collect-edge"})
    /* loaded from: dg-lidar-collect-edge.jar:BOOT-INF/classes/com/shdatalink/cautious/doodle/lidar/edge/hardware/HardwareController$PTZSpinCommand.class */
    public static final class PTZSpinCommand {

        @jakarta.validation.constraints.NotNull(message = "channel通道名称不能为空")
        @Nullable
        private String channelName;

        @jakarta.validation.constraints.NotNull(message = "旋转方向不能为空")
        @Nullable
        private PTZEnum direction;

        @jakarta.validation.constraints.NotNull(message = "旋转角度不能为空")
        @Nullable
        @Max(359)
        @Min(0)
        private final Integer angle;

        @jakarta.validation.constraints.NotNull(message = "云台从机地址不能为空")
        @Nullable
        @Max(247)
        @Min(KotlinModule.serialVersionUID)
        private final Integer address;

        public PTZSpinCommand(@Nullable String channelName, @Nullable PTZEnum direction, @Nullable Integer angle, @Nullable Integer address) {
            this.channelName = channelName;
            this.direction = direction;
            this.angle = angle;
            this.address = address;
        }

        @Nullable
        public final String getChannelName() {
            return this.channelName;
        }

        public final void setChannelName(@Nullable String str) {
            this.channelName = str;
        }

        @Nullable
        public final PTZEnum getDirection() {
            return this.direction;
        }

        public final void setDirection(@Nullable PTZEnum pTZEnum) {
            this.direction = pTZEnum;
        }

        @Nullable
        public final Integer getAngle() {
            return this.angle;
        }

        @Nullable
        public final Integer getAddress() {
            return this.address;
        }
    }

    @PostMapping({"/ptz/calcSpinCmdStr"})
    @NotNull
    public String calcPTZCommandSts(@RequestBody @Validated @NotNull PTZSpinCommand spin) {
        Intrinsics.checkNotNullParameter(spin, "spin");
        Integer angle = spin.getAngle();
        Intrinsics.checkNotNull(angle);
        int intValue = angle.intValue();
        Integer address = spin.getAddress();
        Intrinsics.checkNotNull(address);
        int intValue2 = address.intValue();
        PTZEnum direction = spin.getDirection();
        Intrinsics.checkNotNull(direction);
        String cmd = SerialPortPTZAPI.calcSpinAngleCmd(intValue, intValue2, direction);
        Intrinsics.checkNotNull(cmd);
        return cmd;
    }

    @PostMapping({"/ptz/sendSpinCmd"})
    public boolean sendSpinCmd(@RequestBody @Validated @NotNull PTZSpinCommand spin) {
        Intrinsics.checkNotNullParameter(spin, "spin");
        Integer angle = spin.getAngle();
        Intrinsics.checkNotNull(angle);
        int intValue = angle.intValue();
        Integer address = spin.getAddress();
        Intrinsics.checkNotNull(address);
        String cmd = SerialPortPTZAPI.calcSpinAngleCmd(intValue, address.intValue(), spin.getDirection());
        Intrinsics.checkNotNullExpressionValue(cmd, "calcSpinAngleCmd(...)");
        this.logger.info("旋转指令:" + cmd);
        NetSerialPortServer.write(spin.getChannelName(), cmd);
        return true;
    }

    /* compiled from: HardwareController.kt */
    @Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"��4\n\u0002\u0018\u0002\n\u0002\u0010��\n��\n\u0002\u0010\u000e\n��\n\u0002\u0018\u0002\n��\n\u0002\u0010\u0007\n��\n\u0002\u0018\u0002\n\u0002\b\u0013\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018��2\u00020\u0001B/\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0001\u0010\b\u001a\u0004\u0018\u00010\t¢\u0006\u0002\u0010\nJ\u000b\u0010\u0016\u001a\u0004\u0018\u00010\u0003HÆ\u0003J\u000b\u0010\u0017\u001a\u0004\u0018\u00010\u0005HÆ\u0003J\u0010\u0010\u0018\u001a\u0004\u0018\u00010\u0007HÆ\u0003¢\u0006\u0002\u0010\u0014J\u000b\u0010\u0019\u001a\u0004\u0018\u00010\tHÆ\u0003J>\u0010\u001a\u001a\u00020��2\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0002\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0003\u0010\b\u001a\u0004\u0018\u00010\tHÆ\u0001¢\u0006\u0002\u0010\u001bJ\u0013\u0010\u001c\u001a\u00020\u001d2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001HÖ\u0003J\t\u0010\u001f\u001a\u00020 HÖ\u0001J\t\u0010!\u001a\u00020\u0003HÖ\u0001R\u0018\u0010\b\u001a\u0004\u0018\u00010\t8\u0006X\u0087\u0004¢\u0006\b\n��\u001a\u0004\b\u000b\u0010\fR \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e¢\u0006\u000e\n��\u001a\u0004\b\r\u0010\u000e\"\u0004\b\u000f\u0010\u0010R\u0018\u0010\u0004\u001a\u0004\u0018\u00010\u00058\u0006X\u0087\u0004¢\u0006\b\n��\u001a\u0004\b\u0011\u0010\u0012R\u001a\u0010\u0006\u001a\u0004\u0018\u00010\u00078\u0006X\u0087\u0004¢\u0006\n\n\u0002\u0010\u0015\u001a\u0004\b\u0013\u0010\u0014¨\u0006\""}, d2 = {"Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/HardwareController$PTZSpinSpeedCommand;", "", "channelName", "", "direction", "Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/lidar/PTZEnum;", "speed", "", "address", "Ljava/lang/Integer;", "(Ljava/lang/String;Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/lidar/PTZEnum;Ljava/lang/Float;Ljava/lang/Integer;)V", "getAddress", "()Ljava/lang/Integer;", "getChannelName", "()Ljava/lang/String;", "setChannelName", "(Ljava/lang/String;)V", "getDirection", "()Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/lidar/PTZEnum;", "getSpeed", "()Ljava/lang/Float;", "Ljava/lang/Float;", "component1", "component2", "component3", "component4", "copy", "(Ljava/lang/String;Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/lidar/PTZEnum;Ljava/lang/Float;Ljava/lang/Integer;)Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/HardwareController$PTZSpinSpeedCommand;", "equals", "", "other", IdentityNamingStrategy.HASH_CODE_KEY, "", "toString", "lidar-collect-edge"})
    /* loaded from: dg-lidar-collect-edge.jar:BOOT-INF/classes/com/shdatalink/cautious/doodle/lidar/edge/hardware/HardwareController$PTZSpinSpeedCommand.class */
    public static final class PTZSpinSpeedCommand {

        @jakarta.validation.constraints.NotNull(message = "channel通道名称不能为空")
        @Nullable
        private String channelName;

        @jakarta.validation.constraints.NotNull
        @Nullable
        private final PTZEnum direction;

        @jakarta.validation.constraints.NotNull
        @Nullable
        private final Float speed;

        @jakarta.validation.constraints.NotNull
        @Nullable
        private final Integer address;

        @Nullable
        public final String component1() {
            return this.channelName;
        }

        @Nullable
        public final PTZEnum component2() {
            return this.direction;
        }

        @Nullable
        public final Float component3() {
            return this.speed;
        }

        @Nullable
        public final Integer component4() {
            return this.address;
        }

        @NotNull
        public final PTZSpinSpeedCommand copy(@Nullable String channelName, @Nullable PTZEnum direction, @Nullable Float speed, @Max(247) @Min(1) @Nullable Integer address) {
            return new PTZSpinSpeedCommand(channelName, direction, speed, address);
        }

        public static /* synthetic */ PTZSpinSpeedCommand copy$default(PTZSpinSpeedCommand pTZSpinSpeedCommand, String str, PTZEnum pTZEnum, Float f, Integer num, int i, Object obj) {
            if ((i & 1) != 0) {
                str = pTZSpinSpeedCommand.channelName;
            }
            if ((i & 2) != 0) {
                pTZEnum = pTZSpinSpeedCommand.direction;
            }
            if ((i & 4) != 0) {
                f = pTZSpinSpeedCommand.speed;
            }
            if ((i & 8) != 0) {
                num = pTZSpinSpeedCommand.address;
            }
            return pTZSpinSpeedCommand.copy(str, pTZEnum, f, num);
        }

        @NotNull
        public String toString() {
            return "PTZSpinSpeedCommand(channelName=" + this.channelName + ", direction=" + this.direction + ", speed=" + this.speed + ", address=" + this.address + ")";
        }

        public int hashCode() {
            int result = this.channelName == null ? 0 : this.channelName.hashCode();
            return (((((result * 31) + (this.direction == null ? 0 : this.direction.hashCode())) * 31) + (this.speed == null ? 0 : this.speed.hashCode())) * 31) + (this.address == null ? 0 : this.address.hashCode());
        }

        public boolean equals(@Nullable Object other) {
            if (this == other) {
                return true;
            }
            if (!(other instanceof PTZSpinSpeedCommand)) {
                return false;
            }
            PTZSpinSpeedCommand pTZSpinSpeedCommand = (PTZSpinSpeedCommand) other;
            return Intrinsics.areEqual(this.channelName, pTZSpinSpeedCommand.channelName) && this.direction == pTZSpinSpeedCommand.direction && Intrinsics.areEqual((Object) this.speed, (Object) pTZSpinSpeedCommand.speed) && Intrinsics.areEqual(this.address, pTZSpinSpeedCommand.address);
        }

        public PTZSpinSpeedCommand(@Nullable String channelName, @Nullable PTZEnum direction, @Nullable Float speed, @Max(247) @Min(1) @Nullable Integer address) {
            this.channelName = channelName;
            this.direction = direction;
            this.speed = speed;
            this.address = address;
        }

        @Nullable
        public final String getChannelName() {
            return this.channelName;
        }

        public final void setChannelName(@Nullable String str) {
            this.channelName = str;
        }

        @Nullable
        public final PTZEnum getDirection() {
            return this.direction;
        }

        @Nullable
        public final Float getSpeed() {
            return this.speed;
        }

        @Nullable
        public final Integer getAddress() {
            return this.address;
        }
    }

    @PostMapping({"/ptz/sendSetupSpeedCmd"})
    public boolean sendSetupSpeedCmd(@RequestBody @Validated @NotNull PTZSpinSpeedCommand spinSpeed) {
        Intrinsics.checkNotNullParameter(spinSpeed, "spinSpeed");
        Float speed = spinSpeed.getSpeed();
        Intrinsics.checkNotNull(speed);
        float floatValue = speed.floatValue();
        Integer address = spinSpeed.getAddress();
        Intrinsics.checkNotNull(address);
        int intValue = address.intValue();
        PTZEnum direction = spinSpeed.getDirection();
        Intrinsics.checkNotNull(direction);
        String cmd = SerialPortPTZAPI.calcSetupSpeedCmd(floatValue, intValue, direction);
        Intrinsics.checkNotNullExpressionValue(cmd, "calcSetupSpeedCmd(...)");
        this.logger.info("发送设置旋转速度指令:" + cmd);
        NetSerialPortServer.write(spinSpeed.getChannelName(), cmd);
        return true;
    }

    @GetMapping({"/ptz/getAngleValue"})
    public int getAngleValue(@RequestParam int slaveAddress, @RequestParam @NotNull PTZEnum direction) {
        Intrinsics.checkNotNullParameter(direction, "direction");
        this.logger.info("获取云台角度，slaveAddress：{}, direction：{}:", Integer.valueOf(slaveAddress), direction);
        Integer angleValue = getPtzService().getAngleValue(slaveAddress, direction);
        Intrinsics.checkNotNullExpressionValue(angleValue, "getAngleValue(...)");
        return angleValue.intValue();
    }

    @GetMapping({"/ptz/repairKaka"})
    public boolean repairKaka(@RequestParam int address, @RequestParam @NotNull String channelName) {
        Intrinsics.checkNotNullParameter(channelName, "channelName");
        String rightSpinCmd = SerialPortPTZAPI.rightSpin(address);
        Intrinsics.checkNotNullExpressionValue(rightSpinCmd, "rightSpin(...)");
        NetSerialPortServer.write(channelName, rightSpinCmd);
        Thread.sleep(AbstractTrafficShapingHandler.DEFAULT_MAX_TIME);
        String stopCmd = SerialPortPTZAPI.stop(address);
        Intrinsics.checkNotNullExpressionValue(stopCmd, "stop(...)");
        NetSerialPortServer.write(channelName, stopCmd);
        return true;
    }

    /* compiled from: HardwareController.kt */
    @Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"��$\n\u0002\u0018\u0002\n\u0002\u0010��\n��\n\u0002\u0010\u000e\n��\n\u0002\u0010\u000b\n��\n\u0002\u0010\b\n��\n\u0002\u0018\u0002\n\u0002\b\u001c\b\u0086\b\u0018��2\u00020\u0001B1\u0012\b\u0010\u0002\u001a\u0004\u0018\u00010\u0003\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0005\u0012\n\b\u0001\u0010\u0006\u001a\u0004\u0018\u00010\u0007\u0012\n\b\u0001\u0010\b\u001a\u0004\u0018\u00010\t¢\u0006\u0002\u0010\nJ\u000b\u0010\u001b\u001a\u0004\u0018\u00010\u0003HÆ\u0003J\u0010\u0010\u001c\u001a\u0004\u0018\u00010\u0005HÆ\u0003¢\u0006\u0002\u0010\u0017J\u0010\u0010\u001d\u001a\u0004\u0018\u00010\u0007HÆ\u0003¢\u0006\u0002\u0010\fJ\u000b\u0010\u001e\u001a\u0004\u0018\u00010\tHÆ\u0003J>\u0010\u001f\u001a\u00020��2\n\b\u0002\u0010\u0002\u001a\u0004\u0018\u00010\u00032\n\b\u0002\u0010\u0004\u001a\u0004\u0018\u00010\u00052\n\b\u0003\u0010\u0006\u001a\u0004\u0018\u00010\u00072\n\b\u0003\u0010\b\u001a\u0004\u0018\u00010\tHÆ\u0001¢\u0006\u0002\u0010 J\u0013\u0010!\u001a\u00020\u00052\b\u0010\"\u001a\u0004\u0018\u00010\u0001HÖ\u0003J\t\u0010#\u001a\u00020\u0007HÖ\u0001J\t\u0010$\u001a\u00020\u0003HÖ\u0001R \u0010\b\u001a\u0004\u0018\u00010\t8\u0006@\u0006X\u0087\u000e¢\u0006\u000e\n��\u001a\u0004\b\u000b\u0010\f\"\u0004\b\r\u0010\u000eR \u0010\u0002\u001a\u0004\u0018\u00010\u00038\u0006@\u0006X\u0087\u000e¢\u0006\u000e\n��\u001a\u0004\b\u000f\u0010\u0010\"\u0004\b\u0011\u0010\u0012R\"\u0010\u0006\u001a\u0004\u0018\u00010\u00078\u0006@\u0006X\u0087\u000e¢\u0006\u0010\n\u0002\u0010\u0015\u001a\u0004\b\u0013\u0010\f\"\u0004\b\u0014\u0010\u000eR\"\u0010\u0004\u001a\u0004\u0018\u00010\u00058\u0006@\u0006X\u0087\u000e¢\u0006\u0010\n\u0002\u0010\u001a\u001a\u0004\b\u0016\u0010\u0017\"\u0004\b\u0018\u0010\u0019¨\u0006%"}, d2 = {"Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/HardwareController$RTUDO;", "", "channelName", "", "f", "", "doNumber", "", "address", "Ljava/lang/Integer;", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Integer;Ljava/lang/Integer;)V", "getAddress", "()Ljava/lang/Integer;", "setAddress", "(Ljava/lang/Integer;)V", "getChannelName", "()Ljava/lang/String;", "setChannelName", "(Ljava/lang/String;)V", "getDoNumber", "setDoNumber", "Ljava/lang/Integer;", "getF", "()Ljava/lang/Boolean;", "setF", "(Ljava/lang/Boolean;)V", "Ljava/lang/Boolean;", "component1", "component2", "component3", "component4", "copy", "(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Integer;Ljava/lang/Integer;)Lcom/shdatalink/cautious/doodle/lidar/edge/hardware/HardwareController$RTUDO;", "equals", "other", IdentityNamingStrategy.HASH_CODE_KEY, "toString", "lidar-collect-edge"})
    /* loaded from: dg-lidar-collect-edge.jar:BOOT-INF/classes/com/shdatalink/cautious/doodle/lidar/edge/hardware/HardwareController$RTUDO.class */
    public static final class RTUDO {

        @jakarta.validation.constraints.NotNull(message = "channel通道名称不能为空")
        @Nullable
        private String channelName;

        @jakarta.validation.constraints.NotNull
        @Nullable
        private Boolean f;

        @jakarta.validation.constraints.NotNull
        @Nullable
        private Integer doNumber;

        @jakarta.validation.constraints.NotNull
        @Nullable
        private Integer address;

        @Nullable
        public final String component1() {
            return this.channelName;
        }

        @Nullable
        public final Boolean component2() {
            return this.f;
        }

        @Nullable
        public final Integer component3() {
            return this.doNumber;
        }

        @Nullable
        public final Integer component4() {
            return this.address;
        }

        @NotNull
        public final RTUDO copy(@Nullable String channelName, @Nullable Boolean f, @Max(32) @Min(1) @Nullable Integer doNumber, @Max(247) @Min(1) @Nullable Integer address) {
            return new RTUDO(channelName, f, doNumber, address);
        }

        public static /* synthetic */ RTUDO copy$default(RTUDO rtudo, String str, Boolean bool, Integer num, Integer num2, int i, Object obj) {
            if ((i & 1) != 0) {
                str = rtudo.channelName;
            }
            if ((i & 2) != 0) {
                bool = rtudo.f;
            }
            if ((i & 4) != 0) {
                num = rtudo.doNumber;
            }
            if ((i & 8) != 0) {
                num2 = rtudo.address;
            }
            return rtudo.copy(str, bool, num, num2);
        }

        @NotNull
        public String toString() {
            return "RTUDO(channelName=" + this.channelName + ", f=" + this.f + ", doNumber=" + this.doNumber + ", address=" + this.address + ")";
        }

        public int hashCode() {
            int result = this.channelName == null ? 0 : this.channelName.hashCode();
            return (((((result * 31) + (this.f == null ? 0 : this.f.hashCode())) * 31) + (this.doNumber == null ? 0 : this.doNumber.hashCode())) * 31) + (this.address == null ? 0 : this.address.hashCode());
        }

        public boolean equals(@Nullable Object other) {
            if (this == other) {
                return true;
            }
            if (!(other instanceof RTUDO)) {
                return false;
            }
            RTUDO rtudo = (RTUDO) other;
            return Intrinsics.areEqual(this.channelName, rtudo.channelName) && Intrinsics.areEqual(this.f, rtudo.f) && Intrinsics.areEqual(this.doNumber, rtudo.doNumber) && Intrinsics.areEqual(this.address, rtudo.address);
        }

        public RTUDO(@Nullable String channelName, @Nullable Boolean f, @Max(32) @Min(1) @Nullable Integer doNumber, @Max(247) @Min(1) @Nullable Integer address) {
            this.channelName = channelName;
            this.f = f;
            this.doNumber = doNumber;
            this.address = address;
        }

        @Nullable
        public final String getChannelName() {
            return this.channelName;
        }

        public final void setChannelName(@Nullable String str) {
            this.channelName = str;
        }

        @Nullable
        public final Boolean getF() {
            return this.f;
        }

        public final void setF(@Nullable Boolean bool) {
            this.f = bool;
        }

        @Nullable
        public final Integer getDoNumber() {
            return this.doNumber;
        }

        public final void setDoNumber(@Nullable Integer num) {
            this.doNumber = num;
        }

        @Nullable
        public final Integer getAddress() {
            return this.address;
        }

        public final void setAddress(@Nullable Integer num) {
            this.address = num;
        }
    }

    @PostMapping({"/relay/doControl"})
    @NotNull
    public String relayControl(@RequestBody @Validated @NotNull RTUDO rtudo) {
        Intrinsics.checkNotNullParameter(rtudo, "rtudo");
        if (Intrinsics.areEqual((Object) rtudo.getF(), (Object) true)) {
            Integer address = rtudo.getAddress();
            Intrinsics.checkNotNull(address);
            int intValue = address.intValue();
            Integer doNumber = rtudo.getDoNumber();
            Intrinsics.checkNotNull(doNumber);
            String openCmd = SerialPortRelayAPI.calcDOOpenCmd(intValue, doNumber.intValue());
            NetSerialPortServer.write(rtudo.getChannelName(), openCmd);
            Intrinsics.checkNotNull(openCmd);
            return openCmd;
        }
        Integer address2 = rtudo.getAddress();
        Intrinsics.checkNotNull(address2);
        int intValue2 = address2.intValue();
        Integer doNumber2 = rtudo.getDoNumber();
        Intrinsics.checkNotNull(doNumber2);
        String closeCmd = SerialPortRelayAPI.calcDOCloseCmd(intValue2, doNumber2.intValue());
        NetSerialPortServer.write(rtudo.getChannelName(), closeCmd);
        Intrinsics.checkNotNull(closeCmd);
        return closeCmd;
    }

    @GetMapping({"/relay/doCacheSearch"})
    public boolean doCacheSearch(@RequestParam @jakarta.validation.constraints.NotNull int address, @RequestParam(required = false, defaultValue = "8") int doNumber) {
        return getRelayService().getDOStatus(address, doNumber);
    }

    @GetMapping({"/relay/doSearch"})
    public boolean relayDoSearch(@RequestParam @NotBlank @NotNull Integer address, @RequestParam @NotBlank @NotNull String channelName, @RequestParam(required = false, defaultValue = "8") int doNumber) {
        Intrinsics.checkNotNullParameter(address, "address");
        Intrinsics.checkNotNullParameter(channelName, "channelName");
        String doSearchCmd = SerialPortRelayAPI.calcDOSearchCmd(address.intValue(), doNumber);
        NetSerialPortServer.write(channelName, doSearchCmd);
        return true;
    }

    @GetMapping({"/serialPortServer/sendCmd"})
    public boolean serialPortServer(@RequestParam @NotBlank @NotNull String data, @RequestParam @NotBlank @NotNull String channelName) {
        Intrinsics.checkNotNullParameter(data, "data");
        Intrinsics.checkNotNullParameter(channelName, "channelName");
        NetSerialPortServer.write(channelName, data);
        return true;
    }

    @GetMapping({"/queue/enqueue"})
    public boolean enQueue(@RequestParam @NotNull String v) {
        Intrinsics.checkNotNullParameter(v, "v");
        return getMyQueue().enqueue(v);
    }

    @GetMapping({"/queue/dequeue"})
    public boolean deQueue(@RequestParam @NotNull String v) {
        Intrinsics.checkNotNullParameter(v, "v");
        return getMyQueue().remove(v);
    }

    @GetMapping({"/queue/position"})
    public int queuePosition(@RequestParam @NotNull String v) {
        Intrinsics.checkNotNullParameter(v, "v");
        return getMyQueue().position(v);
    }
}
