package com.shdatalink.cautious.doodle.lidar.edge.boot.netty;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.shdatalink.cautious.doodle.lidar.edge.boot.event.MyEvent;
import com.shdatalink.cautious.doodle.lidar.edge.event.PTZAngleValueEvent;
import com.shdatalink.cautious.doodle.lidar.edge.event.RelayDOValueEvent;
import com.shdatalink.cautious.doodle.lidar.edge.hardware.lidar.SerialPortEthernetConverter;
import com.shdatalink.cautious.doodle.lidar.edge.hardware.lidar.SerialPortEthernetConverterService;
import com.shdatalink.cautious.doodle.lidar.edge.hardware.ptz.PTZ;
import com.shdatalink.cautious.doodle.lidar.edge.hardware.ptz.SerialPortPTZAPI;
import com.shdatalink.cautious.doodle.lidar.edge.hardware.relay.SerialPortRelayAPI;
import com.shdatalink.common.util.SpringContextHolder;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.lang.invoke.SerializedLambda;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListSet;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.apache.tomcat.util.buf.HexUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: dg-lidar-collect-edge.jar:BOOT-INF/classes/com/shdatalink/cautious/doodle/lidar/edge/boot/netty/NetSerialPortServerHandler.class */
public class NetSerialPortServerHandler extends SimpleChannelInboundHandler<byte[]> {
    private static final Logger logger = LoggerFactory.getLogger((Class<?>) NetSerialPortServerHandler.class);
    public static Map<Channel, String> authChannelMap = new ConcurrentHashMap();
    private static Set<Channel> unAuthChannelSet = new ConcurrentSkipListSet();
    private static ScheduledExecutorService threadPool = Executors.newScheduledThreadPool(5);

    private static /* synthetic */ Object $deserializeLambda$(SerializedLambda lambda) {
        String implMethodName = lambda.getImplMethodName();
        boolean z = -1;
        switch (implMethodName.hashCode()) {
            case 397166713:
                if (implMethodName.equals("getEnable")) {
                    z = false;
                    break;
                }
                break;
        }
        switch (z) {
            case false:
                if (lambda.getImplMethodKind() == 5 && lambda.getFunctionalInterfaceClass().equals("com/baomidou/mybatisplus/core/toolkit/support/SFunction") && lambda.getFunctionalInterfaceMethodName().equals("apply") && lambda.getFunctionalInterfaceMethodSignature().equals("(Ljava/lang/Object;)Ljava/lang/Object;") && lambda.getImplClass().equals("com/shdatalink/cautious/doodle/lidar/edge/hardware/lidar/SerialPortEthernetConverter") && lambda.getImplMethodSignature().equals("()Ljava/lang/Boolean;")) {
                    return (v0) -> {
                        return v0.getEnable();
                    };
                }
                break;
        }
        throw new IllegalArgumentException("Invalid lambda deserialization");
    }

    @Override // io.netty.channel.ChannelInboundHandlerAdapter, io.netty.channel.ChannelInboundHandler
    public void channelInactive(ChannelHandlerContext ctx) {
        Channel channel = ctx.channel();
        authChannelMap.remove(channel);
        logger.info(channel.remoteAddress() + "下线了.");
    }

    @Override // io.netty.channel.ChannelInboundHandlerAdapter, io.netty.channel.ChannelInboundHandler
    public void channelActive(ChannelHandlerContext ctx) {
        Channel channel = ctx.channel();
        logger.info(channel.remoteAddress() + "上线了.");
        unAuthChannelSet.add(channel);
        threadPool.schedule(() -> {
            if (unAuthChannelSet.contains(channel)) {
                unAuthChannelSet.remove(channel);
                channel.close();
                logger.info("超过5秒还没有发送注册包信息:" + channel.remoteAddress() + "连接被关闭.");
            }
        }, 5L, TimeUnit.SECONDS);
    }

    /* JADX WARN: Multi-variable type inference failed */
    @Override // io.netty.channel.SimpleChannelInboundHandler
    public void channelRead0(ChannelHandlerContext ctx, byte[] msg) {
        Channel channel = ctx.channel();
        String hexString = HexUtils.toHexString(msg);
        if (msg.length == 6) {
            String t = hexString.replaceAll("(.{2})", " $1").substring(1);
            String doStatus = SerialPortRelayAPI.resolveDOStatus(t);
            if (doStatus != null) {
                String[] doStatusArray = doStatus.split("-");
                int slaveAddress = Integer.parseInt(doStatusArray[0]);
                String binaryStr = doStatusArray[1];
                if (logger.isInfoEnabled()) {
                    logger.info("继电器从机地址:{}-状态位:{}", Integer.valueOf(slaveAddress), binaryStr);
                }
                SpringContextHolder.getApplicationContext().publishEvent(new MyEvent(new RelayDOValueEvent(slaveAddress, binaryStr)));
                return;
            }
            return;
        }
        if (msg.length % 7 == 0 && hexString.startsWith("ff")) {
            String[] strings = hexString.split("(?=ff)");
            for (String s : strings) {
                String t1 = s.replaceAll("(.{2})", " $1").substring(1);
                q(t1);
            }
            return;
        }
        if (msg.length == 8) {
            logger.info("串口服务器{},注册包内容:{}", channel.remoteAddress(), hexString);
            if (authChannelMap.get(channel) == null) {
                List<SerialPortEthernetConverter> serialPortEthernetConverterList = ((SerialPortEthernetConverterService) SpringContextHolder.getApplicationContext().getBean(SerialPortEthernetConverterService.class)).list((Wrapper) new LambdaQueryWrapper().eq((v0) -> {
                    return v0.getEnable();
                }, 1));
                serialPortEthernetConverterList.stream().filter(t2 -> {
                    return t2.getConnectSecret().equals(hexString);
                }).findFirst().map(t3 -> {
                    logger.info("串口服务器连接认证成功:{}", channel.remoteAddress());
                    authChannelMap.put(channel, t3.getConnectSecret());
                    return Boolean.valueOf(unAuthChannelSet.remove(channel));
                }).orElseGet(() -> {
                    logger.info("数据库未找到和设备匹配的密钥,串口服务器连接认证失败:{},连接已经断开.", channel.remoteAddress());
                    unAuthChannelSet.remove(channel);
                    channel.close();
                    return true;
                });
                return;
            }
            return;
        }
        logger.error("串口服务器返回的数据无法解析:{}", hexString);
    }

    private void q(String t1) {
        PTZ ptz = SerialPortPTZAPI.resolvePTZAngle(t1);
        if (ptz != null) {
            if (logger.isInfoEnabled()) {
                logger.info("云台从机地址:{}-方向:{}-角度:{}", Integer.valueOf(ptz.getSlaveAddress()), ptz.getDirection(), Integer.valueOf(ptz.getRealAngle()));
            }
            SpringContextHolder.getApplicationContext().publishEvent(new MyEvent(new PTZAngleValueEvent(Integer.valueOf(ptz.getSlaveAddress()), Integer.valueOf(ptz.getRealAngle()), ptz.getDirection())));
        }
    }

    @Override // io.netty.channel.ChannelInboundHandlerAdapter, io.netty.channel.ChannelHandlerAdapter, io.netty.channel.ChannelHandler, io.netty.channel.ChannelInboundHandler
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        StringWriter sw = new StringWriter();
        cause.printStackTrace(new PrintWriter(sw));
        logger.error("出错了:" + sw.getBuffer().toString());
        ctx.close();
    }

    @Override // io.netty.channel.ChannelHandlerAdapter, io.netty.channel.ChannelHandler
    public void handlerAdded(ChannelHandlerContext ctx) {
    }

    @Override // io.netty.channel.ChannelHandlerAdapter, io.netty.channel.ChannelHandler
    public void handlerRemoved(ChannelHandlerContext ctx) {
        Channel channel = ctx.channel();
        logger.info(channel.remoteAddress() + "离开了");
    }
}
