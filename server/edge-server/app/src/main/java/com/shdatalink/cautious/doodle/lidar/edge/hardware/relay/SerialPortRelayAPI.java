package com.shdatalink.cautious.doodle.lidar.edge.hardware.relay;

import com.shdatalink.cautious.doodle.lidar.edge.util.ByteArrayUtil;
import com.shdatalink.cautious.doodle.lidar.edge.util.CRC16Util;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/* loaded from: dg-lidar-collect-edge.jar:BOOT-INF/classes/com/shdatalink/cautious/doodle/lidar/edge/hardware/relay/SerialPortRelayAPI.class */
public class SerialPortRelayAPI {
    private static final Logger logger = LoggerFactory.getLogger((Class<?>) SerialPortRelayAPI.class);
    private static final String DI_REPORT_FIRST_BYTE_HEX = "40";
    private static final String DI_REPORT_SECOND_BYTE_HEX = "57";

    public static String resolveDOStatus(String msg) {
        try {
            if (StringUtils.isBlank(msg)) {
                logger.error("IO控制器DO数据消息体为空");
                return null;
            }
            String[] msgArr = StringUtils.split(msg, " ");
            if (msgArr.length != 6) {
                logger.error("IO控制器DO485数据不合法，字节数量不等于6");
                return null;
            }
            int i = Integer.parseInt(msgArr[3], 16);
            Integer slaveAddress = Integer.valueOf(Integer.parseInt(msgArr[0], 16));
            String binaryStr = StringUtils.reverse(StringUtils.leftPad(Integer.toBinaryString(i), 8, '0'));
            if (logger.isDebugEnabled()) {
                logger.info("IO从机地址{}-DO状态:{}", slaveAddress, binaryStr);
            }
            return slaveAddress + "-" + binaryStr;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("IO控制器DO解析数据异常", (Throwable) e);
            return null;
        }
    }

    public static String resolveDIStatus(String msg) {
        try {
            if (StringUtils.isBlank(msg)) {
                logger.error("IO控制器DI485数据消息体为空");
                return null;
            }
            String[] msgArr = StringUtils.split(msg, " ");
            if (msgArr.length != 6) {
                logger.error("IO控制器DI485数据不合法，字节数量不等于6");
                return null;
            }
            if (!DI_REPORT_FIRST_BYTE_HEX.equals(msgArr[0])) {
                logger.error("IO控制器485数据不合法，第一个字节十六进制字符不等于40");
                return null;
            }
            if (!DI_REPORT_SECOND_BYTE_HEX.equals(msgArr[1])) {
                logger.error("地磅485数据不合法，第二个字节十六进制字符不等于57");
                return null;
            }
            int i = Integer.parseInt(msgArr[3], 16);
            Integer slaveAddress = Integer.valueOf(Integer.parseInt(msgArr[2], 16));
            String binaryStr = StringUtils.reverse(StringUtils.leftPad(Integer.toBinaryString(i), 8, '0'));
            if (logger.isDebugEnabled()) {
                logger.info("IO从机地址{}-DI状态:{}", slaveAddress, binaryStr);
            }
            return slaveAddress + "-" + binaryStr;
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("IO控制器DI解析数据异常", (Throwable) e);
            return null;
        }
    }

    public static String calcDOSearchCmd(int slaveAddress, int doNumber) {
        String address = StringUtils.leftPad(Integer.toHexString(slaveAddress), 2, '0');
        String searchDoNumber = StringUtils.leftPad(Integer.toHexString(doNumber), 4, '0');
        String str = address + "010000" + searchDoNumber;
        byte[] bytes = ByteArrayUtil.hexStringToByteArray(str);
        String crc = Integer.toHexString(CRC16Util.calcCrc16Modbus(bytes));
        return str + crc;
    }

    private static String calcDoControlCmd(int slaveAddress, int doIndex, boolean f) {
        String address = StringUtils.leftPad(Integer.toHexString(slaveAddress), 2, '0');
        String doX = StringUtils.leftPad(String.valueOf(doIndex - 1), 2, '0');
        String open = f ? "ff" : "00";
        String cmd = address + "0500" + doX + open + "00";
        byte[] bytes = ByteArrayUtil.hexStringToByteArray(cmd);
        int num = CRC16Util.calcCrc16Modbus(bytes);
        String crc = Integer.toHexString(num);
        return cmd + crc;
    }

    public static String calcDOOpenCmd(int slaveAddress, int doIndex) {
        return calcDoControlCmd(slaveAddress, doIndex, true);
    }

    public static String calcDOCloseCmd(int slaveAddress, int doIndex) {
        return calcDoControlCmd(slaveAddress, doIndex, false);
    }
}
