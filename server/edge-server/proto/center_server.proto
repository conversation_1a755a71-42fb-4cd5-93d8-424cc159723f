syntax = "proto3";

package center_server;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

// 中央服务器服务定义
service CenterServer {
    // 边缘服务器管理
    rpc RegisterEdgeServer(EdgeServerInfo) returns (RegisterResponse);
    rpc HeartBeat(HeartBeatRequest) returns (HeartBeatResponse);
    rpc GetEdgeServerList(google.protobuf.Empty) returns (EdgeServerListResponse);
    rpc GetEdgeServerStatus(EdgeServerRequest) returns (EdgeServerStatusResponse);
    
    // 任务管理
    rpc CreateTask(CreateTaskRequest) returns (TaskResponse);
    rpc AssignTask(AssignTaskRequest) returns (TaskResponse);
    rpc UpdateTaskStatus(UpdateTaskStatusRequest) returns (TaskResponse);
    rpc GetTaskList(TaskListRequest) returns (TaskListResponse);
    rpc GetTaskDetails(TaskRequest) returns (TaskDetailsResponse);
    rpc CancelTask(TaskRequest) returns (TaskResponse);
    
    // 数据管理
    rpc RegisterDataUpload(DataUploadInfo) returns (DataResponse);
    rpc GetDataList(DataListRequest) returns (DataListResponse);
    rpc GetDataStatistics(DataStatsRequest) returns (DataStatsResponse);
    
    // 系统监控
    rpc GetSystemOverview(google.protobuf.Empty) returns (SystemOverviewResponse);
    rpc GetMetrics(MetricsRequest) returns (MetricsResponse);
    
    // 硬件控制
    rpc SendHardwareCommand(HardwareControlRequest) returns (HardwareControlResponse);
    rpc GetHardwareStatus(EdgeServerRequest) returns (HardwareStatusResponse);
}

// 边缘服务器信息
message EdgeServerInfo {
    string edge_id = 1;
    string name = 2;
    string ip_address = 3;
    int32 grpc_port = 4;
    repeated string capabilities = 5;  // 支持的功能列表
    map<string, string> metadata = 6;  // 额外信息
}

// 注册响应
message RegisterResponse {
    bool success = 1;
    string message = 2;
    string session_id = 3;  // 会话ID，用于后续通信
}

// 心跳请求
message HeartBeatRequest {
    string edge_id = 1;
    string session_id = 2;
    SystemStatus status = 3;
    repeated TaskProgress task_progress = 4;  // 任务进度更新
}

// 系统状态
message SystemStatus {
    float cpu_usage = 1;
    float memory_usage = 2;
    float disk_usage = 3;
    bool is_healthy = 4;
    map<string, ModuleStatus> modules = 5;
    HardwareStatus hardware_status = 6;  // 硬件状态信息
}

// 模块状态
message ModuleStatus {
    string name = 1;
    bool is_active = 2;
    string status = 3;
    map<string, string> details = 4;
}

// 任务进度
message TaskProgress {
    string task_id = 1;
    string status = 2;  // pending, running, completed, failed
    float progress = 3;  // 0-100
    string message = 4;
}

// 心跳响应
message HeartBeatResponse {
    bool success = 1;
    google.protobuf.Timestamp server_time = 2;
    repeated Command commands = 3;  // 待执行的命令
}

// 命令
message Command {
    string command_id = 1;
    string type = 2;  // task_assign, config_update, emergency_stop, hardware_control等
    map<string, string> parameters = 3;
    HardwareCommand hardware_command = 4;  // 硬件控制命令（可选）
}

// 边缘服务器请求
message EdgeServerRequest {
    string edge_id = 1;
}

// 边缘服务器列表响应
message EdgeServerListResponse {
    repeated EdgeServerDetail servers = 1;
}

// 边缘服务器详情
message EdgeServerDetail {
    EdgeServerInfo info = 1;
    bool is_online = 2;
    google.protobuf.Timestamp last_seen = 3;
    SystemStatus current_status = 4;
    int32 active_tasks = 5;
}

// 边缘服务器状态响应
message EdgeServerStatusResponse {
    EdgeServerDetail server = 1;
    repeated TaskInfo running_tasks = 2;
    repeated DataUploadInfo recent_uploads = 3;
}

// 创建任务请求
message CreateTaskRequest {
    string task_type = 1;  // lidar_scan, camera_record等
    map<string, string> parameters = 2;
    TaskSchedule schedule = 3;
    repeated string target_edges = 4;  // 目标边缘服务器列表，空则自动分配
}

// 任务调度信息
message TaskSchedule {
    google.protobuf.Timestamp start_time = 1;
    int32 duration_seconds = 2;
    string priority = 3;  // high, medium, low
    int32 retry_count = 4;
}

// 分配任务请求
message AssignTaskRequest {
    string task_id = 1;
    string edge_id = 2;
}

// 任务响应
message TaskResponse {
    bool success = 1;
    string message = 2;
    string task_id = 3;
}

// 更新任务状态请求
message UpdateTaskStatusRequest {
    string task_id = 1;
    string edge_id = 2;
    TaskProgress progress = 3;
    map<string, string> result_data = 4;  // 任务结果数据
}

// 任务列表请求
message TaskListRequest {
    string edge_id = 1;  // 可选，过滤特定边缘服务器的任务
    string status = 2;   // 可选，过滤特定状态的任务
    int32 limit = 3;     // 返回数量限制
}

// 任务信息
message TaskInfo {
    string task_id = 1;
    string task_type = 2;
    string status = 3;
    string edge_id = 4;
    google.protobuf.Timestamp created_at = 5;
    google.protobuf.Timestamp updated_at = 6;
    float progress = 7;
}

// 任务列表响应
message TaskListResponse {
    repeated TaskInfo tasks = 1;
    int32 total_count = 2;
}

// 任务请求
message TaskRequest {
    string task_id = 1;
}

// 任务详情响应
message TaskDetailsResponse {
    TaskInfo info = 1;
    map<string, string> parameters = 2;
    TaskSchedule schedule = 3;
    repeated TaskProgress history = 4;  // 进度历史
    map<string, string> result_data = 5;
}

// 数据上传信息
message DataUploadInfo {
    string upload_id = 1;
    string edge_id = 2;
    string task_id = 3;
    string data_type = 4;  // lidar_pcd, camera_video等
    string file_path = 5;  // MinIO中的路径
    int64 file_size = 6;
    google.protobuf.Timestamp upload_time = 7;
    map<string, string> metadata = 8;
}

// 数据响应
message DataResponse {
    bool success = 1;
    string message = 2;
}

// 数据列表请求
message DataListRequest {
    string edge_id = 1;
    string task_id = 2;
    string data_type = 3;
    google.protobuf.Timestamp start_time = 4;
    google.protobuf.Timestamp end_time = 5;
    int32 limit = 6;
}

// 数据列表响应
message DataListResponse {
    repeated DataUploadInfo data = 1;
    int32 total_count = 2;
    int64 total_size = 3;
}

// 数据统计请求
message DataStatsRequest {
    string edge_id = 1;
    google.protobuf.Timestamp start_time = 2;
    google.protobuf.Timestamp end_time = 3;
}

// 数据统计响应
message DataStatsResponse {
    map<string, int64> data_count_by_type = 1;  // 各类型数据数量
    map<string, int64> data_size_by_type = 2;   // 各类型数据大小
    int64 total_count = 3;
    int64 total_size = 4;
}

// 系统概览响应
message SystemOverviewResponse {
    int32 total_edge_servers = 1;
    int32 online_edge_servers = 2;
    int32 total_tasks = 3;
    int32 running_tasks = 4;
    int32 completed_tasks = 5;
    int32 failed_tasks = 6;
    int64 total_data_size = 7;
    map<string, float> avg_metrics = 8;  // 平均CPU、内存等指标
}

// 指标请求
message MetricsRequest {
    repeated string edge_ids = 1;
    repeated string metric_names = 2;  // cpu, memory, disk, network等
    int32 time_range_minutes = 3;
}

// 指标响应
message MetricsResponse {
    map<string, EdgeMetrics> edge_metrics = 1;
}

// 边缘服务器指标
message EdgeMetrics {
    string edge_id = 1;
    map<string, MetricData> metrics = 2;
}

// 指标数据
message MetricData {
    repeated MetricPoint points = 1;
}

// 指标点
message MetricPoint {
    google.protobuf.Timestamp timestamp = 1;
    float value = 2;
}

// 硬件状态信息
message HardwareStatus {
    map<string, string> power_status = 1;  // 设备电源状态 (on/off)
    PTZStatus ptz_status = 2;  // 云台状态
    map<string, DeviceInfo> devices = 3;  // 设备详细信息
}

// 云台状态
message PTZStatus {
    float horizontal = 1;  // 水平角度
    float vertical = 2;    // 垂直角度
    float zoom = 3;        // 缩放级别
    bool is_moving = 4;    // 是否正在移动
    string status = 5;     // 状态描述
}

// 设备信息
message DeviceInfo {
    string device_type = 1;  // lidar, camera, ptz等
    string status = 2;       // online, offline, error等
    map<string, string> properties = 3;  // 设备特定属性
}

// 硬件控制命令
message HardwareCommand {
    string command_type = 1;  // power_on, power_off, ptz_goto等
    string device = 2;        // 目标设备
    map<string, string> parameters = 3;  // 命令参数
}

// 硬件控制请求
message HardwareControlRequest {
    string edge_id = 1;  // 目标边缘服务器
    HardwareCommand command = 2;  // 硬件命令
}

// 硬件控制响应
message HardwareControlResponse {
    bool success = 1;
    string message = 2;
    string command_id = 3;  // 命令ID，用于跟踪
}

// 硬件状态响应
message HardwareStatusResponse {
    string edge_id = 1;
    HardwareStatus hardware_status = 2;
    google.protobuf.Timestamp last_updated = 3;
}