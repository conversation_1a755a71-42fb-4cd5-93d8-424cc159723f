# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: edge_server.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11\x65\x64ge_server.proto\x12\x0b\x65\x64ge_server\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\"\x98\x01\n\x0b\x41uthRequest\x12\r\n\x05token\x18\x01 \x01(\t\x12\x0f\n\x07\x65\x64ge_id\x18\x02 \x01(\t\x12\x38\n\x08metadata\x18\x03 \x03(\x0b\x32&.edge_server.AuthRequest.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"X\n\x0c\x41uthResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x12\n\nsession_id\x18\x02 \x01(\t\x12\x0f\n\x07message\x18\x03 \x01(\t\x12\x12\n\nexpires_at\x18\x04 \x01(\x03\"\xc3\x01\n\x0bTaskRequest\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0e\n\x06target\x18\x02 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x03 \x01(\t\x12\x34\n\x06params\x18\x04 \x03(\x0b\x32$.edge_server.TaskRequest.ParamsEntry\x12\x10\n\x08priority\x18\x05 \x01(\x05\x12\x0f\n\x07timeout\x18\x06 \x01(\x05\x1a-\n\x0bParamsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"A\n\x0cTaskResponse\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x0f\n\x07success\x18\x02 \x01(\x08\x12\x0f\n\x07message\x18\x03 \x01(\t\"$\n\x11TaskStatusRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\"\xb1\x02\n\x12TaskStatusResponse\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x10\n\x08progress\x18\x03 \x01(\x02\x12\r\n\x05\x65rror\x18\x04 \x01(\t\x12.\n\ncreated_at\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x30\n\x0c\x63ompleted_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x44\n\x0bresult_data\x18\x07 \x03(\x0b\x32/.edge_server.TaskStatusResponse.ResultDataEntry\x1a\x31\n\x0fResultDataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"$\n\x11\x43\x61ncelTaskRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\"8\n\x10ListTasksRequest\x12\x15\n\rstatus_filter\x18\x01 \x01(\t\x12\r\n\x05limit\x18\x02 \x01(\x05\"9\n\x11ListTasksResponse\x12$\n\x05tasks\x18\x01 \x03(\x0b\x32\x15.edge_server.TaskInfo\"\x9b\x01\n\x08TaskInfo\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\x12\x0e\n\x06target\x18\x03 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x04 \x01(\t\x12\x0e\n\x06status\x18\x05 \x01(\t\x12\x10\n\x08progress\x18\x06 \x01(\x02\x12.\n\ncreated_at\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\x8e\x02\n\x14SystemStatusResponse\x12\x0f\n\x07healthy\x18\x01 \x01(\x08\x12?\n\x07modules\x18\x02 \x03(\x0b\x32..edge_server.SystemStatusResponse.ModulesEntry\x12,\n\x08task_bus\x18\x03 \x01(\x0b\x32\x1a.edge_server.TaskBusStatus\x12+\n\x07metrics\x18\x04 \x01(\x0b\x32\x1a.edge_server.SystemMetrics\x1aI\n\x0cModulesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12(\n\x05value\x18\x02 \x01(\x0b\x32\x19.edge_server.ModuleStatus:\x02\x38\x01\"S\n\x0cModuleStatus\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0f\n\x07healthy\x18\x03 \x01(\x08\x12\x14\n\x0c\x63urrent_task\x18\x04 \x01(\t\"\x81\x01\n\rTaskBusStatus\x12\x15\n\rpending_tasks\x18\x01 \x01(\x05\x12\x15\n\rrunning_tasks\x18\x02 \x01(\x05\x12\x17\n\x0f\x63ompleted_tasks\x18\x03 \x01(\x05\x12\x14\n\x0csuccess_rate\x18\x04 \x01(\x02\x12\x13\n\x0bqueue_usage\x18\x05 \x01(\x02\"\x80\x01\n\rSystemMetrics\x12\x13\n\x0b\x63pu_percent\x18\x01 \x01(\x02\x12\x16\n\x0ememory_percent\x18\x02 \x01(\x02\x12\x14\n\x0c\x64isk_percent\x18\x03 \x01(\x02\x12\x16\n\x0ememory_used_mb\x18\x04 \x01(\x03\x12\x14\n\x0c\x64isk_free_gb\x18\x05 \x01(\x03\">\n\x12ModuleListResponse\x12(\n\x07modules\x18\x01 \x03(\x0b\x32\x17.edge_server.ModuleInfo\"\xa5\x01\n\nModuleInfo\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x0f\n\x07\x61\x63tions\x18\x03 \x03(\t\x12\x37\n\x08\x66\x65\x61tures\x18\x04 \x03(\x0b\x32%.edge_server.ModuleInfo.FeaturesEntry\x1a/\n\rFeaturesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"D\n\x13PowerControlRequest\x12\x0e\n\x06\x64\x65vice\x18\x01 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\x12\r\n\x05\x64\x65lay\x18\x03 \x01(\x05\"f\n\x11PTZControlRequest\x12\x12\n\nhorizontal\x18\x01 \x01(\x02\x12\x10\n\x08vertical\x18\x02 \x01(\x02\x12\x0c\n\x04zoom\x18\x03 \x01(\x02\x12\r\n\x05speed\x18\x04 \x01(\x05\x12\x0e\n\x06\x61\x63tion\x18\x05 \x01(\t\"\x96\x01\n\x0f\x43ontrolResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x34\n\x04\x64\x61ta\x18\x03 \x03(\x0b\x32&.edge_server.ControlResponse.DataEntry\x1a+\n\tDataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xa1\x02\n\x10LidarScanRequest\x12\x11\n\tscan_type\x18\x01 \x01(\t\x12\x10\n\x08\x64uration\x18\x02 \x01(\x05\x12\'\n\x08position\x18\x03 \x01(\x0b\x32\x15.edge_server.Position\x12(\n\tscan_area\x18\x04 \x01(\x0b\x32\x15.edge_server.ScanArea\x12\x12\n\nresolution\x18\x05 \x01(\x02\x12\x11\n\ttask_name\x18\x06 \x01(\t\x12=\n\x08metadata\x18\x07 \x03(\x0b\x32+.edge_server.LidarScanRequest.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"0\n\x08Position\x12\x12\n\nhorizontal\x18\x01 \x01(\x02\x12\x10\n\x08vertical\x18\x02 \x01(\x02\"J\n\x08ScanArea\x12\x0f\n\x07h_start\x18\x01 \x01(\x02\x12\r\n\x05h_end\x18\x02 \x01(\x02\x12\x0f\n\x07v_start\x18\x03 \x01(\x02\x12\r\n\x05v_end\x18\x04 \x01(\x02\"\xb7\x01\n\x13\x43\x61meraRecordRequest\x12\x0f\n\x07\x63hannel\x18\x01 \x01(\t\x12\x10\n\x08\x64uration\x18\x02 \x01(\x05\x12\'\n\x08position\x18\x03 \x01(\x0b\x32\x15.edge_server.Position\x12\x11\n\tpreset_id\x18\x04 \x01(\x05\x12\x11\n\ttask_name\x18\x05 \x01(\t\x12.\n\x07options\x18\x06 \x01(\x0b\x32\x1d.edge_server.RecordingOptions\"G\n\x10RecordingOptions\x12\x0f\n\x07quality\x18\x01 \x01(\t\x12\x12\n\nwith_audio\x18\x02 \x01(\x08\x12\x0e\n\x06\x66ormat\x18\x03 \x01(\t\"l\n\x0fSnapshotRequest\x12\x0f\n\x07\x63hannel\x18\x01 \x01(\t\x12\'\n\x08position\x18\x02 \x01(\x0b\x32\x15.edge_server.Position\x12\x0f\n\x07quality\x18\x03 \x01(\t\x12\x0e\n\x06\x66ormat\x18\x04 \x01(\t\"|\n\x0fListDataRequest\x12\x11\n\tdata_type\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x61te_filter\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\x12\x0e\n\x06offset\x18\x04 \x01(\x05\x12\x0f\n\x07sort_by\x18\x05 \x01(\t\x12\x11\n\tascending\x18\x06 \x01(\x08\"P\n\x10ListDataResponse\x12(\n\x05\x66iles\x18\x01 \x03(\x0b\x32\x19.edge_server.DataFileInfo\x12\x12\n\ntotal_size\x18\x02 \x01(\x03\"\xc5\x02\n\x0c\x44\x61taFileInfo\x12\x0f\n\x07\x66ile_id\x18\x01 \x01(\t\x12\x11\n\tfile_name\x18\x02 \x01(\t\x12\x11\n\tdata_type\x18\x03 \x01(\t\x12\x11\n\tfile_size\x18\x04 \x01(\x03\x12.\n\ncreated_at\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x15\n\rupload_status\x18\x06 \x01(\t\x12\x13\n\x0bobject_name\x18\x07 \x01(\t\x12\x11\n\tfile_path\x18\x08 \x01(\t\x12\x39\n\x08metadata\x18\t \x03(\x0b\x32\'.edge_server.DataFileInfo.MetadataEntry\x12\x10\n\x08\x63hecksum\x18\n \x01(\t\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"&\n\x13UploadStatusRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\"\xb2\x01\n\x14UploadStatusResponse\x12\x13\n\x0btotal_files\x18\x01 \x01(\x05\x12\x16\n\x0euploaded_files\x18\x02 \x01(\x05\x12\x14\n\x0c\x66\x61iled_files\x18\x03 \x01(\x05\x12\x13\n\x0btotal_bytes\x18\x04 \x01(\x03\x12\x16\n\x0euploaded_bytes\x18\x05 \x01(\x03\x12*\n\x05tasks\x18\x06 \x03(\x0b\x32\x1b.edge_server.UploadTaskInfo\"X\n\x0eUploadTaskInfo\x12\x11\n\tupload_id\x18\x01 \x01(\t\x12\x11\n\tfile_path\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x10\n\x08progress\x18\x04 \x01(\x02\"4\n\x11\x44\x65leteDataRequest\x12\x10\n\x08\x66ile_ids\x18\x01 \x03(\t\x12\r\n\x05\x66orce\x18\x02 \x01(\x08\"c\n\x12\x44\x65leteDataResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x15\n\rdeleted_count\x18\x02 \x01(\x05\x12\x14\n\x0c\x66\x61iled_files\x18\x03 \x03(\t\x12\x0f\n\x07message\x18\x04 \x01(\t\"%\n\x12\x44\x61taDetailsRequest\x12\x0f\n\x07\x66ile_id\x18\x01 \x01(\t\"\xcc\x01\n\x13\x44\x61taDetailsResponse\x12,\n\tfile_info\x18\x01 \x01(\x0b\x32\x19.edge_server.DataFileInfo\x12\x13\n\x0bpreview_url\x18\x02 \x01(\t\x12\x41\n\ttask_info\x18\x03 \x03(\x0b\x32..edge_server.DataDetailsResponse.TaskInfoEntry\x1a/\n\rTaskInfoEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"*\n\x13\x44\x65viceStatusRequest\x12\x13\n\x0b\x64\x65vice_type\x18\x01 \x01(\t\"\xcf\x01\n\x14\x44\x65viceStatusResponse\x12?\n\x07\x64\x65vices\x18\x01 \x03(\x0b\x32..edge_server.DeviceStatusResponse.DevicesEntry\x12-\n\ttimestamp\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x1aG\n\x0c\x44\x65vicesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12&\n\x05value\x18\x02 \x01(\x0b\x32\x17.edge_server.DeviceInfo:\x02\x38\x01\"\x8b\x02\n\nDeviceInfo\x12\x11\n\tdevice_id\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65vice_type\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x10\n\x08power_on\x18\x04 \x01(\x08\x12;\n\nproperties\x18\x05 \x03(\x0b\x32\'.edge_server.DeviceInfo.PropertiesEntry\x12\x14\n\x0c\x63urrent_task\x18\x06 \x01(\t\x12-\n\tlast_seen\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x1a\x31\n\x0fPropertiesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"?\n\x13ListDevicesResponse\x12(\n\x07\x64\x65vices\x18\x01 \x03(\x0b\x32\x17.edge_server.DeviceInfo\";\n\x11\x45ventSubscription\x12\x13\n\x0b\x65vent_types\x18\x01 \x03(\t\x12\x11\n\tclient_id\x18\x02 \x01(\t\"\xec\x01\n\x05\x45vent\x12\x10\n\x08\x65vent_id\x18\x01 \x01(\t\x12\x12\n\nevent_type\x18\x02 \x01(\t\x12-\n\ttimestamp\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0e\n\x06source\x18\x04 \x01(\t\x12\x10\n\x08severity\x18\x05 \x01(\t\x12*\n\x04\x64\x61ta\x18\x06 \x03(\x0b\x32\x1c.edge_server.Event.DataEntry\x12\x13\n\x0b\x64\x65scription\x18\x07 \x01(\t\x1a+\n\tDataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x32\x9b\x0c\n\nEdgeServer\x12\x43\n\x0c\x41uthenticate\x12\x18.edge_server.AuthRequest\x1a\x19.edge_server.AuthResponse\x12L\n\x0fGetSystemStatus\x12\x16.google.protobuf.Empty\x1a!.edge_server.SystemStatusResponse\x12H\n\rGetModuleList\x12\x16.google.protobuf.Empty\x1a\x1f.edge_server.ModuleListResponse\x12\x41\n\nSubmitTask\x12\x18.edge_server.TaskRequest\x1a\x19.edge_server.TaskResponse\x12P\n\rGetTaskStatus\x12\x1e.edge_server.TaskStatusRequest\x1a\x1f.edge_server.TaskStatusResponse\x12G\n\nCancelTask\x12\x1e.edge_server.CancelTaskRequest\x1a\x19.edge_server.TaskResponse\x12J\n\tListTasks\x12\x1d.edge_server.ListTasksRequest\x1a\x1e.edge_server.ListTasksResponse\x12N\n\x0c\x43ontrolPower\x12 .edge_server.PowerControlRequest\x1a\x1c.edge_server.ControlResponse\x12J\n\nControlPTZ\x12\x1e.edge_server.PTZControlRequest\x1a\x1c.edge_server.ControlResponse\x12I\n\x11\x45mergencyShutdown\x12\x16.google.protobuf.Empty\x1a\x1c.edge_server.ControlResponse\x12J\n\x0eStartLidarScan\x12\x1d.edge_server.LidarScanRequest\x1a\x19.edge_server.TaskResponse\x12S\n\x14StartCameraRecording\x12 .edge_server.CameraRecordRequest\x1a\x19.edge_server.TaskResponse\x12G\n\x0cTakeSnapshot\x12\x1c.edge_server.SnapshotRequest\x1a\x19.edge_server.TaskResponse\x12M\n\x0eListStoredData\x12\x1c.edge_server.ListDataRequest\x1a\x1d.edge_server.ListDataResponse\x12V\n\x0fGetUploadStatus\x12 .edge_server.UploadStatusRequest\x1a!.edge_server.UploadStatusResponse\x12M\n\nDeleteData\x12\x1e.edge_server.DeleteDataRequest\x1a\x1f.edge_server.DeleteDataResponse\x12S\n\x0eGetDataDetails\x12\x1f.edge_server.DataDetailsRequest\x1a .edge_server.DataDetailsResponse\x12V\n\x0fGetDeviceStatus\x12 .edge_server.DeviceStatusRequest\x1a!.edge_server.DeviceStatusResponse\x12G\n\x0bListDevices\x12\x16.google.protobuf.Empty\x1a .edge_server.ListDevicesResponse\x12I\n\x0fSubscribeEvents\x12\x1e.edge_server.EventSubscription\x1a\x12.edge_server.Event(\x01\x30\x01\x62\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'edge_server_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_AUTHREQUEST_METADATAENTRY']._options = None
  _globals['_AUTHREQUEST_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_TASKREQUEST_PARAMSENTRY']._options = None
  _globals['_TASKREQUEST_PARAMSENTRY']._serialized_options = b'8\001'
  _globals['_TASKSTATUSRESPONSE_RESULTDATAENTRY']._options = None
  _globals['_TASKSTATUSRESPONSE_RESULTDATAENTRY']._serialized_options = b'8\001'
  _globals['_SYSTEMSTATUSRESPONSE_MODULESENTRY']._options = None
  _globals['_SYSTEMSTATUSRESPONSE_MODULESENTRY']._serialized_options = b'8\001'
  _globals['_MODULEINFO_FEATURESENTRY']._options = None
  _globals['_MODULEINFO_FEATURESENTRY']._serialized_options = b'8\001'
  _globals['_CONTROLRESPONSE_DATAENTRY']._options = None
  _globals['_CONTROLRESPONSE_DATAENTRY']._serialized_options = b'8\001'
  _globals['_LIDARSCANREQUEST_METADATAENTRY']._options = None
  _globals['_LIDARSCANREQUEST_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_DATAFILEINFO_METADATAENTRY']._options = None
  _globals['_DATAFILEINFO_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_DATADETAILSRESPONSE_TASKINFOENTRY']._options = None
  _globals['_DATADETAILSRESPONSE_TASKINFOENTRY']._serialized_options = b'8\001'
  _globals['_DEVICESTATUSRESPONSE_DEVICESENTRY']._options = None
  _globals['_DEVICESTATUSRESPONSE_DEVICESENTRY']._serialized_options = b'8\001'
  _globals['_DEVICEINFO_PROPERTIESENTRY']._options = None
  _globals['_DEVICEINFO_PROPERTIESENTRY']._serialized_options = b'8\001'
  _globals['_EVENT_DATAENTRY']._options = None
  _globals['_EVENT_DATAENTRY']._serialized_options = b'8\001'
  _globals['_AUTHREQUEST']._serialized_start=97
  _globals['_AUTHREQUEST']._serialized_end=249
  _globals['_AUTHREQUEST_METADATAENTRY']._serialized_start=202
  _globals['_AUTHREQUEST_METADATAENTRY']._serialized_end=249
  _globals['_AUTHRESPONSE']._serialized_start=251
  _globals['_AUTHRESPONSE']._serialized_end=339
  _globals['_TASKREQUEST']._serialized_start=342
  _globals['_TASKREQUEST']._serialized_end=537
  _globals['_TASKREQUEST_PARAMSENTRY']._serialized_start=492
  _globals['_TASKREQUEST_PARAMSENTRY']._serialized_end=537
  _globals['_TASKRESPONSE']._serialized_start=539
  _globals['_TASKRESPONSE']._serialized_end=604
  _globals['_TASKSTATUSREQUEST']._serialized_start=606
  _globals['_TASKSTATUSREQUEST']._serialized_end=642
  _globals['_TASKSTATUSRESPONSE']._serialized_start=645
  _globals['_TASKSTATUSRESPONSE']._serialized_end=950
  _globals['_TASKSTATUSRESPONSE_RESULTDATAENTRY']._serialized_start=901
  _globals['_TASKSTATUSRESPONSE_RESULTDATAENTRY']._serialized_end=950
  _globals['_CANCELTASKREQUEST']._serialized_start=952
  _globals['_CANCELTASKREQUEST']._serialized_end=988
  _globals['_LISTTASKSREQUEST']._serialized_start=990
  _globals['_LISTTASKSREQUEST']._serialized_end=1046
  _globals['_LISTTASKSRESPONSE']._serialized_start=1048
  _globals['_LISTTASKSRESPONSE']._serialized_end=1105
  _globals['_TASKINFO']._serialized_start=1108
  _globals['_TASKINFO']._serialized_end=1263
  _globals['_SYSTEMSTATUSRESPONSE']._serialized_start=1266
  _globals['_SYSTEMSTATUSRESPONSE']._serialized_end=1536
  _globals['_SYSTEMSTATUSRESPONSE_MODULESENTRY']._serialized_start=1463
  _globals['_SYSTEMSTATUSRESPONSE_MODULESENTRY']._serialized_end=1536
  _globals['_MODULESTATUS']._serialized_start=1538
  _globals['_MODULESTATUS']._serialized_end=1621
  _globals['_TASKBUSSTATUS']._serialized_start=1624
  _globals['_TASKBUSSTATUS']._serialized_end=1753
  _globals['_SYSTEMMETRICS']._serialized_start=1756
  _globals['_SYSTEMMETRICS']._serialized_end=1884
  _globals['_MODULELISTRESPONSE']._serialized_start=1886
  _globals['_MODULELISTRESPONSE']._serialized_end=1948
  _globals['_MODULEINFO']._serialized_start=1951
  _globals['_MODULEINFO']._serialized_end=2116
  _globals['_MODULEINFO_FEATURESENTRY']._serialized_start=2069
  _globals['_MODULEINFO_FEATURESENTRY']._serialized_end=2116
  _globals['_POWERCONTROLREQUEST']._serialized_start=2118
  _globals['_POWERCONTROLREQUEST']._serialized_end=2186
  _globals['_PTZCONTROLREQUEST']._serialized_start=2188
  _globals['_PTZCONTROLREQUEST']._serialized_end=2290
  _globals['_CONTROLRESPONSE']._serialized_start=2293
  _globals['_CONTROLRESPONSE']._serialized_end=2443
  _globals['_CONTROLRESPONSE_DATAENTRY']._serialized_start=2400
  _globals['_CONTROLRESPONSE_DATAENTRY']._serialized_end=2443
  _globals['_LIDARSCANREQUEST']._serialized_start=2446
  _globals['_LIDARSCANREQUEST']._serialized_end=2735
  _globals['_LIDARSCANREQUEST_METADATAENTRY']._serialized_start=202
  _globals['_LIDARSCANREQUEST_METADATAENTRY']._serialized_end=249
  _globals['_POSITION']._serialized_start=2737
  _globals['_POSITION']._serialized_end=2785
  _globals['_SCANAREA']._serialized_start=2787
  _globals['_SCANAREA']._serialized_end=2861
  _globals['_CAMERARECORDREQUEST']._serialized_start=2864
  _globals['_CAMERARECORDREQUEST']._serialized_end=3047
  _globals['_RECORDINGOPTIONS']._serialized_start=3049
  _globals['_RECORDINGOPTIONS']._serialized_end=3120
  _globals['_SNAPSHOTREQUEST']._serialized_start=3122
  _globals['_SNAPSHOTREQUEST']._serialized_end=3230
  _globals['_LISTDATAREQUEST']._serialized_start=3232
  _globals['_LISTDATAREQUEST']._serialized_end=3356
  _globals['_LISTDATARESPONSE']._serialized_start=3358
  _globals['_LISTDATARESPONSE']._serialized_end=3438
  _globals['_DATAFILEINFO']._serialized_start=3441
  _globals['_DATAFILEINFO']._serialized_end=3766
  _globals['_DATAFILEINFO_METADATAENTRY']._serialized_start=202
  _globals['_DATAFILEINFO_METADATAENTRY']._serialized_end=249
  _globals['_UPLOADSTATUSREQUEST']._serialized_start=3768
  _globals['_UPLOADSTATUSREQUEST']._serialized_end=3806
  _globals['_UPLOADSTATUSRESPONSE']._serialized_start=3809
  _globals['_UPLOADSTATUSRESPONSE']._serialized_end=3987
  _globals['_UPLOADTASKINFO']._serialized_start=3989
  _globals['_UPLOADTASKINFO']._serialized_end=4077
  _globals['_DELETEDATAREQUEST']._serialized_start=4079
  _globals['_DELETEDATAREQUEST']._serialized_end=4131
  _globals['_DELETEDATARESPONSE']._serialized_start=4133
  _globals['_DELETEDATARESPONSE']._serialized_end=4232
  _globals['_DATADETAILSREQUEST']._serialized_start=4234
  _globals['_DATADETAILSREQUEST']._serialized_end=4271
  _globals['_DATADETAILSRESPONSE']._serialized_start=4274
  _globals['_DATADETAILSRESPONSE']._serialized_end=4478
  _globals['_DATADETAILSRESPONSE_TASKINFOENTRY']._serialized_start=4431
  _globals['_DATADETAILSRESPONSE_TASKINFOENTRY']._serialized_end=4478
  _globals['_DEVICESTATUSREQUEST']._serialized_start=4480
  _globals['_DEVICESTATUSREQUEST']._serialized_end=4522
  _globals['_DEVICESTATUSRESPONSE']._serialized_start=4525
  _globals['_DEVICESTATUSRESPONSE']._serialized_end=4732
  _globals['_DEVICESTATUSRESPONSE_DEVICESENTRY']._serialized_start=4661
  _globals['_DEVICESTATUSRESPONSE_DEVICESENTRY']._serialized_end=4732
  _globals['_DEVICEINFO']._serialized_start=4735
  _globals['_DEVICEINFO']._serialized_end=5002
  _globals['_DEVICEINFO_PROPERTIESENTRY']._serialized_start=4953
  _globals['_DEVICEINFO_PROPERTIESENTRY']._serialized_end=5002
  _globals['_LISTDEVICESRESPONSE']._serialized_start=5004
  _globals['_LISTDEVICESRESPONSE']._serialized_end=5067
  _globals['_EVENTSUBSCRIPTION']._serialized_start=5069
  _globals['_EVENTSUBSCRIPTION']._serialized_end=5128
  _globals['_EVENT']._serialized_start=5131
  _globals['_EVENT']._serialized_end=5367
  _globals['_EVENT_DATAENTRY']._serialized_start=2400
  _globals['_EVENT_DATAENTRY']._serialized_end=2443
  _globals['_EDGESERVER']._serialized_start=5370
  _globals['_EDGESERVER']._serialized_end=6933
# @@protoc_insertion_point(module_scope)
