# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import edge_server_pb2 as edge__server__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


class EdgeServerStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Authenticate = channel.unary_unary(
                '/edge_server.EdgeServer/Authenticate',
                request_serializer=edge__server__pb2.AuthRequest.SerializeToString,
                response_deserializer=edge__server__pb2.AuthResponse.FromString,
                )
        self.GetSystemStatus = channel.unary_unary(
                '/edge_server.EdgeServer/GetSystemStatus',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=edge__server__pb2.SystemStatusResponse.FromString,
                )
        self.GetModuleList = channel.unary_unary(
                '/edge_server.EdgeServer/GetModuleList',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=edge__server__pb2.ModuleListResponse.FromString,
                )
        self.SubmitTask = channel.unary_unary(
                '/edge_server.EdgeServer/SubmitTask',
                request_serializer=edge__server__pb2.TaskRequest.SerializeToString,
                response_deserializer=edge__server__pb2.TaskResponse.FromString,
                )
        self.GetTaskStatus = channel.unary_unary(
                '/edge_server.EdgeServer/GetTaskStatus',
                request_serializer=edge__server__pb2.TaskStatusRequest.SerializeToString,
                response_deserializer=edge__server__pb2.TaskStatusResponse.FromString,
                )
        self.CancelTask = channel.unary_unary(
                '/edge_server.EdgeServer/CancelTask',
                request_serializer=edge__server__pb2.CancelTaskRequest.SerializeToString,
                response_deserializer=edge__server__pb2.TaskResponse.FromString,
                )
        self.ListTasks = channel.unary_unary(
                '/edge_server.EdgeServer/ListTasks',
                request_serializer=edge__server__pb2.ListTasksRequest.SerializeToString,
                response_deserializer=edge__server__pb2.ListTasksResponse.FromString,
                )
        self.ControlPower = channel.unary_unary(
                '/edge_server.EdgeServer/ControlPower',
                request_serializer=edge__server__pb2.PowerControlRequest.SerializeToString,
                response_deserializer=edge__server__pb2.ControlResponse.FromString,
                )
        self.ControlPTZ = channel.unary_unary(
                '/edge_server.EdgeServer/ControlPTZ',
                request_serializer=edge__server__pb2.PTZControlRequest.SerializeToString,
                response_deserializer=edge__server__pb2.ControlResponse.FromString,
                )
        self.EmergencyShutdown = channel.unary_unary(
                '/edge_server.EdgeServer/EmergencyShutdown',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=edge__server__pb2.ControlResponse.FromString,
                )
        self.StartLidarScan = channel.unary_unary(
                '/edge_server.EdgeServer/StartLidarScan',
                request_serializer=edge__server__pb2.LidarScanRequest.SerializeToString,
                response_deserializer=edge__server__pb2.TaskResponse.FromString,
                )
        self.StartCameraRecording = channel.unary_unary(
                '/edge_server.EdgeServer/StartCameraRecording',
                request_serializer=edge__server__pb2.CameraRecordRequest.SerializeToString,
                response_deserializer=edge__server__pb2.TaskResponse.FromString,
                )
        self.TakeSnapshot = channel.unary_unary(
                '/edge_server.EdgeServer/TakeSnapshot',
                request_serializer=edge__server__pb2.SnapshotRequest.SerializeToString,
                response_deserializer=edge__server__pb2.TaskResponse.FromString,
                )
        self.ListStoredData = channel.unary_unary(
                '/edge_server.EdgeServer/ListStoredData',
                request_serializer=edge__server__pb2.ListDataRequest.SerializeToString,
                response_deserializer=edge__server__pb2.ListDataResponse.FromString,
                )
        self.GetUploadStatus = channel.unary_unary(
                '/edge_server.EdgeServer/GetUploadStatus',
                request_serializer=edge__server__pb2.UploadStatusRequest.SerializeToString,
                response_deserializer=edge__server__pb2.UploadStatusResponse.FromString,
                )
        self.DeleteData = channel.unary_unary(
                '/edge_server.EdgeServer/DeleteData',
                request_serializer=edge__server__pb2.DeleteDataRequest.SerializeToString,
                response_deserializer=edge__server__pb2.DeleteDataResponse.FromString,
                )
        self.GetDataDetails = channel.unary_unary(
                '/edge_server.EdgeServer/GetDataDetails',
                request_serializer=edge__server__pb2.DataDetailsRequest.SerializeToString,
                response_deserializer=edge__server__pb2.DataDetailsResponse.FromString,
                )
        self.GetDeviceStatus = channel.unary_unary(
                '/edge_server.EdgeServer/GetDeviceStatus',
                request_serializer=edge__server__pb2.DeviceStatusRequest.SerializeToString,
                response_deserializer=edge__server__pb2.DeviceStatusResponse.FromString,
                )
        self.ListDevices = channel.unary_unary(
                '/edge_server.EdgeServer/ListDevices',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=edge__server__pb2.ListDevicesResponse.FromString,
                )
        self.SubscribeEvents = channel.stream_stream(
                '/edge_server.EdgeServer/SubscribeEvents',
                request_serializer=edge__server__pb2.EventSubscription.SerializeToString,
                response_deserializer=edge__server__pb2.Event.FromString,
                )


class EdgeServerServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Authenticate(self, request, context):
        """认证接口
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSystemStatus(self, request, context):
        """系统管理
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetModuleList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubmitTask(self, request, context):
        """任务管理
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTaskStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListTasks(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ControlPower(self, request, context):
        """硬件控制
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ControlPTZ(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def EmergencyShutdown(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartLidarScan(self, request, context):
        """数据采集
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartCameraRecording(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TakeSnapshot(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListStoredData(self, request, context):
        """数据管理
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetUploadStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDataDetails(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeviceStatus(self, request, context):
        """设备状态
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ListDevices(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubscribeEvents(self, request_iterator, context):
        """实时流（双向流）
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_EdgeServerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Authenticate': grpc.unary_unary_rpc_method_handler(
                    servicer.Authenticate,
                    request_deserializer=edge__server__pb2.AuthRequest.FromString,
                    response_serializer=edge__server__pb2.AuthResponse.SerializeToString,
            ),
            'GetSystemStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSystemStatus,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=edge__server__pb2.SystemStatusResponse.SerializeToString,
            ),
            'GetModuleList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetModuleList,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=edge__server__pb2.ModuleListResponse.SerializeToString,
            ),
            'SubmitTask': grpc.unary_unary_rpc_method_handler(
                    servicer.SubmitTask,
                    request_deserializer=edge__server__pb2.TaskRequest.FromString,
                    response_serializer=edge__server__pb2.TaskResponse.SerializeToString,
            ),
            'GetTaskStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTaskStatus,
                    request_deserializer=edge__server__pb2.TaskStatusRequest.FromString,
                    response_serializer=edge__server__pb2.TaskStatusResponse.SerializeToString,
            ),
            'CancelTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelTask,
                    request_deserializer=edge__server__pb2.CancelTaskRequest.FromString,
                    response_serializer=edge__server__pb2.TaskResponse.SerializeToString,
            ),
            'ListTasks': grpc.unary_unary_rpc_method_handler(
                    servicer.ListTasks,
                    request_deserializer=edge__server__pb2.ListTasksRequest.FromString,
                    response_serializer=edge__server__pb2.ListTasksResponse.SerializeToString,
            ),
            'ControlPower': grpc.unary_unary_rpc_method_handler(
                    servicer.ControlPower,
                    request_deserializer=edge__server__pb2.PowerControlRequest.FromString,
                    response_serializer=edge__server__pb2.ControlResponse.SerializeToString,
            ),
            'ControlPTZ': grpc.unary_unary_rpc_method_handler(
                    servicer.ControlPTZ,
                    request_deserializer=edge__server__pb2.PTZControlRequest.FromString,
                    response_serializer=edge__server__pb2.ControlResponse.SerializeToString,
            ),
            'EmergencyShutdown': grpc.unary_unary_rpc_method_handler(
                    servicer.EmergencyShutdown,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=edge__server__pb2.ControlResponse.SerializeToString,
            ),
            'StartLidarScan': grpc.unary_unary_rpc_method_handler(
                    servicer.StartLidarScan,
                    request_deserializer=edge__server__pb2.LidarScanRequest.FromString,
                    response_serializer=edge__server__pb2.TaskResponse.SerializeToString,
            ),
            'StartCameraRecording': grpc.unary_unary_rpc_method_handler(
                    servicer.StartCameraRecording,
                    request_deserializer=edge__server__pb2.CameraRecordRequest.FromString,
                    response_serializer=edge__server__pb2.TaskResponse.SerializeToString,
            ),
            'TakeSnapshot': grpc.unary_unary_rpc_method_handler(
                    servicer.TakeSnapshot,
                    request_deserializer=edge__server__pb2.SnapshotRequest.FromString,
                    response_serializer=edge__server__pb2.TaskResponse.SerializeToString,
            ),
            'ListStoredData': grpc.unary_unary_rpc_method_handler(
                    servicer.ListStoredData,
                    request_deserializer=edge__server__pb2.ListDataRequest.FromString,
                    response_serializer=edge__server__pb2.ListDataResponse.SerializeToString,
            ),
            'GetUploadStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetUploadStatus,
                    request_deserializer=edge__server__pb2.UploadStatusRequest.FromString,
                    response_serializer=edge__server__pb2.UploadStatusResponse.SerializeToString,
            ),
            'DeleteData': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteData,
                    request_deserializer=edge__server__pb2.DeleteDataRequest.FromString,
                    response_serializer=edge__server__pb2.DeleteDataResponse.SerializeToString,
            ),
            'GetDataDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDataDetails,
                    request_deserializer=edge__server__pb2.DataDetailsRequest.FromString,
                    response_serializer=edge__server__pb2.DataDetailsResponse.SerializeToString,
            ),
            'GetDeviceStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeviceStatus,
                    request_deserializer=edge__server__pb2.DeviceStatusRequest.FromString,
                    response_serializer=edge__server__pb2.DeviceStatusResponse.SerializeToString,
            ),
            'ListDevices': grpc.unary_unary_rpc_method_handler(
                    servicer.ListDevices,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=edge__server__pb2.ListDevicesResponse.SerializeToString,
            ),
            'SubscribeEvents': grpc.stream_stream_rpc_method_handler(
                    servicer.SubscribeEvents,
                    request_deserializer=edge__server__pb2.EventSubscription.FromString,
                    response_serializer=edge__server__pb2.Event.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'edge_server.EdgeServer', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class EdgeServer(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Authenticate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/Authenticate',
            edge__server__pb2.AuthRequest.SerializeToString,
            edge__server__pb2.AuthResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetSystemStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/GetSystemStatus',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            edge__server__pb2.SystemStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetModuleList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/GetModuleList',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            edge__server__pb2.ModuleListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SubmitTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/SubmitTask',
            edge__server__pb2.TaskRequest.SerializeToString,
            edge__server__pb2.TaskResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetTaskStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/GetTaskStatus',
            edge__server__pb2.TaskStatusRequest.SerializeToString,
            edge__server__pb2.TaskStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CancelTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/CancelTask',
            edge__server__pb2.CancelTaskRequest.SerializeToString,
            edge__server__pb2.TaskResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListTasks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/ListTasks',
            edge__server__pb2.ListTasksRequest.SerializeToString,
            edge__server__pb2.ListTasksResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ControlPower(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/ControlPower',
            edge__server__pb2.PowerControlRequest.SerializeToString,
            edge__server__pb2.ControlResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ControlPTZ(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/ControlPTZ',
            edge__server__pb2.PTZControlRequest.SerializeToString,
            edge__server__pb2.ControlResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def EmergencyShutdown(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/EmergencyShutdown',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            edge__server__pb2.ControlResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartLidarScan(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/StartLidarScan',
            edge__server__pb2.LidarScanRequest.SerializeToString,
            edge__server__pb2.TaskResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartCameraRecording(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/StartCameraRecording',
            edge__server__pb2.CameraRecordRequest.SerializeToString,
            edge__server__pb2.TaskResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def TakeSnapshot(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/TakeSnapshot',
            edge__server__pb2.SnapshotRequest.SerializeToString,
            edge__server__pb2.TaskResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListStoredData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/ListStoredData',
            edge__server__pb2.ListDataRequest.SerializeToString,
            edge__server__pb2.ListDataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetUploadStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/GetUploadStatus',
            edge__server__pb2.UploadStatusRequest.SerializeToString,
            edge__server__pb2.UploadStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/DeleteData',
            edge__server__pb2.DeleteDataRequest.SerializeToString,
            edge__server__pb2.DeleteDataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDataDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/GetDataDetails',
            edge__server__pb2.DataDetailsRequest.SerializeToString,
            edge__server__pb2.DataDetailsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDeviceStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/GetDeviceStatus',
            edge__server__pb2.DeviceStatusRequest.SerializeToString,
            edge__server__pb2.DeviceStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ListDevices(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/edge_server.EdgeServer/ListDevices',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            edge__server__pb2.ListDevicesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SubscribeEvents(request_iterator,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.stream_stream(request_iterator, target, '/edge_server.EdgeServer/SubscribeEvents',
            edge__server__pb2.EventSubscription.SerializeToString,
            edge__server__pb2.Event.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
