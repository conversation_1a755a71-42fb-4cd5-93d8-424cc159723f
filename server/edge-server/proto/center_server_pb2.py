# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: center_server.proto
# Protobuf Python Version: 4.25.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13\x63\x65nter_server.proto\x12\rcenter_server\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\"\xdc\x01\n\x0e\x45\x64geServerInfo\x12\x0f\n\x07\x65\x64ge_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x12\n\nip_address\x18\x03 \x01(\t\x12\x11\n\tgrpc_port\x18\x04 \x01(\x05\x12\x14\n\x0c\x63\x61pabilities\x18\x05 \x03(\t\x12=\n\x08metadata\x18\x06 \x03(\x0b\x32+.center_server.EdgeServerInfo.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"H\n\x10RegisterResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x12\n\nsession_id\x18\x03 \x01(\t\"\x98\x01\n\x10HeartBeatRequest\x12\x0f\n\x07\x65\x64ge_id\x18\x01 \x01(\t\x12\x12\n\nsession_id\x18\x02 \x01(\t\x12+\n\x06status\x18\x03 \x01(\x0b\x32\x1b.center_server.SystemStatus\x12\x32\n\rtask_progress\x18\x04 \x03(\x0b\x32\x1b.center_server.TaskProgress\"\x9f\x02\n\x0cSystemStatus\x12\x11\n\tcpu_usage\x18\x01 \x01(\x02\x12\x14\n\x0cmemory_usage\x18\x02 \x01(\x02\x12\x12\n\ndisk_usage\x18\x03 \x01(\x02\x12\x12\n\nis_healthy\x18\x04 \x01(\x08\x12\x39\n\x07modules\x18\x05 \x03(\x0b\x32(.center_server.SystemStatus.ModulesEntry\x12\x36\n\x0fhardware_status\x18\x06 \x01(\x0b\x32\x1d.center_server.HardwareStatus\x1aK\n\x0cModulesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12*\n\x05value\x18\x02 \x01(\x0b\x32\x1b.center_server.ModuleStatus:\x02\x38\x01\"\xaa\x01\n\x0cModuleStatus\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tis_active\x18\x02 \x01(\x08\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x39\n\x07\x64\x65tails\x18\x04 \x03(\x0b\x32(.center_server.ModuleStatus.DetailsEntry\x1a.\n\x0c\x44\x65tailsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"R\n\x0cTaskProgress\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\x10\n\x08progress\x18\x03 \x01(\x02\x12\x0f\n\x07message\x18\x04 \x01(\t\"\x7f\n\x11HeartBeatResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12/\n\x0bserver_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12(\n\x08\x63ommands\x18\x03 \x03(\x0b\x32\x16.center_server.Command\"\xd4\x01\n\x07\x43ommand\x12\x12\n\ncommand_id\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\x12:\n\nparameters\x18\x03 \x03(\x0b\x32&.center_server.Command.ParametersEntry\x12\x38\n\x10hardware_command\x18\x04 \x01(\x0b\x32\x1e.center_server.HardwareCommand\x1a\x31\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"$\n\x11\x45\x64geServerRequest\x12\x0f\n\x07\x65\x64ge_id\x18\x01 \x01(\t\"J\n\x16\x45\x64geServerListResponse\x12\x30\n\x07servers\x18\x01 \x03(\x0b\x32\x1f.center_server.EdgeServerDetail\"\xcc\x01\n\x10\x45\x64geServerDetail\x12+\n\x04info\x18\x01 \x01(\x0b\x32\x1d.center_server.EdgeServerInfo\x12\x11\n\tis_online\x18\x02 \x01(\x08\x12-\n\tlast_seen\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x33\n\x0e\x63urrent_status\x18\x04 \x01(\x0b\x32\x1b.center_server.SystemStatus\x12\x14\n\x0c\x61\x63tive_tasks\x18\x05 \x01(\x05\"\xb2\x01\n\x18\x45\x64geServerStatusResponse\x12/\n\x06server\x18\x01 \x01(\x0b\x32\x1f.center_server.EdgeServerDetail\x12.\n\rrunning_tasks\x18\x02 \x03(\x0b\x32\x17.center_server.TaskInfo\x12\x35\n\x0erecent_uploads\x18\x03 \x03(\x0b\x32\x1d.center_server.DataUploadInfo\"\xe4\x01\n\x11\x43reateTaskRequest\x12\x11\n\ttask_type\x18\x01 \x01(\t\x12\x44\n\nparameters\x18\x02 \x03(\x0b\x32\x30.center_server.CreateTaskRequest.ParametersEntry\x12-\n\x08schedule\x18\x03 \x01(\x0b\x32\x1b.center_server.TaskSchedule\x12\x14\n\x0ctarget_edges\x18\x04 \x03(\t\x1a\x31\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x7f\n\x0cTaskSchedule\x12.\n\nstart_time\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x18\n\x10\x64uration_seconds\x18\x02 \x01(\x05\x12\x10\n\x08priority\x18\x03 \x01(\t\x12\x13\n\x0bretry_count\x18\x04 \x01(\x05\"5\n\x11\x41ssignTaskRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x0f\n\x07\x65\x64ge_id\x18\x02 \x01(\t\"A\n\x0cTaskResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x0f\n\x07task_id\x18\x03 \x01(\t\"\xea\x01\n\x17UpdateTaskStatusRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x0f\n\x07\x65\x64ge_id\x18\x02 \x01(\t\x12-\n\x08progress\x18\x03 \x01(\x0b\x32\x1b.center_server.TaskProgress\x12K\n\x0bresult_data\x18\x04 \x03(\x0b\x32\x36.center_server.UpdateTaskStatusRequest.ResultDataEntry\x1a\x31\n\x0fResultDataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"A\n\x0fTaskListRequest\x12\x0f\n\x07\x65\x64ge_id\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t\x12\r\n\x05limit\x18\x03 \x01(\x05\"\xc1\x01\n\x08TaskInfo\x12\x0f\n\x07task_id\x18\x01 \x01(\t\x12\x11\n\ttask_type\x18\x02 \x01(\t\x12\x0e\n\x06status\x18\x03 \x01(\t\x12\x0f\n\x07\x65\x64ge_id\x18\x04 \x01(\t\x12.\n\ncreated_at\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12.\n\nupdated_at\x18\x06 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08progress\x18\x07 \x01(\x02\"O\n\x10TaskListResponse\x12&\n\x05tasks\x18\x01 \x03(\x0b\x32\x17.center_server.TaskInfo\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\"\x1e\n\x0bTaskRequest\x12\x0f\n\x07task_id\x18\x01 \x01(\t\"\x90\x03\n\x13TaskDetailsResponse\x12%\n\x04info\x18\x01 \x01(\x0b\x32\x17.center_server.TaskInfo\x12\x46\n\nparameters\x18\x02 \x03(\x0b\x32\x32.center_server.TaskDetailsResponse.ParametersEntry\x12-\n\x08schedule\x18\x03 \x01(\x0b\x32\x1b.center_server.TaskSchedule\x12,\n\x07history\x18\x04 \x03(\x0b\x32\x1b.center_server.TaskProgress\x12G\n\x0bresult_data\x18\x05 \x03(\x0b\x32\x32.center_server.TaskDetailsResponse.ResultDataEntry\x1a\x31\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x31\n\x0fResultDataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x9f\x02\n\x0e\x44\x61taUploadInfo\x12\x11\n\tupload_id\x18\x01 \x01(\t\x12\x0f\n\x07\x65\x64ge_id\x18\x02 \x01(\t\x12\x0f\n\x07task_id\x18\x03 \x01(\t\x12\x11\n\tdata_type\x18\x04 \x01(\t\x12\x11\n\tfile_path\x18\x05 \x01(\t\x12\x11\n\tfile_size\x18\x06 \x01(\x03\x12/\n\x0bupload_time\x18\x07 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12=\n\x08metadata\x18\x08 \x03(\x0b\x32+.center_server.DataUploadInfo.MetadataEntry\x1a/\n\rMetadataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"0\n\x0c\x44\x61taResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\"\xb3\x01\n\x0f\x44\x61taListRequest\x12\x0f\n\x07\x65\x64ge_id\x18\x01 \x01(\t\x12\x0f\n\x07task_id\x18\x02 \x01(\t\x12\x11\n\tdata_type\x18\x03 \x01(\t\x12.\n\nstart_time\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x05 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05limit\x18\x06 \x01(\x05\"h\n\x10\x44\x61taListResponse\x12+\n\x04\x64\x61ta\x18\x01 \x03(\x0b\x32\x1d.center_server.DataUploadInfo\x12\x13\n\x0btotal_count\x18\x02 \x01(\x05\x12\x12\n\ntotal_size\x18\x03 \x01(\x03\"\x81\x01\n\x10\x44\x61taStatsRequest\x12\x0f\n\x07\x65\x64ge_id\x18\x01 \x01(\t\x12.\n\nstart_time\x18\x02 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12,\n\x08\x65nd_time\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xcf\x02\n\x11\x44\x61taStatsResponse\x12Q\n\x12\x64\x61ta_count_by_type\x18\x01 \x03(\x0b\x32\x35.center_server.DataStatsResponse.DataCountByTypeEntry\x12O\n\x11\x64\x61ta_size_by_type\x18\x02 \x03(\x0b\x32\x34.center_server.DataStatsResponse.DataSizeByTypeEntry\x12\x13\n\x0btotal_count\x18\x03 \x01(\x03\x12\x12\n\ntotal_size\x18\x04 \x01(\x03\x1a\x36\n\x14\x44\x61taCountByTypeEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x03:\x02\x38\x01\x1a\x35\n\x13\x44\x61taSizeByTypeEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x03:\x02\x38\x01\"\xc4\x02\n\x16SystemOverviewResponse\x12\x1a\n\x12total_edge_servers\x18\x01 \x01(\x05\x12\x1b\n\x13online_edge_servers\x18\x02 \x01(\x05\x12\x13\n\x0btotal_tasks\x18\x03 \x01(\x05\x12\x15\n\rrunning_tasks\x18\x04 \x01(\x05\x12\x17\n\x0f\x63ompleted_tasks\x18\x05 \x01(\x05\x12\x14\n\x0c\x66\x61iled_tasks\x18\x06 \x01(\x05\x12\x17\n\x0ftotal_data_size\x18\x07 \x01(\x03\x12J\n\x0b\x61vg_metrics\x18\x08 \x03(\x0b\x32\x35.center_server.SystemOverviewResponse.AvgMetricsEntry\x1a\x31\n\x0f\x41vgMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"T\n\x0eMetricsRequest\x12\x10\n\x08\x65\x64ge_ids\x18\x01 \x03(\t\x12\x14\n\x0cmetric_names\x18\x02 \x03(\t\x12\x1a\n\x12time_range_minutes\x18\x03 \x01(\x05\"\xa8\x01\n\x0fMetricsResponse\x12\x45\n\x0c\x65\x64ge_metrics\x18\x01 \x03(\x0b\x32/.center_server.MetricsResponse.EdgeMetricsEntry\x1aN\n\x10\x45\x64geMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12)\n\x05value\x18\x02 \x01(\x0b\x32\x1a.center_server.EdgeMetrics:\x02\x38\x01\"\xa3\x01\n\x0b\x45\x64geMetrics\x12\x0f\n\x07\x65\x64ge_id\x18\x01 \x01(\t\x12\x38\n\x07metrics\x18\x02 \x03(\x0b\x32\'.center_server.EdgeMetrics.MetricsEntry\x1aI\n\x0cMetricsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12(\n\x05value\x18\x02 \x01(\x0b\x32\x19.center_server.MetricData:\x02\x38\x01\"8\n\nMetricData\x12*\n\x06points\x18\x01 \x03(\x0b\x32\x1a.center_server.MetricPoint\"K\n\x0bMetricPoint\x12-\n\ttimestamp\x18\x01 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\r\n\x05value\x18\x02 \x01(\x02\"\xc0\x02\n\x0eHardwareStatus\x12\x44\n\x0cpower_status\x18\x01 \x03(\x0b\x32..center_server.HardwareStatus.PowerStatusEntry\x12,\n\nptz_status\x18\x02 \x01(\x0b\x32\x18.center_server.PTZStatus\x12;\n\x07\x64\x65vices\x18\x03 \x03(\x0b\x32*.center_server.HardwareStatus.DevicesEntry\x1a\x32\n\x10PowerStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1aI\n\x0c\x44\x65vicesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12(\n\x05value\x18\x02 \x01(\x0b\x32\x19.center_server.DeviceInfo:\x02\x38\x01\"b\n\tPTZStatus\x12\x12\n\nhorizontal\x18\x01 \x01(\x02\x12\x10\n\x08vertical\x18\x02 \x01(\x02\x12\x0c\n\x04zoom\x18\x03 \x01(\x02\x12\x11\n\tis_moving\x18\x04 \x01(\x08\x12\x0e\n\x06status\x18\x05 \x01(\t\"\xa3\x01\n\nDeviceInfo\x12\x13\n\x0b\x64\x65vice_type\x18\x01 \x01(\t\x12\x0e\n\x06status\x18\x02 \x01(\t\x12=\n\nproperties\x18\x03 \x03(\x0b\x32).center_server.DeviceInfo.PropertiesEntry\x1a\x31\n\x0fPropertiesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xae\x01\n\x0fHardwareCommand\x12\x14\n\x0c\x63ommand_type\x18\x01 \x01(\t\x12\x0e\n\x06\x64\x65vice\x18\x02 \x01(\t\x12\x42\n\nparameters\x18\x03 \x03(\x0b\x32..center_server.HardwareCommand.ParametersEntry\x1a\x31\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"Z\n\x16HardwareControlRequest\x12\x0f\n\x07\x65\x64ge_id\x18\x01 \x01(\t\x12/\n\x07\x63ommand\x18\x02 \x01(\x0b\x32\x1e.center_server.HardwareCommand\"O\n\x17HardwareControlResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x12\n\ncommand_id\x18\x03 \x01(\t\"\x93\x01\n\x16HardwareStatusResponse\x12\x0f\n\x07\x65\x64ge_id\x18\x01 \x01(\t\x12\x36\n\x0fhardware_status\x18\x02 \x01(\x0b\x32\x1d.center_server.HardwareStatus\x12\x30\n\x0clast_updated\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp2\xa5\x0b\n\x0c\x43\x65nterServer\x12T\n\x12RegisterEdgeServer\x12\x1d.center_server.EdgeServerInfo\x1a\x1f.center_server.RegisterResponse\x12N\n\tHeartBeat\x12\x1f.center_server.HeartBeatRequest\x1a .center_server.HeartBeatResponse\x12R\n\x11GetEdgeServerList\x12\x16.google.protobuf.Empty\x1a%.center_server.EdgeServerListResponse\x12`\n\x13GetEdgeServerStatus\x12 .center_server.EdgeServerRequest\x1a\'.center_server.EdgeServerStatusResponse\x12K\n\nCreateTask\x12 .center_server.CreateTaskRequest\x1a\x1b.center_server.TaskResponse\x12K\n\nAssignTask\x12 .center_server.AssignTaskRequest\x1a\x1b.center_server.TaskResponse\x12W\n\x10UpdateTaskStatus\x12&.center_server.UpdateTaskStatusRequest\x1a\x1b.center_server.TaskResponse\x12N\n\x0bGetTaskList\x12\x1e.center_server.TaskListRequest\x1a\x1f.center_server.TaskListResponse\x12P\n\x0eGetTaskDetails\x12\x1a.center_server.TaskRequest\x1a\".center_server.TaskDetailsResponse\x12\x45\n\nCancelTask\x12\x1a.center_server.TaskRequest\x1a\x1b.center_server.TaskResponse\x12P\n\x12RegisterDataUpload\x12\x1d.center_server.DataUploadInfo\x1a\x1b.center_server.DataResponse\x12N\n\x0bGetDataList\x12\x1e.center_server.DataListRequest\x1a\x1f.center_server.DataListResponse\x12V\n\x11GetDataStatistics\x12\x1f.center_server.DataStatsRequest\x1a .center_server.DataStatsResponse\x12R\n\x11GetSystemOverview\x12\x16.google.protobuf.Empty\x1a%.center_server.SystemOverviewResponse\x12K\n\nGetMetrics\x12\x1d.center_server.MetricsRequest\x1a\x1e.center_server.MetricsResponse\x12\x64\n\x13SendHardwareCommand\x12%.center_server.HardwareControlRequest\x1a&.center_server.HardwareControlResponse\x12\\\n\x11GetHardwareStatus\x12 .center_server.EdgeServerRequest\x1a%.center_server.HardwareStatusResponseb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'center_server_pb2', _globals)
if _descriptor._USE_C_DESCRIPTORS == False:
  DESCRIPTOR._options = None
  _globals['_EDGESERVERINFO_METADATAENTRY']._options = None
  _globals['_EDGESERVERINFO_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_SYSTEMSTATUS_MODULESENTRY']._options = None
  _globals['_SYSTEMSTATUS_MODULESENTRY']._serialized_options = b'8\001'
  _globals['_MODULESTATUS_DETAILSENTRY']._options = None
  _globals['_MODULESTATUS_DETAILSENTRY']._serialized_options = b'8\001'
  _globals['_COMMAND_PARAMETERSENTRY']._options = None
  _globals['_COMMAND_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_CREATETASKREQUEST_PARAMETERSENTRY']._options = None
  _globals['_CREATETASKREQUEST_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_UPDATETASKSTATUSREQUEST_RESULTDATAENTRY']._options = None
  _globals['_UPDATETASKSTATUSREQUEST_RESULTDATAENTRY']._serialized_options = b'8\001'
  _globals['_TASKDETAILSRESPONSE_PARAMETERSENTRY']._options = None
  _globals['_TASKDETAILSRESPONSE_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_TASKDETAILSRESPONSE_RESULTDATAENTRY']._options = None
  _globals['_TASKDETAILSRESPONSE_RESULTDATAENTRY']._serialized_options = b'8\001'
  _globals['_DATAUPLOADINFO_METADATAENTRY']._options = None
  _globals['_DATAUPLOADINFO_METADATAENTRY']._serialized_options = b'8\001'
  _globals['_DATASTATSRESPONSE_DATACOUNTBYTYPEENTRY']._options = None
  _globals['_DATASTATSRESPONSE_DATACOUNTBYTYPEENTRY']._serialized_options = b'8\001'
  _globals['_DATASTATSRESPONSE_DATASIZEBYTYPEENTRY']._options = None
  _globals['_DATASTATSRESPONSE_DATASIZEBYTYPEENTRY']._serialized_options = b'8\001'
  _globals['_SYSTEMOVERVIEWRESPONSE_AVGMETRICSENTRY']._options = None
  _globals['_SYSTEMOVERVIEWRESPONSE_AVGMETRICSENTRY']._serialized_options = b'8\001'
  _globals['_METRICSRESPONSE_EDGEMETRICSENTRY']._options = None
  _globals['_METRICSRESPONSE_EDGEMETRICSENTRY']._serialized_options = b'8\001'
  _globals['_EDGEMETRICS_METRICSENTRY']._options = None
  _globals['_EDGEMETRICS_METRICSENTRY']._serialized_options = b'8\001'
  _globals['_HARDWARESTATUS_POWERSTATUSENTRY']._options = None
  _globals['_HARDWARESTATUS_POWERSTATUSENTRY']._serialized_options = b'8\001'
  _globals['_HARDWARESTATUS_DEVICESENTRY']._options = None
  _globals['_HARDWARESTATUS_DEVICESENTRY']._serialized_options = b'8\001'
  _globals['_DEVICEINFO_PROPERTIESENTRY']._options = None
  _globals['_DEVICEINFO_PROPERTIESENTRY']._serialized_options = b'8\001'
  _globals['_HARDWARECOMMAND_PARAMETERSENTRY']._options = None
  _globals['_HARDWARECOMMAND_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_EDGESERVERINFO']._serialized_start=101
  _globals['_EDGESERVERINFO']._serialized_end=321
  _globals['_EDGESERVERINFO_METADATAENTRY']._serialized_start=274
  _globals['_EDGESERVERINFO_METADATAENTRY']._serialized_end=321
  _globals['_REGISTERRESPONSE']._serialized_start=323
  _globals['_REGISTERRESPONSE']._serialized_end=395
  _globals['_HEARTBEATREQUEST']._serialized_start=398
  _globals['_HEARTBEATREQUEST']._serialized_end=550
  _globals['_SYSTEMSTATUS']._serialized_start=553
  _globals['_SYSTEMSTATUS']._serialized_end=840
  _globals['_SYSTEMSTATUS_MODULESENTRY']._serialized_start=765
  _globals['_SYSTEMSTATUS_MODULESENTRY']._serialized_end=840
  _globals['_MODULESTATUS']._serialized_start=843
  _globals['_MODULESTATUS']._serialized_end=1013
  _globals['_MODULESTATUS_DETAILSENTRY']._serialized_start=967
  _globals['_MODULESTATUS_DETAILSENTRY']._serialized_end=1013
  _globals['_TASKPROGRESS']._serialized_start=1015
  _globals['_TASKPROGRESS']._serialized_end=1097
  _globals['_HEARTBEATRESPONSE']._serialized_start=1099
  _globals['_HEARTBEATRESPONSE']._serialized_end=1226
  _globals['_COMMAND']._serialized_start=1229
  _globals['_COMMAND']._serialized_end=1441
  _globals['_COMMAND_PARAMETERSENTRY']._serialized_start=1392
  _globals['_COMMAND_PARAMETERSENTRY']._serialized_end=1441
  _globals['_EDGESERVERREQUEST']._serialized_start=1443
  _globals['_EDGESERVERREQUEST']._serialized_end=1479
  _globals['_EDGESERVERLISTRESPONSE']._serialized_start=1481
  _globals['_EDGESERVERLISTRESPONSE']._serialized_end=1555
  _globals['_EDGESERVERDETAIL']._serialized_start=1558
  _globals['_EDGESERVERDETAIL']._serialized_end=1762
  _globals['_EDGESERVERSTATUSRESPONSE']._serialized_start=1765
  _globals['_EDGESERVERSTATUSRESPONSE']._serialized_end=1943
  _globals['_CREATETASKREQUEST']._serialized_start=1946
  _globals['_CREATETASKREQUEST']._serialized_end=2174
  _globals['_CREATETASKREQUEST_PARAMETERSENTRY']._serialized_start=1392
  _globals['_CREATETASKREQUEST_PARAMETERSENTRY']._serialized_end=1441
  _globals['_TASKSCHEDULE']._serialized_start=2176
  _globals['_TASKSCHEDULE']._serialized_end=2303
  _globals['_ASSIGNTASKREQUEST']._serialized_start=2305
  _globals['_ASSIGNTASKREQUEST']._serialized_end=2358
  _globals['_TASKRESPONSE']._serialized_start=2360
  _globals['_TASKRESPONSE']._serialized_end=2425
  _globals['_UPDATETASKSTATUSREQUEST']._serialized_start=2428
  _globals['_UPDATETASKSTATUSREQUEST']._serialized_end=2662
  _globals['_UPDATETASKSTATUSREQUEST_RESULTDATAENTRY']._serialized_start=2613
  _globals['_UPDATETASKSTATUSREQUEST_RESULTDATAENTRY']._serialized_end=2662
  _globals['_TASKLISTREQUEST']._serialized_start=2664
  _globals['_TASKLISTREQUEST']._serialized_end=2729
  _globals['_TASKINFO']._serialized_start=2732
  _globals['_TASKINFO']._serialized_end=2925
  _globals['_TASKLISTRESPONSE']._serialized_start=2927
  _globals['_TASKLISTRESPONSE']._serialized_end=3006
  _globals['_TASKREQUEST']._serialized_start=3008
  _globals['_TASKREQUEST']._serialized_end=3038
  _globals['_TASKDETAILSRESPONSE']._serialized_start=3041
  _globals['_TASKDETAILSRESPONSE']._serialized_end=3441
  _globals['_TASKDETAILSRESPONSE_PARAMETERSENTRY']._serialized_start=1392
  _globals['_TASKDETAILSRESPONSE_PARAMETERSENTRY']._serialized_end=1441
  _globals['_TASKDETAILSRESPONSE_RESULTDATAENTRY']._serialized_start=2613
  _globals['_TASKDETAILSRESPONSE_RESULTDATAENTRY']._serialized_end=2662
  _globals['_DATAUPLOADINFO']._serialized_start=3444
  _globals['_DATAUPLOADINFO']._serialized_end=3731
  _globals['_DATAUPLOADINFO_METADATAENTRY']._serialized_start=274
  _globals['_DATAUPLOADINFO_METADATAENTRY']._serialized_end=321
  _globals['_DATARESPONSE']._serialized_start=3733
  _globals['_DATARESPONSE']._serialized_end=3781
  _globals['_DATALISTREQUEST']._serialized_start=3784
  _globals['_DATALISTREQUEST']._serialized_end=3963
  _globals['_DATALISTRESPONSE']._serialized_start=3965
  _globals['_DATALISTRESPONSE']._serialized_end=4069
  _globals['_DATASTATSREQUEST']._serialized_start=4072
  _globals['_DATASTATSREQUEST']._serialized_end=4201
  _globals['_DATASTATSRESPONSE']._serialized_start=4204
  _globals['_DATASTATSRESPONSE']._serialized_end=4539
  _globals['_DATASTATSRESPONSE_DATACOUNTBYTYPEENTRY']._serialized_start=4430
  _globals['_DATASTATSRESPONSE_DATACOUNTBYTYPEENTRY']._serialized_end=4484
  _globals['_DATASTATSRESPONSE_DATASIZEBYTYPEENTRY']._serialized_start=4486
  _globals['_DATASTATSRESPONSE_DATASIZEBYTYPEENTRY']._serialized_end=4539
  _globals['_SYSTEMOVERVIEWRESPONSE']._serialized_start=4542
  _globals['_SYSTEMOVERVIEWRESPONSE']._serialized_end=4866
  _globals['_SYSTEMOVERVIEWRESPONSE_AVGMETRICSENTRY']._serialized_start=4817
  _globals['_SYSTEMOVERVIEWRESPONSE_AVGMETRICSENTRY']._serialized_end=4866
  _globals['_METRICSREQUEST']._serialized_start=4868
  _globals['_METRICSREQUEST']._serialized_end=4952
  _globals['_METRICSRESPONSE']._serialized_start=4955
  _globals['_METRICSRESPONSE']._serialized_end=5123
  _globals['_METRICSRESPONSE_EDGEMETRICSENTRY']._serialized_start=5045
  _globals['_METRICSRESPONSE_EDGEMETRICSENTRY']._serialized_end=5123
  _globals['_EDGEMETRICS']._serialized_start=5126
  _globals['_EDGEMETRICS']._serialized_end=5289
  _globals['_EDGEMETRICS_METRICSENTRY']._serialized_start=5216
  _globals['_EDGEMETRICS_METRICSENTRY']._serialized_end=5289
  _globals['_METRICDATA']._serialized_start=5291
  _globals['_METRICDATA']._serialized_end=5347
  _globals['_METRICPOINT']._serialized_start=5349
  _globals['_METRICPOINT']._serialized_end=5424
  _globals['_HARDWARESTATUS']._serialized_start=5427
  _globals['_HARDWARESTATUS']._serialized_end=5747
  _globals['_HARDWARESTATUS_POWERSTATUSENTRY']._serialized_start=5622
  _globals['_HARDWARESTATUS_POWERSTATUSENTRY']._serialized_end=5672
  _globals['_HARDWARESTATUS_DEVICESENTRY']._serialized_start=5674
  _globals['_HARDWARESTATUS_DEVICESENTRY']._serialized_end=5747
  _globals['_PTZSTATUS']._serialized_start=5749
  _globals['_PTZSTATUS']._serialized_end=5847
  _globals['_DEVICEINFO']._serialized_start=5850
  _globals['_DEVICEINFO']._serialized_end=6013
  _globals['_DEVICEINFO_PROPERTIESENTRY']._serialized_start=5964
  _globals['_DEVICEINFO_PROPERTIESENTRY']._serialized_end=6013
  _globals['_HARDWARECOMMAND']._serialized_start=6016
  _globals['_HARDWARECOMMAND']._serialized_end=6190
  _globals['_HARDWARECOMMAND_PARAMETERSENTRY']._serialized_start=1392
  _globals['_HARDWARECOMMAND_PARAMETERSENTRY']._serialized_end=1441
  _globals['_HARDWARECONTROLREQUEST']._serialized_start=6192
  _globals['_HARDWARECONTROLREQUEST']._serialized_end=6282
  _globals['_HARDWARECONTROLRESPONSE']._serialized_start=6284
  _globals['_HARDWARECONTROLRESPONSE']._serialized_end=6363
  _globals['_HARDWARESTATUSRESPONSE']._serialized_start=6366
  _globals['_HARDWARESTATUSRESPONSE']._serialized_end=6513
  _globals['_CENTERSERVER']._serialized_start=6516
  _globals['_CENTERSERVER']._serialized_end=7961
# @@protoc_insertion_point(module_scope)
