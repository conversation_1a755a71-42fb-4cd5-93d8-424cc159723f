syntax = "proto3";

package edge_server;

// 导入标准包
import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

service EdgeServer {
    // 认证接口
    rpc Authenticate(AuthRequest) returns (AuthResponse);
    // 系统管理
    rpc GetSystemStatus(google.protobuf.Empty) returns (SystemStatusResponse);
    rpc GetModuleList(google.protobuf.Empty) returns (ModuleListResponse);
    
    // 任务管理
    rpc SubmitTask(TaskRequest) returns (TaskResponse);
    rpc GetTaskStatus(TaskStatusRequest) returns (TaskStatusResponse);
    rpc CancelTask(CancelTaskRequest) returns (TaskResponse);
    rpc ListTasks(ListTasksRequest) returns (ListTasksResponse);
    
    // 硬件控制
    rpc ControlPower(PowerControlRequest) returns (ControlResponse);
    rpc ControlPTZ(PTZControlRequest) returns (ControlResponse);
    rpc EmergencyShutdown(google.protobuf.Empty) returns (ControlResponse);
    
    // 数据采集
    rpc StartLidarScan(LidarScanRequest) returns (TaskResponse);
    rpc StartCameraRecording(CameraRecordRequest) returns (TaskResponse);
    rpc TakeSnapshot(SnapshotRequest) returns (TaskResponse);
    
    // 数据管理
    rpc ListStoredData(ListDataRequest) returns (ListDataResponse);
    rpc GetUploadStatus(UploadStatusRequest) returns (UploadStatusResponse);
    rpc DeleteData(DeleteDataRequest) returns (DeleteDataResponse);
    rpc GetDataDetails(DataDetailsRequest) returns (DataDetailsResponse);
    
    // 设备状态
    rpc GetDeviceStatus(DeviceStatusRequest) returns (DeviceStatusResponse);
    rpc ListDevices(google.protobuf.Empty) returns (ListDevicesResponse);
    
    // 实时流（双向流）
    rpc SubscribeEvents(stream EventSubscription) returns (stream Event);
}

// 认证消息定义
message AuthRequest {
    string token = 1;          // 认证令牌
    string edge_id = 2;        // 边缘服务器ID
    map<string, string> metadata = 3;  // 额外元数据
}

message AuthResponse {
    bool success = 1;
    string session_id = 2;     // 会话ID
    string message = 3;
    int64 expires_at = 4;      // 过期时间戳
}

// 基础消息定义
message TaskRequest {
    string type = 1;          // control, collect_data, upload, query
    string target = 2;        // hardware, lidar, camera, minio
    string action = 3;        // specific action
    map<string, string> params = 4;
    int32 priority = 5;
    int32 timeout = 6;
}

message TaskResponse {
    string task_id = 1;
    bool success = 2;
    string message = 3;
}

message TaskStatusRequest {
    string task_id = 1;
}

message TaskStatusResponse {
    string task_id = 1;
    string status = 2;        // pending, running, completed, failed
    float progress = 3;
    string error = 4;
    google.protobuf.Timestamp created_at = 5;
    google.protobuf.Timestamp completed_at = 6;
    map<string, string> result_data = 7;
}

message CancelTaskRequest {
    string task_id = 1;
}

message ListTasksRequest {
    string status_filter = 1;  // optional: filter by status
    int32 limit = 2;          // max results
}

message ListTasksResponse {
    repeated TaskInfo tasks = 1;
}

message TaskInfo {
    string task_id = 1;
    string type = 2;
    string target = 3;
    string action = 4;
    string status = 5;
    float progress = 6;
    google.protobuf.Timestamp created_at = 7;
}

// 系统状态
message SystemStatusResponse {
    bool healthy = 1;
    map<string, ModuleStatus> modules = 2;
    TaskBusStatus task_bus = 3;
    SystemMetrics metrics = 4;
}

message ModuleStatus {
    string name = 1;
    string status = 2;        // idle, busy, error, offline
    bool healthy = 3;
    string current_task = 4;
}

message TaskBusStatus {
    int32 pending_tasks = 1;
    int32 running_tasks = 2;
    int32 completed_tasks = 3;
    float success_rate = 4;
    float queue_usage = 5;
}

message SystemMetrics {
    float cpu_percent = 1;
    float memory_percent = 2;
    float disk_percent = 3;
    int64 memory_used_mb = 4;
    int64 disk_free_gb = 5;
}

message ModuleListResponse {
    repeated ModuleInfo modules = 1;
}

message ModuleInfo {
    string name = 1;
    string status = 2;
    repeated string actions = 3;
    map<string, string> features = 4;
}

// 硬件控制
message PowerControlRequest {
    string device = 1;        // lidar, ptz, camera1, camera2, etc
    string action = 2;        // on, off, cycle
    int32 delay = 3;         // for power cycle
}

message PTZControlRequest {
    float horizontal = 1;
    float vertical = 2;
    float zoom = 3;
    int32 speed = 4;
    string action = 5;        // goto, move, stop
}

message ControlResponse {
    bool success = 1;
    string message = 2;
    map<string, string> data = 3;
}

// 激光雷达扫描
message LidarScanRequest {
    string scan_type = 1;     // point_scan, terrain_scan
    int32 duration = 2;       // seconds for point scan
    Position position = 3;    // scan position
    ScanArea scan_area = 4;   // for terrain scan
    float resolution = 5;     // degrees
    string task_name = 6;     // 任务名称
    map<string, string> metadata = 7;  // 元数据
}

message Position {
    float horizontal = 1;
    float vertical = 2;
}

message ScanArea {
    float h_start = 1;
    float h_end = 2;
    float v_start = 3;
    float v_end = 4;
}

// 摄像头控制
message CameraRecordRequest {
    string channel = 1;       // ptz1, ptz2, fixed1, fixed2
    int32 duration = 2;       // seconds
    Position position = 3;    // optional PTZ position
    int32 preset_id = 4;     // optional preset
    string task_name = 5;     // 任务名称
    RecordingOptions options = 6;  // 录制选项
}

message RecordingOptions {
    string quality = 1;       // high, medium, low
    bool with_audio = 2;      // 是否录制音频
    string format = 3;        // mp4, avi, etc
}

message SnapshotRequest {
    string channel = 1;
    Position position = 2;    // optional
    string quality = 3;       // high, medium, low
    string format = 4;        // jpg, png
}

// 数据管理
message ListDataRequest {
    string data_type = 1;     // lidar, camera, all
    string date_filter = 2;   // YYYYMMDD format
    int32 limit = 3;
    int32 offset = 4;         // 分页偏移
    string sort_by = 5;       // created_at, size, name
    bool ascending = 6;       // 排序方向
}

message ListDataResponse {
    repeated DataFileInfo files = 1;
    int64 total_size = 2;
}

message DataFileInfo {
    string file_id = 1;        // 文件唯一ID
    string file_name = 2;
    string data_type = 3;
    int64 file_size = 4;
    google.protobuf.Timestamp created_at = 5;
    string upload_status = 6;  // pending, uploading, completed, failed
    string object_name = 7;    // MinIO object name if uploaded
    string file_path = 8;      // 本地文件路径
    map<string, string> metadata = 9;  // 文件元数据
    string checksum = 10;      // 文件校验和
}

message UploadStatusRequest {
    string task_id = 1;        // optional: specific task
}

message UploadStatusResponse {
    int32 total_files = 1;
    int32 uploaded_files = 2;
    int32 failed_files = 3;
    int64 total_bytes = 4;
    int64 uploaded_bytes = 5;
    repeated UploadTaskInfo tasks = 6;
}

message UploadTaskInfo {
    string upload_id = 1;
    string file_path = 2;
    string status = 3;
    float progress = 4;
}

// 删除数据请求
message DeleteDataRequest {
    repeated string file_ids = 1;  // 要删除的文件ID列表
    bool force = 2;                // 强制删除（包括已上传的）
}

message DeleteDataResponse {
    bool success = 1;
    int32 deleted_count = 2;
    repeated string failed_files = 3;
    string message = 4;
}

// 数据详情请求
message DataDetailsRequest {
    string file_id = 1;
}

message DataDetailsResponse {
    DataFileInfo file_info = 1;
    string preview_url = 2;        // 预览URL（如果适用）
    map<string, string> task_info = 3;  // 相关任务信息
}

// 设备状态请求
message DeviceStatusRequest {
    string device_type = 1;        // lidar, camera, ptz, all
}

message DeviceStatusResponse {
    map<string, DeviceInfo> devices = 1;
    google.protobuf.Timestamp timestamp = 2;
}

message DeviceInfo {
    string device_id = 1;
    string device_type = 2;        // lidar, camera, ptz
    string status = 3;             // online, offline, error, busy
    bool power_on = 4;             // 电源状态
    map<string, string> properties = 5;  // 设备属性
    string current_task = 6;       // 当前任务ID
    google.protobuf.Timestamp last_seen = 7;
}

message ListDevicesResponse {
    repeated DeviceInfo devices = 1;
}

// 事件订阅
message EventSubscription {
    repeated string event_types = 1;  // task.*, system.*, data.*, device.*
    string client_id = 2;          // 客户端ID
}

message Event {
    string event_id = 1;           // 事件ID
    string event_type = 2;         // 事件类型
    google.protobuf.Timestamp timestamp = 3;
    string source = 4;             // 事件源
    string severity = 5;           // info, warning, error, critical
    map<string, string> data = 6;  // 事件数据
    string description = 7;        // 事件描述
}