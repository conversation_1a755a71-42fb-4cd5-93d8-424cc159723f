#!/bin/bash

# 编译protobuf文件脚本

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
cd "$SCRIPT_DIR"

echo "Compiling protobuf files..."

# 编译proto文件到当前目录
python -m grpc_tools.protoc \
    -I. \
    --python_out=. \
    --grpc_python_out=. \
    edge_server.proto

python -m grpc_tools.protoc \
    -I. \
    --python_out=. \
    --grpc_python_out=. \
    center_server.proto

# 修复导入路径（grpc_tools生成的代码有时需要调整导入路径）
sed -i 's/import edge_server_pb2/from . import edge_server_pb2/g' ./edge_server_pb2_grpc.py 2>/dev/null || true
sed -i 's/import center_server_pb2/from . import center_server_pb2/g' ./center_server_pb2_grpc.py 2>/dev/null || true

echo "Protobuf compilation completed!"

# 复制到grpc_proto目录（如果存在）
if [ -d "../src/grpc_proto" ]; then
    echo "Copying to grpc_proto directory..."
    cp *_pb2.py *_pb2_grpc.py ../src/grpc_proto/ 2>/dev/null || true
fi