# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from . import center_server_pb2 as center__server__pb2
from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2


class CenterServerStub(object):
    """中央服务器服务定义
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.RegisterEdgeServer = channel.unary_unary(
                '/center_server.CenterServer/RegisterEdgeServer',
                request_serializer=center__server__pb2.EdgeServerInfo.SerializeToString,
                response_deserializer=center__server__pb2.RegisterResponse.FromString,
                )
        self.HeartBeat = channel.unary_unary(
                '/center_server.CenterServer/HeartBeat',
                request_serializer=center__server__pb2.HeartBeatRequest.SerializeToString,
                response_deserializer=center__server__pb2.HeartBeatResponse.FromString,
                )
        self.GetEdgeServerList = channel.unary_unary(
                '/center_server.CenterServer/GetEdgeServerList',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=center__server__pb2.EdgeServerListResponse.FromString,
                )
        self.GetEdgeServerStatus = channel.unary_unary(
                '/center_server.CenterServer/GetEdgeServerStatus',
                request_serializer=center__server__pb2.EdgeServerRequest.SerializeToString,
                response_deserializer=center__server__pb2.EdgeServerStatusResponse.FromString,
                )
        self.CreateTask = channel.unary_unary(
                '/center_server.CenterServer/CreateTask',
                request_serializer=center__server__pb2.CreateTaskRequest.SerializeToString,
                response_deserializer=center__server__pb2.TaskResponse.FromString,
                )
        self.AssignTask = channel.unary_unary(
                '/center_server.CenterServer/AssignTask',
                request_serializer=center__server__pb2.AssignTaskRequest.SerializeToString,
                response_deserializer=center__server__pb2.TaskResponse.FromString,
                )
        self.UpdateTaskStatus = channel.unary_unary(
                '/center_server.CenterServer/UpdateTaskStatus',
                request_serializer=center__server__pb2.UpdateTaskStatusRequest.SerializeToString,
                response_deserializer=center__server__pb2.TaskResponse.FromString,
                )
        self.GetTaskList = channel.unary_unary(
                '/center_server.CenterServer/GetTaskList',
                request_serializer=center__server__pb2.TaskListRequest.SerializeToString,
                response_deserializer=center__server__pb2.TaskListResponse.FromString,
                )
        self.GetTaskDetails = channel.unary_unary(
                '/center_server.CenterServer/GetTaskDetails',
                request_serializer=center__server__pb2.TaskRequest.SerializeToString,
                response_deserializer=center__server__pb2.TaskDetailsResponse.FromString,
                )
        self.CancelTask = channel.unary_unary(
                '/center_server.CenterServer/CancelTask',
                request_serializer=center__server__pb2.TaskRequest.SerializeToString,
                response_deserializer=center__server__pb2.TaskResponse.FromString,
                )
        self.RegisterDataUpload = channel.unary_unary(
                '/center_server.CenterServer/RegisterDataUpload',
                request_serializer=center__server__pb2.DataUploadInfo.SerializeToString,
                response_deserializer=center__server__pb2.DataResponse.FromString,
                )
        self.GetDataList = channel.unary_unary(
                '/center_server.CenterServer/GetDataList',
                request_serializer=center__server__pb2.DataListRequest.SerializeToString,
                response_deserializer=center__server__pb2.DataListResponse.FromString,
                )
        self.GetDataStatistics = channel.unary_unary(
                '/center_server.CenterServer/GetDataStatistics',
                request_serializer=center__server__pb2.DataStatsRequest.SerializeToString,
                response_deserializer=center__server__pb2.DataStatsResponse.FromString,
                )
        self.GetSystemOverview = channel.unary_unary(
                '/center_server.CenterServer/GetSystemOverview',
                request_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
                response_deserializer=center__server__pb2.SystemOverviewResponse.FromString,
                )
        self.GetMetrics = channel.unary_unary(
                '/center_server.CenterServer/GetMetrics',
                request_serializer=center__server__pb2.MetricsRequest.SerializeToString,
                response_deserializer=center__server__pb2.MetricsResponse.FromString,
                )
        self.SendHardwareCommand = channel.unary_unary(
                '/center_server.CenterServer/SendHardwareCommand',
                request_serializer=center__server__pb2.HardwareControlRequest.SerializeToString,
                response_deserializer=center__server__pb2.HardwareControlResponse.FromString,
                )
        self.GetHardwareStatus = channel.unary_unary(
                '/center_server.CenterServer/GetHardwareStatus',
                request_serializer=center__server__pb2.EdgeServerRequest.SerializeToString,
                response_deserializer=center__server__pb2.HardwareStatusResponse.FromString,
                )


class CenterServerServicer(object):
    """中央服务器服务定义
    """

    def RegisterEdgeServer(self, request, context):
        """边缘服务器管理
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def HeartBeat(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetEdgeServerList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetEdgeServerStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateTask(self, request, context):
        """任务管理
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AssignTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateTaskStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTaskList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTaskDetails(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RegisterDataUpload(self, request, context):
        """数据管理
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDataList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDataStatistics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSystemOverview(self, request, context):
        """系统监控
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetMetrics(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendHardwareCommand(self, request, context):
        """硬件控制
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetHardwareStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CenterServerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'RegisterEdgeServer': grpc.unary_unary_rpc_method_handler(
                    servicer.RegisterEdgeServer,
                    request_deserializer=center__server__pb2.EdgeServerInfo.FromString,
                    response_serializer=center__server__pb2.RegisterResponse.SerializeToString,
            ),
            'HeartBeat': grpc.unary_unary_rpc_method_handler(
                    servicer.HeartBeat,
                    request_deserializer=center__server__pb2.HeartBeatRequest.FromString,
                    response_serializer=center__server__pb2.HeartBeatResponse.SerializeToString,
            ),
            'GetEdgeServerList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetEdgeServerList,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=center__server__pb2.EdgeServerListResponse.SerializeToString,
            ),
            'GetEdgeServerStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetEdgeServerStatus,
                    request_deserializer=center__server__pb2.EdgeServerRequest.FromString,
                    response_serializer=center__server__pb2.EdgeServerStatusResponse.SerializeToString,
            ),
            'CreateTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateTask,
                    request_deserializer=center__server__pb2.CreateTaskRequest.FromString,
                    response_serializer=center__server__pb2.TaskResponse.SerializeToString,
            ),
            'AssignTask': grpc.unary_unary_rpc_method_handler(
                    servicer.AssignTask,
                    request_deserializer=center__server__pb2.AssignTaskRequest.FromString,
                    response_serializer=center__server__pb2.TaskResponse.SerializeToString,
            ),
            'UpdateTaskStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateTaskStatus,
                    request_deserializer=center__server__pb2.UpdateTaskStatusRequest.FromString,
                    response_serializer=center__server__pb2.TaskResponse.SerializeToString,
            ),
            'GetTaskList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTaskList,
                    request_deserializer=center__server__pb2.TaskListRequest.FromString,
                    response_serializer=center__server__pb2.TaskListResponse.SerializeToString,
            ),
            'GetTaskDetails': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTaskDetails,
                    request_deserializer=center__server__pb2.TaskRequest.FromString,
                    response_serializer=center__server__pb2.TaskDetailsResponse.SerializeToString,
            ),
            'CancelTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelTask,
                    request_deserializer=center__server__pb2.TaskRequest.FromString,
                    response_serializer=center__server__pb2.TaskResponse.SerializeToString,
            ),
            'RegisterDataUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.RegisterDataUpload,
                    request_deserializer=center__server__pb2.DataUploadInfo.FromString,
                    response_serializer=center__server__pb2.DataResponse.SerializeToString,
            ),
            'GetDataList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDataList,
                    request_deserializer=center__server__pb2.DataListRequest.FromString,
                    response_serializer=center__server__pb2.DataListResponse.SerializeToString,
            ),
            'GetDataStatistics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDataStatistics,
                    request_deserializer=center__server__pb2.DataStatsRequest.FromString,
                    response_serializer=center__server__pb2.DataStatsResponse.SerializeToString,
            ),
            'GetSystemOverview': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSystemOverview,
                    request_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
                    response_serializer=center__server__pb2.SystemOverviewResponse.SerializeToString,
            ),
            'GetMetrics': grpc.unary_unary_rpc_method_handler(
                    servicer.GetMetrics,
                    request_deserializer=center__server__pb2.MetricsRequest.FromString,
                    response_serializer=center__server__pb2.MetricsResponse.SerializeToString,
            ),
            'SendHardwareCommand': grpc.unary_unary_rpc_method_handler(
                    servicer.SendHardwareCommand,
                    request_deserializer=center__server__pb2.HardwareControlRequest.FromString,
                    response_serializer=center__server__pb2.HardwareControlResponse.SerializeToString,
            ),
            'GetHardwareStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetHardwareStatus,
                    request_deserializer=center__server__pb2.EdgeServerRequest.FromString,
                    response_serializer=center__server__pb2.HardwareStatusResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'center_server.CenterServer', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class CenterServer(object):
    """中央服务器服务定义
    """

    @staticmethod
    def RegisterEdgeServer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/RegisterEdgeServer',
            center__server__pb2.EdgeServerInfo.SerializeToString,
            center__server__pb2.RegisterResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def HeartBeat(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/HeartBeat',
            center__server__pb2.HeartBeatRequest.SerializeToString,
            center__server__pb2.HeartBeatResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetEdgeServerList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/GetEdgeServerList',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            center__server__pb2.EdgeServerListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetEdgeServerStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/GetEdgeServerStatus',
            center__server__pb2.EdgeServerRequest.SerializeToString,
            center__server__pb2.EdgeServerStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CreateTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/CreateTask',
            center__server__pb2.CreateTaskRequest.SerializeToString,
            center__server__pb2.TaskResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AssignTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/AssignTask',
            center__server__pb2.AssignTaskRequest.SerializeToString,
            center__server__pb2.TaskResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UpdateTaskStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/UpdateTaskStatus',
            center__server__pb2.UpdateTaskStatusRequest.SerializeToString,
            center__server__pb2.TaskResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetTaskList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/GetTaskList',
            center__server__pb2.TaskListRequest.SerializeToString,
            center__server__pb2.TaskListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetTaskDetails(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/GetTaskDetails',
            center__server__pb2.TaskRequest.SerializeToString,
            center__server__pb2.TaskDetailsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CancelTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/CancelTask',
            center__server__pb2.TaskRequest.SerializeToString,
            center__server__pb2.TaskResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RegisterDataUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/RegisterDataUpload',
            center__server__pb2.DataUploadInfo.SerializeToString,
            center__server__pb2.DataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDataList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/GetDataList',
            center__server__pb2.DataListRequest.SerializeToString,
            center__server__pb2.DataListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDataStatistics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/GetDataStatistics',
            center__server__pb2.DataStatsRequest.SerializeToString,
            center__server__pb2.DataStatsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetSystemOverview(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/GetSystemOverview',
            google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
            center__server__pb2.SystemOverviewResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetMetrics(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/GetMetrics',
            center__server__pb2.MetricsRequest.SerializeToString,
            center__server__pb2.MetricsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SendHardwareCommand(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/SendHardwareCommand',
            center__server__pb2.HardwareControlRequest.SerializeToString,
            center__server__pb2.HardwareControlResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetHardwareStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/center_server.CenterServer/GetHardwareStatus',
            center__server__pb2.EdgeServerRequest.SerializeToString,
            center__server__pb2.HardwareStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)
