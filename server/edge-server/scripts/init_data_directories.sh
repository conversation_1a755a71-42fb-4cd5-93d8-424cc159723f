#!/bin/bash

# 数据目录结构初始化脚本
# 用于创建统一的数据存储目录结构

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${GREEN}=== 初始化统一数据目录结构 ===${NC}"

# 基础数据目录
DATA_ROOT="/data"

# 创建主数据目录
if [ ! -d "$DATA_ROOT" ]; then
    echo -e "${YELLOW}创建主数据目录: $DATA_ROOT${NC}"
    sudo mkdir -p "$DATA_ROOT"
fi

# 摄像头数据目录
echo -e "${GREEN}创建摄像头数据目录...${NC}"
sudo mkdir -p "$DATA_ROOT/camera/snapshots"
sudo mkdir -p "$DATA_ROOT/camera/recordings"
sudo mkdir -p "$DATA_ROOT/camera/playback"
sudo mkdir -p "$DATA_ROOT/camera/temp"

# 激光雷达数据目录
echo -e "${GREEN}创建激光雷达数据目录...${NC}"
sudo mkdir -p "$DATA_ROOT/lidar/point_scans"
sudo mkdir -p "$DATA_ROOT/lidar/terrain_scans"
sudo mkdir -p "$DATA_ROOT/lidar/raw_data"
sudo mkdir -p "$DATA_ROOT/lidar/processed"

# 日志目录
echo -e "${GREEN}创建日志目录...${NC}"
sudo mkdir -p "$DATA_ROOT/logs/edge-server"
sudo mkdir -p "$DATA_ROOT/logs/camera-service"
sudo mkdir -p "$DATA_ROOT/logs/hardware"
sudo mkdir -p "$DATA_ROOT/logs/lidar"
sudo mkdir -p "$DATA_ROOT/logs/system"

# 元数据目录
echo -e "${GREEN}创建元数据目录...${NC}"
sudo mkdir -p "$DATA_ROOT/metadata/camera"
sudo mkdir -p "$DATA_ROOT/metadata/lidar"
sudo mkdir -p "$DATA_ROOT/metadata/tasks"
sudo mkdir -p "$DATA_ROOT/metadata/sync"

# MinIO缓存目录
echo -e "${GREEN}创建MinIO缓存目录...${NC}"
sudo mkdir -p "$DATA_ROOT/minio_cache"
sudo mkdir -p "$DATA_ROOT/minio_cache/upload"
sudo mkdir -p "$DATA_ROOT/minio_cache/download"

# 测试数据目录
echo -e "${GREEN}创建测试数据目录...${NC}"
sudo mkdir -p "$DATA_ROOT/test/camera"
sudo mkdir -p "$DATA_ROOT/test/lidar"
sudo mkdir -p "$DATA_ROOT/test/hardware"

# 临时文件目录
echo -e "${GREEN}创建临时文件目录...${NC}"
sudo mkdir -p "$DATA_ROOT/temp"

# 设置目录权限
echo -e "${GREEN}设置目录权限...${NC}"
# 获取当前用户和组
CURRENT_USER=$(whoami)
CURRENT_GROUP=$(id -gn)

# 设置所有者和权限
sudo chown -R $CURRENT_USER:$CURRENT_GROUP "$DATA_ROOT"
sudo chmod -R 755 "$DATA_ROOT"

# 特殊权限设置
# 日志目录需要写权限
sudo chmod -R 775 "$DATA_ROOT/logs"
# 缓存目录需要写权限
sudo chmod -R 775 "$DATA_ROOT/minio_cache"
# 临时目录需要写权限
sudo chmod -R 775 "$DATA_ROOT/temp"

# 创建目录结构说明文件
cat > "$DATA_ROOT/DIRECTORY_STRUCTURE.md" << 'EOF'
# 数据目录结构说明

## 目录结构
```
/data/
├── camera/                # 摄像头数据
│   ├── snapshots/        # 快照图片
│   ├── recordings/       # 录像文件
│   ├── playback/         # 回放文件
│   └── temp/             # 临时文件
├── lidar/                # 激光雷达数据
│   ├── point_scans/      # 定点扫描数据
│   ├── terrain_scans/    # 地形扫描数据
│   ├── raw_data/         # 原始数据
│   └── processed/        # 处理后数据
├── logs/                 # 日志文件
│   ├── edge-server/      # 边缘服务器日志
│   ├── camera-service/   # 摄像头服务日志
│   ├── hardware/         # 硬件控制日志
│   ├── lidar/            # 激光雷达日志
│   └── system/           # 系统日志
├── metadata/             # 元数据
│   ├── camera/           # 摄像头元数据
│   ├── lidar/            # 激光雷达元数据
│   ├── tasks/            # 任务元数据
│   └── sync/             # 同步元数据
├── minio_cache/          # MinIO缓存
│   ├── upload/           # 上传缓存
│   └── download/         # 下载缓存
├── test/                 # 测试数据
│   ├── camera/           # 摄像头测试数据
│   ├── lidar/            # 激光雷达测试数据
│   └── hardware/         # 硬件测试数据
└── temp/                 # 临时文件
```

## 使用说明
1. 所有服务应使用统一的数据目录结构
2. 避免使用/tmp目录存储重要数据
3. 定期清理temp目录中的临时文件
4. 日志文件应按日期进行轮转
5. MinIO缓存目录会自动管理文件生命周期
EOF

# 创建.gitignore文件
cat > "$DATA_ROOT/.gitignore" << 'EOF'
# 忽略所有数据文件
*
# 但保留目录结构文件
!.gitignore
!DIRECTORY_STRUCTURE.md
!*/
EOF

echo -e "${GREEN}=== 数据目录结构初始化完成 ===${NC}"
echo -e "${GREEN}目录结构已创建在: $DATA_ROOT${NC}"
echo -e "${GREEN}目录权限已设置为: 755 (特殊目录775)${NC}"
echo -e "${GREEN}目录所有者: $CURRENT_USER:$CURRENT_GROUP${NC}"

# 显示目录树
if command -v tree &> /dev/null; then
    echo -e "\n${GREEN}目录结构预览:${NC}"
    tree -L 2 "$DATA_ROOT"
else
    echo -e "\n${GREEN}目录列表:${NC}"
    ls -la "$DATA_ROOT"
fi