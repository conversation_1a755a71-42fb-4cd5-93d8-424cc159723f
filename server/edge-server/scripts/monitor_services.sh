#!/bin/bash
# 服务监控和自动重启脚本

EDGE_DIR="/home/<USER>/server/edge-server"
LOG_DIR="$EDGE_DIR/logs"
MONITOR_LOG="$LOG_DIR/monitor.log"

# 创建日志目录
mkdir -p $LOG_DIR

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $MONITOR_LOG
}

# 检查硬件服务
check_hardware_service() {
    if ! curl -s http://localhost:7080/health > /dev/null 2>&1; then
        log "硬件服务异常，尝试重启..."
        
        # 杀死旧进程
        pkill -f "hardware_control_service.py"
        sleep 2
        
        # 重新启动
        cd $EDGE_DIR/src/modules/hardware
        nohup python hardware_control_service.py > $LOG_DIR/hardware_service.log 2>&1 &
        
        sleep 5
        if curl -s http://localhost:7080/health > /dev/null 2>&1; then
            log "硬件服务重启成功"
        else
            log "硬件服务重启失败"
        fi
    fi
}

# 检查边缘服务器主进程
check_edge_server() {
    if ! pgrep -f "edge-server/src/main.py" > /dev/null; then
        log "边缘服务器主进程异常，尝试重启..."
        
        # 使用启动脚本重启
        cd $EDGE_DIR
        nohup scripts/start_edge_server.sh > $LOG_DIR/edge_server_restart.log 2>&1 &
        
        log "边缘服务器重启命令已执行"
    fi
}

# 主循环
log "服务监控启动"

while true; do
    # 检查各服务
    check_hardware_service
    check_edge_server
    
    # 每30秒检查一次
    sleep 30
done