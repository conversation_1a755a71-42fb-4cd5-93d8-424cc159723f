#!/bin/bash

# 边缘服务器状态检查脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}Edge Server Status Check${NC}"
echo -e "${BLUE}========================================${NC}"
echo ""

# 检查主进程
echo -e "${YELLOW}Main Process:${NC}"
if [ -f "$PROJECT_DIR/edge-server.pid" ]; then
    PID=$(cat "$PROJECT_DIR/edge-server.pid")
    if ps -p $PID > /dev/null 2>&1; then
        echo -e "  ${GREEN}✓${NC} Edge server is running (PID: $PID)"
        
        # 获取进程信息
        ps -p $PID -o pid,vsz,rss,comm,etime
    else
        echo -e "  ${RED}✗${NC} Edge server is not running (stale PID file)"
    fi
else
    # 检查是否有python main.py进程
    PIDS=$(pgrep -f "python main.py")
    if [ -n "$PIDS" ]; then
        echo -e "  ${YELLOW}!${NC} Edge server is running but no PID file"
        echo "  PIDs: $PIDS"
    else
        echo -e "  ${RED}✗${NC} Edge server is not running"
    fi
fi
echo ""

# 检查gRPC端口
echo -e "${YELLOW}gRPC Service:${NC}"
GRPC_PORT=50051
if netstat -tuln 2>/dev/null | grep -q ":$GRPC_PORT "; then
    echo -e "  ${GREEN}✓${NC} gRPC server listening on port $GRPC_PORT"
else
    echo -e "  ${RED}✗${NC} gRPC server not listening on port $GRPC_PORT"
fi
echo ""

# 检查硬件控制服务
echo -e "${YELLOW}Hardware Control Service:${NC}"
HARDWARE_URL="http://localhost:7080/api/hardware/status"
HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" "$HARDWARE_URL" 2>/dev/null)
if [ "$HTTP_CODE" == "200" ]; then
    echo -e "  ${GREEN}✓${NC} Hardware control service is accessible"
else
    echo -e "  ${RED}✗${NC} Hardware control service not accessible (HTTP $HTTP_CODE)"
fi
echo ""

# 检查日志文件
echo -e "${YELLOW}Log Files:${NC}"
LOG_DIR="$PROJECT_DIR/logs"
if [ -d "$LOG_DIR" ]; then
    LOG_FILE="$LOG_DIR/edge-server.log"
    if [ -f "$LOG_FILE" ]; then
        echo -e "  ${GREEN}✓${NC} Log file exists: $LOG_FILE"
        echo "  Last 5 lines:"
        tail -5 "$LOG_FILE" | sed 's/^/    /'
    else
        echo -e "  ${YELLOW}!${NC} No log file found"
    fi
else
    echo -e "  ${RED}✗${NC} Log directory not found"
fi
echo ""

# 检查数据目录
echo -e "${YELLOW}Data Directories:${NC}"
DATA_DIRS=("data/lidar" "data/camera" "data/minio_cache")
for dir in "${DATA_DIRS[@]}"; do
    if [ -d "$PROJECT_DIR/$dir" ]; then
        SIZE=$(du -sh "$PROJECT_DIR/$dir" 2>/dev/null | cut -f1)
        echo -e "  ${GREEN}✓${NC} $dir (Size: $SIZE)"
    else
        echo -e "  ${RED}✗${NC} $dir not found"
    fi
done
echo ""

# 使用gRPC客户端获取详细状态（如果服务器正在运行）
if command -v python &> /dev/null && [ -f "$PROJECT_DIR/grpc/client/edge_client.py" ]; then
    echo -e "${YELLOW}Server Status (via gRPC):${NC}"
    python "$PROJECT_DIR/grpc/client/edge_client.py" --host localhost --port 50051 2>/dev/null | head -20
fi

echo ""
echo -e "${BLUE}========================================${NC}"