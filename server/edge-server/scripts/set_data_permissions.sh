#!/bin/bash

# 数据目录权限设置脚本
# 用于设置和修复/data目录的权限

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== 数据目录权限设置脚本 ===${NC}"

# 检查是否为root或有sudo权限
if [ "$EUID" -ne 0 ] && ! sudo -n true 2>/dev/null; then 
    echo -e "${RED}错误: 需要root权限或sudo权限来执行此脚本${NC}"
    exit 1
fi

# 获取运行服务的用户和组
SERVICE_USER=${SERVICE_USER:-$(whoami)}
SERVICE_GROUP=${SERVICE_GROUP:-$(id -gn)}
DATA_ROOT="/data"

echo -e "${GREEN}服务用户: $SERVICE_USER${NC}"
echo -e "${GREEN}服务组: $SERVICE_GROUP${NC}"

# 检查数据目录是否存在
if [ ! -d "$DATA_ROOT" ]; then
    echo -e "${RED}错误: 数据目录 $DATA_ROOT 不存在${NC}"
    echo -e "${YELLOW}请先运行 init_data_directories.sh 创建目录结构${NC}"
    exit 1
fi

# 设置基础权限
echo -e "\n${GREEN}1. 设置目录所有者...${NC}"
sudo chown -R $SERVICE_USER:$SERVICE_GROUP "$DATA_ROOT"

# 设置目录权限
echo -e "\n${GREEN}2. 设置目录权限...${NC}"

# 基础目录权限 (755 - 用户完全权限，组和其他只读执行)
echo -e "${YELLOW}设置基础目录权限 (755)...${NC}"
sudo find "$DATA_ROOT" -type d -exec chmod 755 {} \;

# 数据写入目录权限 (775 - 用户和组完全权限)
echo -e "${YELLOW}设置数据写入目录权限 (775)...${NC}"
WRITE_DIRS=(
    "$DATA_ROOT/camera"
    "$DATA_ROOT/lidar"
    "$DATA_ROOT/logs"
    "$DATA_ROOT/metadata"
    "$DATA_ROOT/minio_cache"
    "$DATA_ROOT/temp"
    "$DATA_ROOT/test"
)

for dir in "${WRITE_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        sudo chmod -R 775 "$dir"
        echo -e "  ${GREEN}✓${NC} $dir"
    fi
done

# 设置文件权限
echo -e "\n${GREEN}3. 设置文件权限...${NC}"

# 普通文件权限 (644 - 用户读写，其他只读)
echo -e "${YELLOW}设置普通文件权限 (644)...${NC}"
sudo find "$DATA_ROOT" -type f -exec chmod 644 {} \;

# 日志文件权限 (664 - 用户和组读写)
echo -e "${YELLOW}设置日志文件权限 (664)...${NC}"
sudo find "$DATA_ROOT/logs" -type f -exec chmod 664 {} \;

# 可执行文件权限保持不变
echo -e "${YELLOW}保持可执行文件权限...${NC}"
sudo find "$DATA_ROOT" -type f -executable -exec chmod 755 {} \;

# 设置特殊权限
echo -e "\n${GREEN}4. 设置特殊权限...${NC}"

# 设置SGID位，确保新创建的文件继承组权限
echo -e "${YELLOW}设置SGID位...${NC}"
for dir in "${WRITE_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        sudo chmod g+s "$dir"
        echo -e "  ${GREEN}✓${NC} $dir (SGID)"
    fi
done

# 创建权限检查脚本
echo -e "\n${GREEN}5. 创建权限检查脚本...${NC}"
cat > "$DATA_ROOT/check_permissions.sh" << 'EOF'
#!/bin/bash

# 权限检查脚本
DATA_ROOT="/data"

echo "=== 数据目录权限检查 ==="
echo ""

# 检查目录权限
echo "目录权限:"
find "$DATA_ROOT" -maxdepth 3 -type d -exec ls -ld {} \; | sort

echo ""
echo "文件权限统计:"
echo "644权限文件数: $(find "$DATA_ROOT" -type f -perm 644 | wc -l)"
echo "664权限文件数: $(find "$DATA_ROOT" -type f -perm 664 | wc -l)"
echo "755权限文件数: $(find "$DATA_ROOT" -type f -perm 755 | wc -l)"

echo ""
echo "所有者统计:"
find "$DATA_ROOT" -printf "%u:%g\n" | sort | uniq -c

echo ""
echo "大文件检查 (>100MB):"
find "$DATA_ROOT" -type f -size +100M -exec ls -lh {} \;
EOF

sudo chmod 755 "$DATA_ROOT/check_permissions.sh"
sudo chown $SERVICE_USER:$SERVICE_GROUP "$DATA_ROOT/check_permissions.sh"

# 创建定期权限修复的cron任务
echo -e "\n${GREEN}6. 创建定期权限修复任务...${NC}"
CRON_FILE="/etc/cron.daily/fix-data-permissions"
sudo tee "$CRON_FILE" > /dev/null << EOF
#!/bin/bash
# 每日修复数据目录权限

DATA_ROOT="/data"
SERVICE_USER="$SERVICE_USER"
SERVICE_GROUP="$SERVICE_GROUP"

# 修复所有者
chown -R \$SERVICE_USER:\$SERVICE_GROUP "\$DATA_ROOT"

# 修复目录权限
find "\$DATA_ROOT" -type d -exec chmod 755 {} \;

# 修复写入目录权限
for dir in camera lidar logs metadata minio_cache temp test; do
    [ -d "\$DATA_ROOT/\$dir" ] && chmod -R 775 "\$DATA_ROOT/\$dir"
done

# 修复文件权限
find "\$DATA_ROOT" -type f ! -perm 755 -exec chmod 644 {} \;
find "\$DATA_ROOT/logs" -type f -exec chmod 664 {} \;

# 记录修复日志
echo "\$(date): 数据目录权限修复完成" >> "\$DATA_ROOT/logs/permission_fix.log"
EOF

sudo chmod 755 "$CRON_FILE"

# 执行权限检查
echo -e "\n${GREEN}7. 执行权限检查...${NC}"
if [ -x "$DATA_ROOT/check_permissions.sh" ]; then
    bash "$DATA_ROOT/check_permissions.sh"
fi

echo -e "\n${BLUE}=== 权限设置完成 ===${NC}"
echo -e "${GREEN}提示:${NC}"
echo -e "  1. 权限已按照生产环境标准设置"
echo -e "  2. 已创建每日自动修复任务: $CRON_FILE"
echo -e "  3. 可以运行 $DATA_ROOT/check_permissions.sh 检查权限"
echo -e "  4. 日志文件会记录在 $DATA_ROOT/logs/permission_fix.log"