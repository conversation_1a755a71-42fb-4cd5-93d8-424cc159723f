[Unit]
Description=Edge Server Service
After=network.target

[Service]
Type=forking
User=app
WorkingDirectory=/home/<USER>/server/edge-server
Environment="PYTHONPATH=/home/<USER>/server/edge-server"
Environment="EDGE_ID=edge-001"
Environment="CENTER_SERVER_HOST=localhost"
Environment="CENTER_SERVER_PORT=51001"
ExecStart=/home/<USER>/server/edge-server/scripts/start_edge_server.sh
Restart=always
RestartSec=30

[Install]
WantedBy=multi-user.target