#!/bin/bash
# 边缘服务器统一启动脚本
# 支持不同启动模式：full(默认), no-camera, hardware-only

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 启动模式
MODE=${1:-"full"}

echo -e "${BLUE}=== 边缘服务器统一启动脚本 ===${NC}"
echo -e "${BLUE}启动模式: $MODE${NC}"
echo "时间: $(date)"

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
cd "$PROJECT_DIR"

# 加载环境变量
if [ -f ".env" ]; then
    source .env
    echo -e "${GREEN}✓ 已加载环境变量${NC}"
else
    echo -e "${RED}✗ .env文件不存在${NC}"
    exit 1
fi

# 初始化数据目录
echo "初始化数据目录..."
if [ -f "$SCRIPT_DIR/init_data_directories.sh" ]; then
    bash "$SCRIPT_DIR/init_data_directories.sh"
else
    echo -e "${YELLOW}警告: 数据目录初始化脚本不存在${NC}"
fi

# 启动函数
start_hardware_service() {
    echo "检查硬件控制服务..."
    if curl -s http://localhost:7080/health > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 硬件控制服务正常${NC}"
        return 0
    else
        echo -e "${YELLOW}启动硬件控制服务...${NC}"
        bash "$SCRIPT_DIR/start_hardware_service.sh"
        sleep 5
        if curl -s http://localhost:7080/health > /dev/null 2>&1; then
            echo -e "${GREEN}✓ 硬件控制服务启动成功${NC}"
            return 0
        else
            echo -e "${RED}✗ 硬件控制服务启动失败${NC}"
            return 1
        fi
    fi
}

start_camera_service() {
    echo "检查相机服务..."
    if curl -s http://localhost:7090/health > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 相机服务正常${NC}"
        return 0
    else
        echo -e "${YELLOW}启动相机服务...${NC}"
        bash "$SCRIPT_DIR/start_camera_service.sh"
        sleep 5
        if curl -s http://localhost:7090/health > /dev/null 2>&1; then
            echo -e "${GREEN}✓ 相机服务启动成功${NC}"
            return 0
        else
            echo -e "${RED}✗ 相机服务启动失败${NC}"
            return 1
        fi
    fi
}

start_main_service() {
    echo "启动边缘服务器主程序..."

    # 检查是否已经运行
    if pgrep -f "python.*src/main.py" > /dev/null; then
        echo -e "${YELLOW}边缘服务器主程序已在运行${NC}"
        return 0
    fi

    # 启动主程序
    nohup python src/main.py > /data/logs/edge-server/edge_server_main.log 2>&1 &
    EDGE_PID=$!

    echo "边缘服务器PID: $EDGE_PID"

    # 等待启动
    sleep 5

    if ps -p $EDGE_PID > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 边缘服务器主程序启动成功${NC}"
        return 0
    else
        echo -e "${RED}✗ 边缘服务器主程序启动失败${NC}"
        echo "查看日志: tail -50 /data/logs/edge-server/edge_server_main.log"
        return 1
    fi
}

# 根据模式启动服务
case $MODE in
    "full")
        echo -e "${BLUE}完整模式启动：硬件服务 + 相机服务 + 主程序${NC}"
        start_hardware_service && start_camera_service && start_main_service
        ;;
    "no-camera")
        echo -e "${BLUE}无相机模式启动：硬件服务 + 主程序${NC}"
        start_hardware_service && start_main_service
        ;;
    "hardware-only")
        echo -e "${BLUE}仅硬件模式启动：硬件服务${NC}"
        start_hardware_service
        ;;
    *)
        echo -e "${RED}未知启动模式: $MODE${NC}"
        echo "支持的模式: full, no-camera, hardware-only"
        exit 1
        ;;
esac

# 显示启动结果
echo ""
echo -e "${BLUE}=== 启动完成 ===${NC}"
echo "日志文件位置:"
echo "  - 主程序: /data/logs/edge-server/edge_server_main.log"
echo "  - 硬件服务: /data/logs/hardware-service/hardware_service.log"
echo "  - 相机服务: /data/logs/camera-service/camera_service.log"
echo ""
echo "状态检查: bash $SCRIPT_DIR/status_check.sh"
echo "停止服务: bash $SCRIPT_DIR/stop_edge_server.sh"