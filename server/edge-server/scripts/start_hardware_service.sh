#!/bin/bash
# 硬件控制服务启动脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_DIR="$PROJECT_ROOT/logs"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 检查硬件服务是否已经运行
if pgrep -f "hardware_control_service.py" > /dev/null; then
    echo "硬件控制服务已经在运行"
    exit 0
fi

echo "启动硬件控制服务..."

# 启动硬件服务
cd "$PROJECT_ROOT/src/hardware"
nohup python hardware_control_service.py > "$LOG_DIR/hardware_service.log" 2>&1 &

# 等待服务启动
echo "等待服务启动..."
sleep 5

# 检查服务状态
if curl -s http://localhost:7080/health > /dev/null 2>&1; then
    echo "✓ 硬件控制服务启动成功！"
    echo "服务地址: http://localhost:7080"
    echo "日志文件: $LOG_DIR/hardware_service.log"
else
    echo "硬件控制服务启动失败，请检查日志"
    echo "日志内容:"
    tail -10 "$LOG_DIR/hardware_service.log"
    exit 1
fi