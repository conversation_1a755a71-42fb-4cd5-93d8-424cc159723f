#!/bin/bash

# 边缘服务器统一停止脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}=== 停止边缘服务器 ===${NC}"

# Stop edge server
if [ -f edge_server.pid ]; then
    PID=$(cat edge_server.pid)
    if ps -p $PID > /dev/null 2>&1; then
        echo "Stopping Edge Server (PID: $PID)..."
        kill $PID
        sleep 2
        if ps -p $PID > /dev/null 2>&1; then
            echo "Force stopping Edge Server..."
            kill -9 $PID
        fi
        rm -f edge_server.pid
        echo "Edge Server stopped"
    else
        echo "Edge Server not running (stale PID file)"
        rm -f edge_server.pid
    fi
else
    echo "Edge Server PID file not found"
fi

# Stop camera service if running
if [ -f camera_service.pid ]; then
    PID=$(cat camera_service.pid)
    if ps -p $PID > /dev/null 2>&1; then
        echo "Stopping Camera Service (PID: $PID)..."
        kill $PID
        sleep 2
        if ps -p $PID > /dev/null 2>&1; then
            kill -9 $PID
        fi
        rm -f camera_service.pid
        echo "Camera Service stopped"
    fi
fi

# Stop hardware service if running
if [ -f hardware_service.pid ]; then
    PID=$(cat hardware_service.pid)
    if ps -p $PID > /dev/null 2>&1; then
        echo "Stopping Hardware Service (PID: $PID)..."
        kill $PID
        sleep 2
        if ps -p $PID > /dev/null 2>&1; then
            kill -9 $PID
        fi
        rm -f hardware_service.pid
        echo "Hardware Service stopped"
    fi
fi

echo -e "${GREEN}=== 所有服务已停止 ===${NC}"