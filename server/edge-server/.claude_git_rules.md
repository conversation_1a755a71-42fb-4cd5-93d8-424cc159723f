# Claude Code Git 提交规范

## 📋 提交规则

每次Claude Code对话完成后，必须执行Git提交保存修改，便于版本控制和回退。

### 版本号规则
- 基础版本: v1.0 (当前硬件控制系统完成版本)
- 每次修改: +0.1 (v1.1, v1.2, v1.3...)
- 重大更新: +1.0 (v2.0, v3.0...)

### 提交消息格式
```
<类型>(<模块>): <简要描述> - v<版本号>

<详细描述>
- 修改点1
- 修改点2  
- 修改点3

🤖 Generated with [<PERSON> Code](https://claude.ai/code)

Co-Authored-By: <PERSON> <<EMAIL>>
```

### 提交类型
- `feat`: 新功能
- `fix`: 修复bug
- `refactor`: 代码重构
- `docs`: 文档更新
- `test`: 测试相关
- `chore`: 维护性工作
- `perf`: 性能优化
- `style`: 代码格式

### 模块标识
- `hardware`: 硬件控制模块
- `core`: 核心控制层
- `config`: 配置管理
- `test`: 测试相关
- `arch`: 架构调整

## 🔄 自动提交脚本

保存为: `/home/<USER>/edge-server/git_commit_helper.sh`