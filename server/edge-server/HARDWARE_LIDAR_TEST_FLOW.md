# 硬件控制和雷达业务完整测试流程

## 测试环境
- 硬件控制服务端口：7080
- 激光雷达IP：*************
- 串口服务器IP：**************
- IO控制端口：7100
- PTZ控制端口：7200

## 测试准备
1. 确保硬件控制服务已启动
2. 确保串口服务器连接正常
3. 准备观察物理设备响应（继电器声音、云台移动、激光雷达指示灯）

## 详细测试步骤

### 第一部分：IO控制基础测试（步骤1-8）

#### 步骤1：查询初始DO状态
```bash
curl -X GET http://localhost:7080/api/io/status?slave_addr=1
```
**预期结果**：
- 返回所有6个DO的状态
- 初始状态应为全部关闭：`[0, 0, 0, 0, 0, 0]`

#### 步骤2：打开DO1（激光雷达电源）
```bash
curl -X POST http://localhost:7080/api/io/control \
  -H "Content-Type: application/json" \
  -d '{"slave_addr": 1, "do_index": 1, "status": 1}'
```
**预期结果**：
- 返回成功消息："DO1打开成功"
- 听到继电器吸合声音
- DO1物理指示灯亮起

#### 步骤3：查询DO状态验证DO1已开启
```bash
curl -X GET http://localhost:7080/api/io/status?slave_addr=1
```
**预期结果**：
- DO状态应为：`[1, 0, 0, 0, 0, 0]`
- 确认DO1状态为1

#### 步骤4：打开DO2（云台电源）
```bash
curl -X POST http://localhost:7080/api/io/control \
  -H "Content-Type: application/json" \
  -d '{"slave_addr": 1, "do_index": 2, "status": 1}'
```
**预期结果**：
- 返回成功消息："DO2打开成功"
- 听到第二个继电器吸合声音
- 云台开始初始化（可能有自检动作）

#### 步骤5：查询DO状态验证DO1和DO2已开启
```bash
curl -X GET http://localhost:7080/api/io/status?slave_addr=1
```
**预期结果**：
- DO状态应为：`[1, 1, 0, 0, 0, 0]`
- 确认DO1和DO2都为1

#### 步骤6：全部打开测试
```bash
curl -X POST http://localhost:7080/api/io/all \
  -H "Content-Type: application/json" \
  -d '{"slave_addr": 1, "status": 1}'
```
**预期结果**：
- 返回所有DO操作结果
- 听到剩余继电器陆续吸合
- 所有DO指示灯亮起

#### 步骤7：查询验证全部DO已开启
```bash
curl -X GET http://localhost:7080/api/io/status?slave_addr=1
```
**预期结果**：
- DO状态应为：`[1, 1, 1, 1, 1, 1]`
- 所有DO都为1

#### 步骤8：全部关闭测试
```bash
curl -X POST http://localhost:7080/api/io/all \
  -H "Content-Type: application/json" \
  -d '{"slave_addr": 1, "status": 0}'
```
**预期结果**：
- 返回所有DO操作结果
- 听到所有继电器释放声音
- 所有DO指示灯熄灭

### 第二部分：云台控制测试（步骤9-18）

#### 步骤9：重新打开云台电源
```bash
curl -X POST http://localhost:7080/api/io/control \
  -H "Content-Type: application/json" \
  -d '{"slave_addr": 1, "do_index": 2, "status": 1}'
```
**预期结果**：
- DO2打开成功
- 云台开始上电初始化

#### 步骤10：等待云台初始化（30秒）
```bash
echo "等待30秒让云台完成初始化..."
sleep 30
```

#### 步骤11：查询云台初始角度
```bash
curl -X GET http://localhost:7080/api/ptz/angle?ptz_addr=30&axis=all
```
**预期结果**：
- 返回水平(pan)和垂直(tilt)角度
- 初始可能在0度或上次停止位置

#### 步骤12：设置云台速度
```bash
# 设置水平速度为5度/秒
curl -X POST http://localhost:7080/api/ptz/speed \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "pan", "speed": 5.0}'

# 设置垂直速度为3度/秒
curl -X POST http://localhost:7080/api/ptz/speed \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "tilt", "speed": 3.0}'
```
**预期结果**：
- 返回实际设置的速度值
- 注意实际速度可能与请求值略有差异（速度表限制）

#### 步骤13：查询速度验证
```bash
curl -X GET http://localhost:7080/api/ptz/speed?ptz_addr=30&axis=all
```
**预期结果**：
- 显示刚设置的速度值
- pan速度约5.0度/秒，tilt速度约3.0度/秒

#### 步骤14：云台角度追踪测试（0度到180度）
```bash
# 移动到0度
curl -X POST http://localhost:7080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "pan", "angle": 0}'

sleep 3

# 查询当前角度
curl -X GET http://localhost:7080/api/ptz/angle?ptz_addr=30&axis=pan
echo "当前角度：0度"

# 移动到50度
curl -X POST http://localhost:7080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "pan", "angle": 50}'

sleep 5

# 查询当前角度
curl -X GET http://localhost:7080/api/ptz/angle?ptz_addr=30&axis=pan
echo "当前角度：约50度"

# 移动到100度
curl -X POST http://localhost:7080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "pan", "angle": 100}'

sleep 5

# 查询当前角度
curl -X GET http://localhost:7080/api/ptz/angle?ptz_addr=30&axis=pan
echo "当前角度：约100度"

# 移动到150度
curl -X POST http://localhost:7080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "pan", "angle": 150}'

sleep 5

# 查询当前角度
curl -X GET http://localhost:7080/api/ptz/angle?ptz_addr=30&axis=pan
echo "当前角度：约150度"

# 移动到180度
curl -X POST http://localhost:7080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "pan", "angle": 180}'

sleep 5

# 查询最终角度
curl -X GET http://localhost:7080/api/ptz/angle?ptz_addr=30&axis=pan
echo "最终角度：约180度"
```
**预期结果**：
- 云台从0度平滑旋转到180度
- 每次查询显示接近目标的角度值
- 观察到云台物理旋转过程

#### 步骤15：垂直角度测试
```bash
# 设置垂直角度到30度
curl -X POST http://localhost:7080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "tilt", "angle": 30}'

sleep 3

# 查询垂直角度
curl -X GET http://localhost:7080/api/ptz/angle?ptz_addr=30&axis=tilt
```
**预期结果**：
- 云台向上倾斜到30度
- 查询返回约30度

#### 步骤16：设置垂直角度到-30度
```bash
curl -X POST http://localhost:7080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "tilt", "angle": -30}'

sleep 3

# 查询垂直角度
curl -X GET http://localhost:7080/api/ptz/angle?ptz_addr=30&axis=tilt
```
**预期结果**：
- 云台向下倾斜到-30度
- 查询返回约-30度

#### 步骤17：云台归零
```bash
# 水平归零
curl -X POST http://localhost:7080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "pan", "angle": 0}'

# 垂直归零
curl -X POST http://localhost:7080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "tilt", "angle": 0}'

sleep 5

# 查询验证归零
curl -X GET http://localhost:7080/api/ptz/angle?ptz_addr=30&axis=all
```
**预期结果**：
- 云台回到0度位置
- 水平和垂直角度都为0

#### 步骤18：云台停止测试
```bash
curl -X POST http://localhost:7080/api/ptz/stop \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "type": "all"}'
```
**预期结果**：
- 云台立即停止任何运动

### 第三部分：激光雷达连通性测试（步骤19-22）

#### 步骤19：打开激光雷达电源
```bash
curl -X POST http://localhost:7080/api/io/control \
  -H "Content-Type: application/json" \
  -d '{"slave_addr": 1, "do_index": 1, "status": 1}'
```
**预期结果**：
- DO1打开成功
- 激光雷达开始上电

#### 步骤20：等待激光雷达启动（30秒）
```bash
echo "等待30秒让激光雷达完成启动..."
sleep 30
```

#### 步骤21：测试激光雷达连通性
```bash
ping -c 5 *************
```
**预期结果**：
- ping成功，收到回复
- 证明激光雷达网络连接正常

#### 步骤22：查询设备整体状态
```bash
curl -X GET http://localhost:7080/api/device/status
```
**预期结果**：
- 显示DO状态
- 显示云台当前角度
- 确认设备状态正常

### 第四部分：雷达业务功能测试（步骤23-27）

#### 步骤23：定点扫描测试准备
```bash
# 确保激光雷达和云台都已开启
curl -X GET http://localhost:7080/api/io/status?slave_addr=1
```
**预期结果**：
- DO1和DO2都为1
- 设备准备就绪

#### 步骤24：执行定点扫描（模拟）
```bash
# 注意：实际扫描需要通过雷达业务服务执行
# 这里展示业务活跃状态设置，防止保护机制断电

# 设置业务活跃
curl -X POST http://localhost:7080/api/protection/business_active \
  -H "Content-Type: application/json" \
  -d '{"active": true, "business_type": "lidar_point_scan"}'

# 移动云台到扫描位置
curl -X POST http://localhost:7080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "pan", "angle": 45}'

curl -X POST http://localhost:7080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "tilt", "angle": 15}'

sleep 5

echo "模拟定点扫描60秒..."
sleep 60

# 清除业务活跃状态
curl -X POST http://localhost:7080/api/protection/business_active \
  -H "Content-Type: application/json" \
  -d '{"active": false, "business_type": "lidar_point_scan"}'
```
**预期结果**：
- 云台移动到指定位置
- 业务活跃期间设备不会被保护机制断电

#### 步骤25：地形扫描测试准备
```bash
# 设置业务活跃
curl -X POST http://localhost:7080/api/protection/business_active \
  -H "Content-Type: application/json" \
  -d '{"active": true, "business_type": "lidar_terrain_scan"}'

# 云台归零准备扫描
curl -X POST http://localhost:7080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "pan", "angle": 0}'

curl -X POST http://localhost:7080/api/ptz/angle \
  -H "Content-Type: application/json" \
  -d '{"ptz_addr": 30, "axis": "tilt", "angle": 0}'
```

#### 步骤26：模拟地形扫描过程
```bash
# 模拟扫描网格：水平-45到45度，垂直-30到30度
echo "开始地形扫描模拟..."

# 扫描第一行
for h in -45 -30 -15 0 15 30 45; do
  curl -X POST http://localhost:7080/api/ptz/angle \
    -H "Content-Type: application/json" \
    -d "{\"ptz_addr\": 30, \"axis\": \"pan\", \"angle\": $h}"
  
  sleep 2
  
  curl -X GET http://localhost:7080/api/ptz/angle?ptz_addr=30&axis=pan
  echo "扫描位置: 水平=$h度, 垂直=0度"
  sleep 3
done

# 清除业务活跃状态
curl -X POST http://localhost:7080/api/protection/business_active \
  -H "Content-Type: application/json" \
  -d '{"active": false, "business_type": "lidar_terrain_scan"}'
```
**预期结果**：
- 云台按照网格模式移动
- 每个位置停留进行扫描
- 完成扫描流程

#### 步骤27：查询保护状态
```bash
curl -X GET http://localhost:7080/api/protection/status
```
**预期结果**：
- 显示设备通电时间
- 显示剩余安全时间
- 确认保护机制工作正常

### 第五部分：清理和验证（步骤28-30）

#### 步骤28：紧急停机测试
```bash
curl -X POST http://localhost:7080/api/device/emergency_stop
```
**预期结果**：
- 云台立即停止
- 所有DO关闭
- 听到所有继电器释放声音

#### 步骤29：查询最终DO状态
```bash
curl -X GET http://localhost:7080/api/io/status?slave_addr=1
```
**预期结果**：
- DO状态应为：`[0, 0, 0, 0, 0, 0]`
- 所有设备已关闭

#### 步骤30：验证激光雷达断电
```bash
ping -c 3 *************
```
**预期结果**：
- ping失败，无响应
- 证明激光雷达已断电

## 测试总结

### 测试覆盖点
1. **IO控制**：单个DO控制、全部控制、状态查询验证
2. **云台控制**：角度设置、速度设置、角度追踪、查询验证
3. **设备连通性**：激光雷达网络连接验证
4. **业务流程**：定点扫描、地形扫描模拟
5. **保护机制**：业务活跃状态、长期通电保护
6. **紧急处理**：紧急停机功能

### 注意事项
1. 每个控制操作后都要查询验证
2. 云台移动需要时间，要适当等待
3. 激光雷达启动需要30秒左右
4. 业务执行期间要设置活跃状态，防止保护机制断电
5. 测试结束必须关闭所有设备

### 异常处理
- 如果DO状态查询失败，检查串口服务器连接
- 如果云台不响应，检查DO2电源和PTZ地址
- 如果激光雷达ping不通，检查DO1电源和网络配置
- 任何异常情况下，执行紧急停机