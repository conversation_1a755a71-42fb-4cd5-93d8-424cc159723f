# API使用示例

本文档提供了边缘服务器和相机服务的API使用示例，包括curl命令和Python代码。

## 目录

1. [相机服务API示例](#相机服务api示例)
2. [gRPC接口示例](#grpc接口示例)
3. [完整工作流示例](#完整工作流示例)
4. [错误处理示例](#错误处理示例)

## 相机服务API示例

### 基础操作

#### 健康检查

```bash
# curl示例
curl http://localhost:7090/health

# Python示例
import requests

response = requests.get('http://localhost:7090/health')
print(response.json())
```

#### 获取服务状态

```bash
# curl示例
curl http://localhost:7090/api/camera/status

# Python示例
import urllib.request
import json

req = urllib.request.Request('http://localhost:7090/api/camera/status')
with urllib.request.urlopen(req) as response:
    data = json.loads(response.read())
    print(data)
```

### 相机控制

#### 抓拍照片

```bash
# curl示例
curl -X POST http://localhost:7090/api/camera/snapshot \
  -H "Content-Type: application/json" \
  -d '{
    "channel": 33,
    "format": "jpeg"
  }'

# Python示例 - 使用urllib
import urllib.request
import json

data = json.dumps({
    "channel": 33,
    "format": "jpeg"
}).encode('utf-8')

req = urllib.request.Request(
    'http://localhost:7090/api/camera/snapshot',
    data=data,
    headers={'Content-Type': 'application/json'}
)

with urllib.request.urlopen(req) as response:
    result = json.loads(response.read())
    print(f"Snapshot saved to: {result['file_path']}")
```

#### 开始录像

```bash
# curl示例
curl -X POST http://localhost:7090/api/camera/record/start \
  -H "Content-Type: application/json" \
  -d '{
    "channel": 33,
    "duration": 60,
    "quality": "high"
  }'

# Python示例 - 异步客户端
import aiohttp
import asyncio

async def start_recording():
    async with aiohttp.ClientSession() as session:
        data = {
            "channel": 33,
            "duration": 60,
            "quality": "high"
        }
        async with session.post(
            'http://localhost:7090/api/camera/record/start',
            json=data
        ) as resp:
            result = await resp.json()
            print(f"Recording started: {result}")

asyncio.run(start_recording())
```

### PTZ控制

#### 移动控制

```bash
# 向上移动
curl -X POST http://localhost:7090/api/camera/ptz/control \
  -H "Content-Type: application/json" \
  -d '{
    "channel": 33,
    "command": "up",
    "params": {"speed": 5}
  }'

# 停止移动
curl -X POST http://localhost:7090/api/camera/ptz/control \
  -H "Content-Type: application/json" \
  -d '{
    "channel": 33,
    "command": "stop"
  }'
```

#### 预置位调用

```python
# Python示例 - 预置位管理
import urllib.request
import json

class PTZController:
    def __init__(self, base_url='http://localhost:7090'):
        self.base_url = base_url
    
    def call_preset(self, channel, preset_id):
        """调用预置位"""
        data = json.dumps({
            "channel": channel,
            "action": "call",
            "preset_id": preset_id
        }).encode('utf-8')
        
        req = urllib.request.Request(
            f'{self.base_url}/api/camera/ptz/preset',
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        with urllib.request.urlopen(req) as response:
            return json.loads(response.read())
    
    def set_preset(self, channel, preset_id):
        """设置预置位"""
        data = json.dumps({
            "channel": channel,
            "action": "set",
            "preset_id": preset_id
        }).encode('utf-8')
        
        req = urllib.request.Request(
            f'{self.base_url}/api/camera/ptz/preset',
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        with urllib.request.urlopen(req) as response:
            return json.loads(response.read())

# 使用示例
ptz = PTZController()
ptz.call_preset(33, 1)  # 调用通道33的预置位1
```

#### 绝对定位

```bash
# curl示例
curl -X POST http://localhost:7090/api/camera/ptz/position \
  -H "Content-Type: application/json" \
  -d '{
    "channel": 33,
    "pan": 45.0,
    "tilt": 30.0,
    "zoom": 1.0
  }'
```

### 流媒体获取

```python
# Python示例 - 获取RTSP流地址
import urllib.request
import json

def get_rtsp_stream(channel, stream_type='main'):
    data = json.dumps({
        "channel": channel,
        "stream_type": stream_type
    }).encode('utf-8')
    
    req = urllib.request.Request(
        'http://localhost:7090/api/camera/stream',
        data=data,
        headers={'Content-Type': 'application/json'}
    )
    
    with urllib.request.urlopen(req) as response:
        result = json.loads(response.read())
        return result['rtsp_url']

# 获取主码流
rtsp_url = get_rtsp_stream(33, 'main')
print(f"RTSP URL: {rtsp_url}")

# 使用OpenCV播放流
import cv2
cap = cv2.VideoCapture(rtsp_url)
while True:
    ret, frame = cap.read()
    if ret:
        cv2.imshow('Stream', frame)
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
cap.release()
cv2.destroyAllWindows()
```

## gRPC接口示例

### 设置gRPC客户端

```python
import grpc
import sys
sys.path.append('/home/<USER>/server/edge-server/proto')
import edge_server_pb2
import edge_server_pb2_grpc

# 创建连接
channel = grpc.insecure_channel('localhost:50051')
stub = edge_server_pb2_grpc.EdgeServerStub(channel)
```

### 系统状态查询

```python
# 获取系统状态
def get_system_status():
    request = edge_server_pb2.Empty()
    response = stub.GetSystemStatus(request)
    
    print(f"System ID: {response.system_id}")
    print(f"Status: {response.status}")
    print(f"Uptime: {response.uptime_seconds}s")
    
    print("\nModules:")
    for module in response.modules:
        print(f"  - {module.name}: {module.status}")

get_system_status()
```

### 任务管理

```python
# 提交相机任务
def submit_camera_task():
    task = edge_server_pb2.Task(
        type="camera",
        action="snapshot",
        parameters={
            "channel": "33",
            "format": "jpeg"
        }
    )
    
    response = stub.SubmitTask(task)
    print(f"Task ID: {response.task_id}")
    print(f"Status: {response.status}")
    return response.task_id

# 查询任务状态
def get_task_status(task_id):
    request = edge_server_pb2.TaskQuery(task_id=task_id)
    response = stub.GetTaskStatus(request)
    
    print(f"Task {task_id}:")
    print(f"  Status: {response.status}")
    print(f"  Progress: {response.progress}%")
    if response.error:
        print(f"  Error: {response.error}")

# 使用示例
task_id = submit_camera_task()
import time
time.sleep(2)
get_task_status(task_id)
```

### 硬件控制

```python
# 控制设备电源
def control_power(device, action):
    request = edge_server_pb2.PowerControl(
        device=device,
        action=action
    )
    
    response = stub.ControlPower(request)
    print(f"Power control result: {response.success}")
    if response.message:
        print(f"Message: {response.message}")

# 打开激光雷达电源
control_power("lidar", "on")

# 关闭所有设备
control_power("all", "off")
```

## 完整工作流示例

### 地形扫描任务

```python
import time
import json

class TerrainScanWorkflow:
    def __init__(self):
        self.camera_base = 'http://localhost:7090'
        self.grpc_channel = grpc.insecure_channel('localhost:50051')
        self.grpc_stub = edge_server_pb2_grpc.EdgeServerStub(self.grpc_channel)
    
    def execute_scan(self, scan_points):
        """执行完整的地形扫描流程"""
        
        # 1. 打开设备电源
        print("1. 启动设备...")
        self._control_power("lidar", "on")
        self._control_power("ptz", "on")
        time.sleep(30)  # 等待设备启动
        
        # 2. 开始录像
        print("2. 开始录像...")
        self._start_recording(duration=300)
        
        # 3. 执行扫描
        print("3. 执行扫描...")
        for i, point in enumerate(scan_points):
            print(f"   扫描点 {i+1}/{len(scan_points)}: {point}")
            
            # 移动到指定位置
            self._move_ptz(point['pan'], point['tilt'])
            time.sleep(5)  # 等待稳定
            
            # 抓拍照片
            self._take_snapshot(f"scan_point_{i+1}")
            
            # 采集激光雷达数据
            self._collect_lidar_data(duration=10)
        
        # 4. 停止录像
        print("4. 停止录像...")
        self._stop_recording()
        
        # 5. 关闭设备
        print("5. 关闭设备...")
        self._control_power("all", "off")
        
        print("扫描完成!")
    
    def _control_power(self, device, action):
        request = edge_server_pb2.PowerControl(
            device=device,
            action=action
        )
        response = self.grpc_stub.ControlPower(request)
        return response.success
    
    def _start_recording(self, duration):
        data = json.dumps({
            "channel": 33,
            "duration": duration,
            "quality": "high"
        }).encode('utf-8')
        
        req = urllib.request.Request(
            f'{self.camera_base}/api/camera/record/start',
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        with urllib.request.urlopen(req) as response:
            return json.loads(response.read())
    
    def _stop_recording(self):
        data = json.dumps({"channel": 33}).encode('utf-8')
        
        req = urllib.request.Request(
            f'{self.camera_base}/api/camera/record/stop',
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        with urllib.request.urlopen(req) as response:
            return json.loads(response.read())
    
    def _move_ptz(self, pan, tilt):
        data = json.dumps({
            "channel": 33,
            "pan": pan,
            "tilt": tilt,
            "zoom": 1.0
        }).encode('utf-8')
        
        req = urllib.request.Request(
            f'{self.camera_base}/api/camera/ptz/position',
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        with urllib.request.urlopen(req) as response:
            return json.loads(response.read())
    
    def _take_snapshot(self, tag):
        data = json.dumps({
            "channel": 33,
            "format": "jpeg",
            "tag": tag
        }).encode('utf-8')
        
        req = urllib.request.Request(
            f'{self.camera_base}/api/camera/snapshot',
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        with urllib.request.urlopen(req) as response:
            return json.loads(response.read())
    
    def _collect_lidar_data(self, duration):
        task = edge_server_pb2.Task(
            type="lidar",
            action="point_scan",
            parameters={
                "duration": str(duration),
                "frequency": "10"
            }
        )
        
        response = self.grpc_stub.SubmitTask(task)
        return response.task_id

# 使用示例
if __name__ == "__main__":
    workflow = TerrainScanWorkflow()
    
    # 定义扫描点
    scan_points = [
        {"pan": 0, "tilt": 0},
        {"pan": 45, "tilt": 30},
        {"pan": 90, "tilt": 45},
        {"pan": 135, "tilt": 30},
        {"pan": 180, "tilt": 0}
    ]
    
    workflow.execute_scan(scan_points)
```

## 错误处理示例

### HTTP错误处理

```python
import urllib.request
import urllib.error
import json

def safe_api_call(url, data=None, method='GET'):
    """安全的API调用，包含错误处理"""
    try:
        if data:
            data = json.dumps(data).encode('utf-8')
            req = urllib.request.Request(
                url,
                data=data,
                headers={'Content-Type': 'application/json'}
            )
        else:
            req = urllib.request.Request(url)
        
        with urllib.request.urlopen(req, timeout=10) as response:
            return {
                'success': True,
                'data': json.loads(response.read()),
                'status_code': response.getcode()
            }
    
    except urllib.error.HTTPError as e:
        return {
            'success': False,
            'error': f'HTTP Error {e.code}: {e.reason}',
            'status_code': e.code
        }
    
    except urllib.error.URLError as e:
        return {
            'success': False,
            'error': f'URL Error: {e.reason}',
            'status_code': None
        }
    
    except Exception as e:
        return {
            'success': False,
            'error': f'Unexpected error: {str(e)}',
            'status_code': None
        }

# 使用示例
result = safe_api_call('http://localhost:7090/api/camera/snapshot', {
    'channel': 33,
    'format': 'jpeg'
}, 'POST')

if result['success']:
    print(f"Success: {result['data']}")
else:
    print(f"Error: {result['error']}")
```

### gRPC错误处理

```python
import grpc

def safe_grpc_call(stub_method, request):
    """安全的gRPC调用，包含错误处理"""
    try:
        response = stub_method(request)
        return {
            'success': True,
            'data': response
        }
    
    except grpc.RpcError as e:
        return {
            'success': False,
            'error': f'gRPC Error: {e.code()} - {e.details()}',
            'code': e.code()
        }
    
    except Exception as e:
        return {
            'success': False,
            'error': f'Unexpected error: {str(e)}',
            'code': None
        }

# 使用示例
request = edge_server_pb2.Empty()
result = safe_grpc_call(stub.GetSystemStatus, request)

if result['success']:
    print(f"System status: {result['data'].status}")
else:
    print(f"Error: {result['error']}")
```

### 重试机制

```python
import time

def retry_api_call(func, max_retries=3, delay=1.0, backoff=2.0):
    """带重试机制的API调用"""
    last_error = None
    
    for attempt in range(max_retries):
        try:
            result = func()
            if result.get('success'):
                return result
            else:
                last_error = result.get('error', 'Unknown error')
        
        except Exception as e:
            last_error = str(e)
        
        if attempt < max_retries - 1:
            wait_time = delay * (backoff ** attempt)
            print(f"Retry {attempt + 1}/{max_retries} after {wait_time}s...")
            time.sleep(wait_time)
    
    return {
        'success': False,
        'error': f'Failed after {max_retries} attempts: {last_error}'
    }

# 使用示例
def take_snapshot():
    return safe_api_call(
        'http://localhost:7090/api/camera/snapshot',
        {'channel': 33, 'format': 'jpeg'},
        'POST'
    )

result = retry_api_call(take_snapshot)
print(result)
```

## 批量操作示例

### 批量抓拍

```python
import asyncio
import aiohttp

async def batch_snapshot(channels, session):
    """批量抓拍多个通道"""
    tasks = []
    
    for channel in channels:
        task = session.post(
            'http://localhost:7090/api/camera/snapshot',
            json={'channel': channel, 'format': 'jpeg'}
        )
        tasks.append(task)
    
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    processed_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            processed_results.append({
                'channel': channels[i],
                'success': False,
                'error': str(result)
            })
        else:
            data = await result.json()
            processed_results.append({
                'channel': channels[i],
                'success': True,
                'data': data
            })
    
    return processed_results

async def main():
    channels = [33, 34, 35, 36]
    
    async with aiohttp.ClientSession() as session:
        results = await batch_snapshot(channels, session)
        
        for result in results:
            if result['success']:
                print(f"Channel {result['channel']}: {result['data']['file_path']}")
            else:
                print(f"Channel {result['channel']}: Failed - {result['error']}")

# 运行批量操作
asyncio.run(main())
```

## 性能测试示例

```python
import time
import statistics

def performance_test(func, iterations=100):
    """测试API性能"""
    response_times = []
    errors = 0
    
    for i in range(iterations):
        start_time = time.time()
        
        try:
            result = func()
            if not result.get('success'):
                errors += 1
        except Exception:
            errors += 1
        
        response_time = time.time() - start_time
        response_times.append(response_time)
        
        if (i + 1) % 10 == 0:
            print(f"Progress: {i + 1}/{iterations}")
    
    # 计算统计信息
    avg_time = statistics.mean(response_times)
    min_time = min(response_times)
    max_time = max(response_times)
    std_dev = statistics.stdev(response_times)
    success_rate = (iterations - errors) / iterations * 100
    
    print(f"\nPerformance Test Results:")
    print(f"  Total requests: {iterations}")
    print(f"  Success rate: {success_rate:.1f}%")
    print(f"  Average time: {avg_time:.3f}s")
    print(f"  Min time: {min_time:.3f}s")
    print(f"  Max time: {max_time:.3f}s")
    print(f"  Std deviation: {std_dev:.3f}s")

# 测试健康检查接口
def health_check():
    return safe_api_call('http://localhost:7090/health')

performance_test(health_check, iterations=100)
```