# 数据目录结构规范

## 概述

本文档定义了边缘服务器的统一数据存储目录结构，所有数据统一存储在 `/data` 目录下。

## 目录结构

```
/data/                          # 根数据目录
├── camera/                     # 相机数据
│   ├── snapshots/             # 抓拍图片
│   ├── recordings/            # 录像文件
│   ├── playback/              # 回放文件
│   └── temp/                  # 临时文件
│
├── lidar/                     # 激光雷达数据
│   ├── scans/                 # 定点扫描数据
│   ├── terrain/               # 地形扫描数据
│   └── temp/                  # 临时文件
│
├── logs/                      # 日志文件
│   ├── edge-server/           # 边缘服务器日志
│   ├── camera-service/        # 相机服务日志
│   └── hardware-service/      # 硬件服务日志
│
├── metadata/                  # 元数据
│   ├── camera/               # 相机元数据
│   └── lidar/                # 激光雷达元数据
│
├── minio_cache/              # MinIO缓存
└── temp/                     # 全局临时文件
```

## 路径映射

### 旧路径 → 新路径

- `/tmp/camera_data` → `/data/camera/temp`
- `/tmp/lidar_scan/{task_id}` → `/data/lidar/scans/{task_id}` 或 `/data/lidar/terrain/{task_id}`
- `/tmp/minio_cache_edge-001` → `/data/minio_cache`
- `/tmp/test.txt` → `/data/temp/test.txt`

### MinIO对象路径规则

上传到MinIO时的对象路径规则：

1. **相机数据**
   - 抓拍: `{edge_id}/camera/snapshots/{date}/{timestamp}_{filename}`
   - 录像: `{edge_id}/camera/recordings/{date}/{timestamp}_{filename}`

2. **激光雷达数据**
   - 定点扫描: `{edge_id}/lidar/scans/{task_id}/{timestamp}_{filename}`
   - 地形扫描: `{edge_id}/lidar/terrain/{task_id}/{timestamp}_{filename}`

3. **任务数据**
   - 通用格式: `{edge_id}/tasks/{task_id}/{data_type}/{timestamp}_{filename}`

## 权限设置

所有数据目录的权限设置：
- 所有者: `app:app`
- 权限: `755` (rwxr-xr-x)

## 环境变量

系统定义了以下环境变量（见 `.env.data` 文件）：

```bash
DATA_BASE=/data
CAMERA_DATA_DIR=/data/camera
LIDAR_DATA_DIR=/data/lidar
LOG_BASE_DIR=/data/logs
METADATA_DIR=/data/metadata
MINIO_CACHE_DIR=/data/minio_cache
TEMP_DIR=/data/temp
```

## 初始化和维护

### 初始化数据目录

```bash
# 运行数据目录初始化脚本
sudo bash scripts/init_data_dirs.sh
```

### 数据迁移

如果需要从旧的 `/tmp` 目录迁移数据：

```bash
# 运行数据迁移脚本
sudo bash scripts/migrate_data.sh
```

### 清理临时文件

建议定期清理临时目录：

```bash
# 清理超过7天的临时文件
find /data/*/temp -type f -mtime +7 -delete
```

## 配置更新

确保以下配置文件已更新为使用新的数据路径：

1. `/home/<USER>/server/edge-server/config/config.yaml`
2. `/home/<USER>/server/edge-server/camera-service/config/config.yaml`
3. 启动脚本中的日志路径

## 注意事项

1. 所有服务必须使用 `/data` 目录存储数据，不再使用 `/tmp`
2. 服务启动前会自动初始化数据目录
3. 确保 `/data` 目录有足够的存储空间
4. 定期备份重要数据到中央服务器