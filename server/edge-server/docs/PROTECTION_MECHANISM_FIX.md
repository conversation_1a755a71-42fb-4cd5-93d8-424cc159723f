# 保护机制修复文档

## 问题分析

### 1. DO查询超时问题
- **现象**：从凌晨2:20开始，DO状态查询一直超时
- **原因**：串口服务器响应延迟或网络问题
- **影响**：保护机制依赖DO状态查询，查询失败导致无法正确判断设备状态

### 2. 业务激活后永不超时
- **现象**：2:27:18激活业务后，激光雷达运行了7.5小时没有自动关闭
- **原因**：`business_active=True`时会完全跳过保护检查，且没有时限
- **影响**：设备可能无限期运行，存在安全隐患

### 3. 通电时间记录缺失
- **现象**：通过API控制DO时没有记录通电开始时间
- **原因**：`io_control` API只调用了`relay_service.control_do`，没有更新时间记录
- **影响**：保护机制无法知道设备已通电多久

## 修复方案

### 1. 增加业务超时机制
```python
# 业务活动最大时长：10分钟
MAX_BUSINESS_DURATION = 600

# 记录业务开始时间
if active:
    self.business_start_time = current_time
    logger.info(f"业务状态变更: {business_type} 激活（最长10分钟自动结束）")
```

### 2. 业务期间仍执行保护检查
```python
# 业务期间使用更长的保护时限（30分钟）
if self.business_active:
    await self._do_protection_check(1800)  # 30分钟
else:
    await self._do_protection_check(self.MAX_POWER_ON_TIME)  # 60秒
```

### 3. DO控制自动记录通电时间
```python
# 在io_control API中添加
if success:
    await self.hardware_manager._update_power_on_time(do_index, status)
```

## 修复后的行为

1. **业务活动限制**
   - 最长持续10分钟
   - 超时自动结束
   - 结束时重置设备计时

2. **分级保护**
   - 无业务：60秒保护
   - 有业务：30分钟保护
   - 始终执行检查

3. **可靠的时间记录**
   - DO控制自动记录
   - 不依赖查询结果
   - 缓存状态优先

## 配置参数

| 参数 | 值 | 说明 |
|-----|-----|-----|
| 检查间隔 | 30秒 | 保护机制检查频率 |
| 无业务保护时限 | 60秒 | 测试环境快速保护 |
| 业务期间保护时限 | 30分钟 | 防止异常长时间运行 |
| 业务最大时长 | 10分钟 | 自动结束超时业务 |

## 验证方法

1. **测试业务超时**
   ```bash
   # 激活业务
   curl -X POST http://localhost:7080/api/protection/business_active \
        -d '{"business_name": "test", "active": true}'
   
   # 等待10分钟后应自动结束
   ```

2. **测试保护机制**
   ```bash
   # 打开设备
   curl -X POST http://localhost:7080/api/io/control \
        -d '{"do_index": 1, "status": 1}'
   
   # 查看保护状态
   curl http://localhost:7080/api/protection/status
   ```

## 日志监控

关键日志信息：
- `业务状态变更: xxx 激活（最长10分钟自动结束）`
- `业务超时（x.x分钟），自动结束`
- `激光雷达(DO1) 首次检测到通电，开始计时`
- `业务活跃中（x.x分钟），但仍检查设备状态`

## 后续建议

1. **生产环境配置**
   - 调整保护时限为实际需求（如10分钟）
   - 增加配置文件参数
   - 支持动态调整

2. **监控告警**
   - 添加设备长时间运行告警
   - 业务超时告警
   - DO查询失败告警

3. **日志优化**
   - 减少重复的超时日志
   - 增加统计信息
   - 定期清理日志

---

修复已部署，保护机制现在更加可靠和安全。