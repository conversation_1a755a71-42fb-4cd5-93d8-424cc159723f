# 边缘服务器优化 - 快速启动检查清单

## 立即执行（第1天）

### 1. 环境准备
- [ ] 创建优化分支：`git checkout -b optimization-phase1`
- [ ] 备份当前系统：`./scripts/backup_current_system.sh`
- [ ] 记录当前性能基准：`./scripts/record_baseline.sh`

### 2. 冗余文件清理脚本
```bash
#!/bin/bash
# cleanup_redundancy.sh

echo "开始清理冗余文件..."

# 1. 删除重复的hikvision目录
echo "清理重复的hikvision目录..."
rm -rf /home/<USER>/server/edge-server/src/modules/camera/hikvision

# 2. 清理所有__pycache__目录
echo "清理__pycache__目录..."
find /home/<USER>/server -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null

# 3. 清理过时的日志文件
echo "清理过期日志..."
find /home/<USER>/server -name "*.log" -mtime +30 -delete

# 4. 清理测试结果
echo "清理旧测试结果..."
find /home/<USER>/server/test_results -name "*.json" -mtime +7 -delete

# 5. 显示清理结果
echo "清理完成！"
echo "释放空间："
du -sh /home/<USER>/server/edge-server/
```

### 3. 性能基准测试
```bash
#!/bin/bash
# record_baseline.sh

echo "记录性能基准..."

# CPU和内存使用
echo "=== 系统资源使用 ===" > baseline_metrics.txt
top -b -n 1 | head -20 >> baseline_metrics.txt

# 服务响应时间
echo -e "\n=== API响应时间 ===" >> baseline_metrics.txt
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:5001/health >> baseline_metrics.txt

# 数据处理速度
echo -e "\n=== 数据处理性能 ===" >> baseline_metrics.txt
python3 test_performance.py >> baseline_metrics.txt

echo "基准测试完成，结果保存在 baseline_metrics.txt"
```

## 第一周任务分配

### Day 1-2: 冗余清理
**负责人**: DevOps工程师
- [ ] 执行清理脚本
- [ ] 验证服务正常运行
- [ ] 更新.gitignore文件
- [ ] 提交清理结果

### Day 3-4: 测试环境
**负责人**: 测试工程师 + DevOps
- [ ] 搭建独立测试环境
- [ ] 部署监控工具（Prometheus + Grafana）
- [ ] 准备自动化测试脚本
- [ ] 创建回滚方案

### Day 5: 基准和规划
**负责人**: 架构师 + 全体
- [ ] 完成性能基准测试
- [ ] 评审优化方案
- [ ] 分配Phase 2任务
- [ ] 准备技术预研

## 关键检查点

### 每日检查（17:00）
1. 今日完成的任务
2. 遇到的问题和风险
3. 明日计划
4. 需要的支持

### 周末评审
1. 本周目标达成情况
2. 性能指标对比
3. 下周计划确认
4. 风险和问题汇总

## 紧急联系方式

- **项目负责人**: 张三 (13800138000)
- **技术架构师**: 李四 (13900139000)
- **DevOps值班**: 王五 (13700137000)

## 常用命令速查

```bash
# 查看服务状态
docker ps -a | grep edge
systemctl status hardware-control

# 快速重启服务
./scripts/restart_edge_server.sh
./scripts/restart_hardware_control.sh

# 查看日志
tail -f logs/edge-server.log
tail -f logs/hardware-control.log

# 性能监控
htop
iotop
nethogs

# 回滚命令
git checkout phase0-baseline
docker-compose down && docker-compose up -d
```

## 风险预案

### 如果清理导致服务异常
1. 立即停止清理：`Ctrl+C`
2. 恢复备份：`./scripts/restore_backup.sh`
3. 检查服务状态：`./scripts/check_all_services.sh`
4. 联系架构师

### 如果性能下降
1. 对比基准指标
2. 检查资源使用
3. 回滚最近更改
4. 启动应急预案

---

**记住**：宁可慢一点，也要稳一点！