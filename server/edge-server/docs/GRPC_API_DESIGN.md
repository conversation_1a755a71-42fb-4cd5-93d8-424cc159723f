# gRPC接口设计文档

## 概述

本文档描述了边缘服务器的gRPC接口设计，包括认证机制、所有业务接口的定义和实现细节。

## 版本信息
- 版本：v2.23
- 更新日期：2025-07-24
- 作者：Claude

## 目录
1. [认证机制](#认证机制)
2. [系统管理接口](#系统管理接口)
3. [任务管理接口](#任务管理接口)
4. [硬件控制接口](#硬件控制接口)
5. [数据采集接口](#数据采集接口)
6. [数据管理接口](#数据管理接口)
7. [设备状态接口](#设备状态接口)
8. [事件订阅接口](#事件订阅接口)
9. [与中央服务器的集成](#与中央服务器的集成)

## 认证机制

### 1. 认证流程

```
客户端                     边缘服务器
  |                           |
  |------ Authenticate ------>|
  |       (token)             |
  |                           |
  |<----- AuthResponse -------|
  |       (session_id)        |
  |                           |
  |------ 业务请求 ----------->|
  |  (携带session_id)         |
  |                           |
```

### 2. 认证接口

```protobuf
rpc Authenticate(AuthRequest) returns (AuthResponse);

message AuthRequest {
    string token = 1;          // 认证令牌
    string edge_id = 2;        // 边缘服务器ID
    map<string, string> metadata = 3;  // 额外元数据
}

message AuthResponse {
    bool success = 1;
    string session_id = 2;     // 会话ID
    string message = 3;
    int64 expires_at = 4;      // 过期时间戳
}
```

### 3. 认证配置

在`config.yaml`中配置认证令牌：

```yaml
auth_tokens:
  test_token_123: "Test Client"
  prod_token_456: "Production Client"
```

## 系统管理接口

### 1. GetSystemStatus - 获取系统状态

**描述**：获取边缘服务器的整体健康状态和性能指标。

**认证**：不需要

**请求**：
```protobuf
google.protobuf.Empty
```

**响应**：
```protobuf
message SystemStatusResponse {
    bool healthy = 1;
    map<string, ModuleStatus> modules = 2;
    TaskBusStatus task_bus = 3;
    SystemMetrics metrics = 4;
}
```

**示例响应**：
```json
{
  "healthy": true,
  "modules": {
    "camera": {"name": "camera", "status": "idle", "healthy": true},
    "lidar": {"name": "lidar", "status": "idle", "healthy": true}
  },
  "task_bus": {
    "pending_tasks": 0,
    "running_tasks": 1,
    "completed_tasks": 10,
    "success_rate": 0.95
  },
  "metrics": {
    "cpu_percent": 25.5,
    "memory_percent": 45.2,
    "disk_percent": 60.0
  }
}
```

### 2. GetModuleList - 获取模块列表

**描述**：获取所有可用模块及其支持的功能。

**认证**：需要

**响应**：包含所有模块的名称、状态、支持的动作和特性。

## 任务管理接口

### 1. SubmitTask - 提交任务

**描述**：提交一个新任务到任务队列。

**请求参数**：
- `type`: 任务类型（control, collect_data, upload, query）
- `target`: 目标模块（hardware, lidar, camera, minio）
- `action`: 具体动作
- `params`: 参数字典
- `priority`: 优先级（可选）
- `timeout`: 超时时间（可选）

### 2. GetTaskStatus - 获取任务状态

**描述**：查询特定任务的执行状态和进度。

### 3. CancelTask - 取消任务

**描述**：取消正在执行或等待执行的任务。

### 4. ListTasks - 列出任务

**描述**：获取任务列表，支持按状态过滤和分页。

## 硬件控制接口

### 1. ControlPower - 电源控制

**描述**：控制设备电源的开关。

**支持的设备**：
- `lidar` - 激光雷达
- `ptz` - 云台
- `camera1` - 摄像头1
- `camera2` - 摄像头2

**支持的动作**：
- `on` - 打开电源
- `off` - 关闭电源
- `cycle` - 电源循环（关闭后重新打开）

### 2. ControlPTZ - 云台控制

**描述**：控制PTZ云台的移动和定位。

**参数**：
- `horizontal`: 水平角度（-180到180度）
- `vertical`: 垂直角度（-90到90度）
- `zoom`: 缩放级别（可选）
- `speed`: 移动速度（0-100）
- `action`: 动作类型（goto, move, stop）

### 3. EmergencyShutdown - 紧急关闭

**描述**：紧急关闭所有设备，最高优先级执行。

## 数据采集接口

### 1. StartLidarScan - 开始激光雷达扫描

**描述**：启动激光雷达扫描任务。

**扫描类型**：
- `point_scan` - 定点扫描（10Hz实时流）
- `terrain_scan` - 地形扫描（多角度累积）

**参数**：
```protobuf
message LidarScanRequest {
    string scan_type = 1;     
    int32 duration = 2;       // 扫描时长（秒）
    Position position = 3;    // 扫描位置
    ScanArea scan_area = 4;   // 扫描区域（地形扫描）
    float resolution = 5;     // 分辨率（度）
    string task_name = 6;     // 任务名称
    map<string, string> metadata = 7;  // 元数据
}
```

### 2. StartCameraRecording - 开始摄像头录制

**描述**：启动摄像头录制任务。

**支持的通道**：
- `ptz1` - PTZ摄像头1（通道1）
- `ptz2` - PTZ摄像头2（通道3）
- `fixed1` - 固定摄像头1（通道2）
- `fixed2` - 固定摄像头2（通道4）

**录制选项**：
```protobuf
message RecordingOptions {
    string quality = 1;       // high, medium, low
    bool with_audio = 2;      // 是否录制音频
    string format = 3;        // mp4, avi等
}
```

### 3. TakeSnapshot - 拍摄快照

**描述**：拍摄单张照片。

**参数**：
- `channel`: 摄像头通道
- `position`: PTZ位置（可选）
- `quality`: 图片质量（high, medium, low）
- `format`: 图片格式（jpg, png）

## 数据管理接口

### 1. ListStoredData - 列出存储数据

**描述**：查询本地存储的数据文件。

**支持的过滤和排序**：
- 按数据类型过滤（lidar, camera, all）
- 按日期过滤（YYYYMMDD格式）
- 排序方式（created_at, size, name）
- 分页（offset, limit）

### 2. GetUploadStatus - 获取上传状态

**描述**：查询MinIO数据上传状态。

**响应信息**：
- 总文件数
- 已上传文件数
- 失败文件数
- 总字节数
- 已上传字节数
- 各上传任务详情

### 3. DeleteData - 删除数据

**描述**：删除指定的数据文件。

**参数**：
- `file_ids`: 要删除的文件ID列表
- `force`: 是否强制删除（包括已上传的）

### 4. GetDataDetails - 获取数据详情

**描述**：获取特定数据文件的详细信息。

## 设备状态接口

### 1. GetDeviceStatus - 获取设备状态

**描述**：查询硬件设备的当前状态。

**设备信息包括**：
- 设备ID和类型
- 在线状态
- 电源状态
- 设备属性（IP地址、型号等）
- 当前执行的任务
- 最后活跃时间

### 2. ListDevices - 列出所有设备

**描述**：获取所有硬件设备的列表。

## 事件订阅接口

### SubscribeEvents - 事件订阅（双向流）

**描述**：订阅系统事件，实时接收事件通知。

**支持的事件类型**：
- `task.*` - 任务相关事件
- `system.*` - 系统相关事件
- `data.*` - 数据相关事件
- `device.*` - 设备相关事件

**事件结构**：
```protobuf
message Event {
    string event_id = 1;           
    string event_type = 2;         
    google.protobuf.Timestamp timestamp = 3;
    string source = 4;             
    string severity = 5;           // info, warning, error, critical
    map<string, string> data = 6;  
    string description = 7;        
}
```

## 与中央服务器的集成

### 1. 边缘服务器作为客户端

边缘服务器通过`CenterServerConnector`主动连接中央服务器：

```python
# 注册到中央服务器
connector = CenterServerConnector(config)
connector.connect()

# 定期发送心跳
# 自动在后台线程中执行

# 报告任务进度
connector.report_task_progress(task_id, status, progress)

# 注册数据上传
connector.register_data_upload(upload_info)
```

### 2. 中央服务器兼容性

- 支持中央服务器的任务分配
- 支持硬件控制命令
- 支持紧急停止命令
- 自动上报系统状态和硬件状态

### 3. 心跳机制

- 每30秒发送一次心跳
- 包含系统状态、模块状态、硬件状态
- 接收并处理中央服务器的命令

## 错误处理

### gRPC状态码使用

| 状态码 | 使用场景 |
|--------|----------|
| OK | 请求成功 |
| UNAUTHENTICATED | 未认证或会话过期 |
| NOT_FOUND | 资源不存在（如任务、文件） |
| INVALID_ARGUMENT | 请求参数无效 |
| INTERNAL | 服务器内部错误 |
| UNAVAILABLE | 服务暂时不可用 |

### 错误响应示例

```python
context.abort(grpc.StatusCode.UNAUTHENTICATED, 'Authentication required')
```

## 部署和配置

### 1. 启动gRPC服务器

```python
# 作为独立服务启动
python src/grpc_server.py

# 或集成到主服务
from grpc_server import serve
serve(config, task_manager)
```

### 2. 配置文件示例

```yaml
grpc:
  port: 50051
  max_workers: 10
  
edge_id: edge-001
edge_name: "Edge Server 001"

auth_tokens:
  test_token_123: "Test Client"
  
center_server:
  host: *************
  port: 50052
```

### 3. 测试脚本

```bash
# 运行完整测试
python tests/test_grpc_interface.py

# 测试特定服务器
python tests/test_grpc_interface.py --host ************* --port 50051
```

## 性能优化建议

1. **连接池管理**：复用gRPC连接
2. **批量操作**：使用流式RPC减少往返次数
3. **异步处理**：使用异步gRPC提高并发性能
4. **缓存策略**：缓存认证信息和设备状态
5. **超时设置**：合理设置RPC超时时间

## 安全建议

1. **使用TLS**：生产环境应使用`secure_channel`
2. **令牌管理**：定期更新认证令牌
3. **访问控制**：基于角色的权限控制
4. **审计日志**：记录所有API调用
5. **限流保护**：防止恶意请求

## 版本历史

- v2.23 (2025-07-24): 完善gRPC接口设计，支持所有业务功能
  - 添加认证机制
  - 实现所有业务接口handler
  - 添加与中央服务器的兼容性支持
  - 创建测试脚本和文档