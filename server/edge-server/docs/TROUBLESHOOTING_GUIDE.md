# 边缘服务器故障排查指南

## 概述

本指南提供了边缘服务器和相机服务常见问题的诊断和解决方案。按照问题类型分类，帮助快速定位和解决问题。

## 目录

1. [服务启动问题](#服务启动问题)
2. [相机服务问题](#相机服务问题)
3. [网络通信问题](#网络通信问题)
4. [性能问题](#性能问题)
5. [数据上传问题](#数据上传问题)
6. [硬件控制问题](#硬件控制问题)
7. [日志分析](#日志分析)
8. [紧急恢复](#紧急恢复)

## 服务启动问题

### 边缘服务器无法启动

**症状**：
- 服务启动后立即退出
- 端口50051无法访问

**诊断步骤**：

1. 检查端口占用：
```bash
sudo lsof -i:50051
sudo lsof -i:7090
```

2. 验证配置文件：
```bash
python3 -c "import yaml; yaml.safe_load(open('config/config.yaml'))"
```

3. 查看启动日志：
```bash
tail -f logs/edge-server.log
journalctl -u edge-server -n 100
```

**解决方案**：
- 释放占用的端口
- 修复配置文件语法错误
- 检查Python依赖是否完整

### 相机服务无法启动

**症状**：
- 端口7090无响应
- SDK初始化失败

**诊断步骤**：

1. 检查SDK库文件：
```bash
ls -la camera-service/camera/hikvision/库文件/
ldd camera-service/camera/hikvision/库文件/libhcnetsdk.so
```

2. 验证环境变量：
```bash
echo $LD_LIBRARY_PATH
```

3. 测试SDK加载：
```python
import ctypes
sdk = ctypes.CDLL('./camera-service/camera/hikvision/库文件/libhcnetsdk.so')
```

**解决方案**：
```bash
# 设置库路径
export LD_LIBRARY_PATH=/home/<USER>/server/edge-server/camera-service/camera/hikvision/库文件:$LD_LIBRARY_PATH

# 修复权限
chmod +x camera-service/camera/hikvision/库文件/*.so

# 安装缺失的系统库
sudo apt-get install libssl1.1
```

## 相机服务问题

### HTTP API调用失败

**症状**：
- curl请求返回connection refused
- Python requests超时

**诊断**：
```bash
# 测试服务健康
curl -v http://localhost:7090/health

# 检查服务状态
systemctl status camera-service

# 查看服务日志
tail -f logs/camera_service.log
```

**解决方案**：
1. 确保相机服务已启动
2. 检查防火墙设置
3. 使用正确的URL和端口

### NVR连接失败

**症状**：
- Login failed错误
- 无法获取设备信息

**诊断**：
```bash
# 测试网络连接
ping *************

# 检查端口
telnet ************* 8000

# 验证凭据
curl -v ****************************************/ISAPI/System/deviceInfo
```

**解决方案**：
1. 确认NVR IP和端口正确
2. 验证用户名密码
3. 检查NVR是否启用了SDK访问
4. 确认网络路由正确

### 抓拍/录像失败

**症状**：
- 返回空文件
- SDK错误码

**诊断**：
```bash
# 检查磁盘空间
df -h /tmp

# 查看文件权限
ls -la /tmp/camera_data/

# 测试手动抓拍
curl -X POST http://localhost:7090/api/camera/snapshot \
  -H "Content-Type: application/json" \
  -d '{"channel": 33}'
```

**解决方案**：
1. 创建数据目录：
```bash
mkdir -p /tmp/camera_data
chmod 755 /tmp/camera_data
```

2. 清理磁盘空间：
```bash
rm -rf /tmp/camera_data/old_*
```

3. 验证通道号是否正确

## 网络通信问题

### gRPC连接失败

**症状**：
- Failed to connect to remote host
- RPC超时

**诊断**：
```bash
# 测试gRPC端口
nc -zv localhost 50051

# 使用grpcurl测试
grpcurl -plaintext localhost:50051 list

# 检查服务注册
python3 -c "from src.grpc_server import server; print('OK')"
```

**解决方案**：
1. 确保gRPC服务正在运行
2. 检查防火墙规则
3. 验证proto文件已编译

### HTTP代理问题

**症状**：
- requests库返回503错误
- 连接被代理拦截

**诊断**：
```bash
# 检查代理设置
echo $http_proxy
echo $https_proxy

# 绕过代理测试
unset http_proxy https_proxy
curl http://localhost:7090/health
```

**解决方案**：
```python
# 在代码中禁用代理
import os
os.environ['NO_PROXY'] = 'localhost,127.0.0.1'

# 或使用urllib
import urllib.request
urllib.request.ProxyHandler({})
```

## 性能问题

### 内存泄漏

**症状**：
- 内存使用持续增长
- 系统变慢

**诊断**：
```bash
# 监控内存使用
ps aux | grep camera_control
top -p $(pgrep -f camera_control)

# 检查系统内存
free -h
```

**解决方案**：
1. 设置systemd内存限制：
```ini
[Service]
MemoryLimit=2G
MemoryAccounting=true
```

2. 定期重启服务：
```bash
# 添加到crontab
0 */6 * * * systemctl restart camera-service
```

### CPU占用过高

**症状**：
- CPU使用率100%
- 响应缓慢

**诊断**：
```bash
# 查看CPU使用
htop
mpstat 1 10

# 分析进程
strace -p $(pgrep -f camera_control)
```

**解决方案**：
1. 限制并发请求数
2. 优化循环和轮询
3. 使用异步IO

## 数据上传问题

### MinIO连接失败

**症状**：
- S3 connection error
- 上传超时

**诊断**：
```bash
# 测试MinIO连接
mc alias set myminio http://**************:9000 minioadmin minioadmin
mc ls myminio/sensor-data/

# 检查网络
ping **************
```

**解决方案**：
1. 验证MinIO配置
2. 检查访问密钥
3. 确认bucket存在

### 上传队列堵塞

**症状**：
- 文件堆积在本地
- 上传进度停滞

**诊断**：
```bash
# 查看队列状态
ls -la /tmp/minio_upload_queue/

# 检查上传日志
grep "upload" logs/edge-server.log
```

**解决方案**：
1. 清理失败的上传任务
2. 增加上传并发数
3. 检查网络带宽

## 硬件控制问题

### IO控制器离线

**症状**：
- 无法控制继电器
- 硬件状态未知

**诊断**：
```bash
# 测试硬件服务
curl http://localhost:7080/api/hardware/status

# 检查串口
ls -la /dev/ttyUSB*
dmesg | grep tty
```

**解决方案**：
1. 重启硬件服务
2. 检查USB连接
3. 更新串口权限

### PTZ控制失败

**症状**：
- 云台不响应
- 位置不准确

**诊断**：
```bash
# 测试PTZ命令
curl -X POST http://localhost:7090/api/camera/ptz/control \
  -H "Content-Type: application/json" \
  -d '{"channel": 33, "command": "stop"}'
```

**解决方案**：
1. 校准PTZ零位
2. 检查通道配置
3. 验证PTZ权限

## 日志分析

### 日志位置

```bash
# 主要日志文件
/home/<USER>/server/edge-server/logs/edge-server.log      # 主服务日志
/home/<USER>/server/edge-server/logs/camera_service.log   # 相机服务日志
/home/<USER>/server/edge-server/logs/hardware_service.log # 硬件服务日志

# 系统日志
journalctl -u edge-server
journalctl -u camera-service
```

### 日志级别

调整日志级别以获取更多信息：
```bash
# 设置DEBUG级别
export LOG_LEVEL=DEBUG
python3 src/main.py
```

### 常见错误模式

1. **SDK错误码**：
   - 错误码7：网络连接失败
   - 错误码28：登录失败
   - 错误码34：参数错误

2. **HTTP错误**：
   - 503：服务不可用（检查相机服务）
   - 504：网关超时（增加超时时间）
   - 400：请求参数错误

## 紧急恢复

### 快速恢复步骤

1. **停止所有服务**：
```bash
systemctl stop edge-server
systemctl stop camera-service
systemctl stop hardware-control
```

2. **清理临时文件**：
```bash
rm -rf /tmp/camera_data/*
rm -rf /tmp/minio_upload_queue/*
```

3. **重置配置**：
```bash
cp config/config.yaml.template config/config.yaml
# 编辑配置文件
```

4. **逐个启动服务**：
```bash
systemctl start hardware-control
sleep 5
systemctl start camera-service  
sleep 5
systemctl start edge-server
```

### 数据备份

```bash
# 备份配置
tar -czf config_backup_$(date +%Y%m%d).tar.gz config/

# 备份日志
tar -czf logs_backup_$(date +%Y%m%d).tar.gz logs/

# 备份数据
rsync -av /tmp/camera_data/ /backup/camera_data/
```

### 回滚程序

```bash
# 查看git历史
git log --oneline -10

# 回滚到稳定版本
git checkout <commit-hash>

# 重新部署
pip install -r requirements.txt
./scripts/start_server.sh
```

## 预防措施

1. **定期维护**：
   - 每周检查日志
   - 每月清理临时文件
   - 定期更新系统

2. **监控告警**：
   - 设置磁盘空间告警
   - 监控服务状态
   - 记录性能指标

3. **备份策略**：
   - 自动备份配置
   - 保留最近7天日志
   - 定期测试恢复流程

## 获取帮助

如果以上方法无法解决问题：

1. 收集诊断信息：
```bash
./scripts/collect_diagnostics.sh
```

2. 查看项目文档：
   - README.md
   - DEPLOYMENT.md
   - 各模块的PROJECT_MEMORY.md

3. 查看提交历史：
```bash
git log --grep="fix" --oneline
```