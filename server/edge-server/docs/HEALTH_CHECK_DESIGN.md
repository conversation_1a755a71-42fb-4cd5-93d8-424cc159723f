# 边缘服务器健康检查机制设计文档

## 概述

边缘服务器健康检查机制提供了分层的健康监控能力，确保系统和设备在执行任务前处于良好状态。该机制分为基础健康检查和深度健康检查两个层次。

## 架构设计

### 1. 健康检查层次

#### 基础健康检查（每30秒自动执行）
- **网络连通性检查**
  - 串口服务器（*************）ping测试
  - NVR（*************）ping测试
  - 激光雷达网络可达性测试（不上电）
  
- **服务状态检查**
  - 硬件控制服务（http://localhost:7080）
  - 摄像头服务（http://localhost:7090）
  - gRPC服务（端口51011）

#### 深度健康检查（任务执行前触发）
- 包含所有基础检查项
- **设备健康检查**（需要上电）
  - 激光雷达上电并检查连接
  - 云台上电并查询状态
  - 检查完成后自动断电（安全措施）

### 2. 健康状态定义

```python
class HealthStatus(Enum):
    HEALTHY = "healthy"      # 完全健康
    DEGRADED = "degraded"    # 部分降级但可用
    UNHEALTHY = "unhealthy"  # 不健康
    UNKNOWN = "unknown"      # 未知状态
```

### 3. 组件架构

```
health_check.py
├── NetworkHealthChecker    # 网络检查器
├── ServiceHealthChecker    # 服务检查器
├── DeviceHealthChecker     # 设备检查器
└── HealthCheckService      # 健康检查服务主类

health_api.py
└── HealthAPI              # RESTful API端点

health_aware_task_manager.py
└── HealthAwareTaskManager  # 健康感知的任务管理器
```

## API端点

### 1. 基础健康检查
```
GET /health
```
返回最近的健康检查结果（缓存60秒）

### 2. 强制基础健康检查
```
GET /health/basic
```
立即执行基础健康检查

### 3. 深度健康检查
```
GET /health/deep
Authorization: Bearer <token>
```
执行包含设备检查的深度健康检查（需要授权）

### 4. 健康状态摘要
```
GET /health/status
```
获取所有健康检查的摘要信息

### 5. 组件健康状态
```
GET /health/components/{component}
```
获取特定组件的健康状态（network/services/devices）

### 6. 任务前健康检查触发
```
POST /health/deep/trigger
Authorization: Bearer <token>
{
    "task_id": "task-001",
    "task_type": "lidar_scan"
}
```

## 使用示例

### 1. 基础使用

```python
from src.health_check import get_health_service

# 获取健康检查服务
health_service = get_health_service()

# 执行基础健康检查
result = await health_service.perform_basic_check()

# 执行深度健康检查
deep_result = await health_service.perform_deep_check()
```

### 2. 集成到任务管理

```python
from src.health_aware_task_manager import HealthAwareTaskManager, TaskType

# 创建健康感知的任务管理器
task_manager = HealthAwareTaskManager()

# 执行带健康检查的任务
result = await task_manager.execute_task_with_health_check(
    task_id="scan_001",
    task_type=TaskType.LIDAR_SCAN,
    task_func=lidar_scan_function,
    task_args={"duration": 60}
)
```

### 3. 任务类型与健康要求

| 任务类型 | 需要深度检查 | 必需组件 | 最低健康级别 |
|---------|------------|---------|------------|
| LIDAR_SCAN | 是 | network, services, devices | HEALTHY |
| PTZ_CONTROL | 是 | network, services, devices | HEALTHY |
| CAMERA_RECORD | 否 | network, services | DEGRADED |
| DATA_SYNC | 否 | network, services | DEGRADED |
| GENERAL | 否 | services | DEGRADED |

## 配置

在`config.yaml`中添加健康检查配置：

```yaml
monitoring:
  health_api_port: 8088  # 健康检查API端口
```

## 安全考虑

1. **设备电源管理**
   - 激光雷达和云台平时保持断电状态
   - 深度健康检查时临时上电
   - 检查完成或异常时立即断电

2. **API访问控制**
   - 深度健康检查需要授权令牌
   - 防止未授权的设备上电操作

3. **异常处理**
   - 任何异常情况下确保设备断电
   - 记录详细的错误日志

## 监控和告警

1. **自动监控**
   - 基础健康检查每30秒自动执行
   - 结果缓存60秒，避免频繁检查

2. **任务前检查**
   - 关键任务执行前强制深度检查
   - 根据检查结果决定是否执行任务

3. **日志记录**
   - 所有健康检查结果记录到日志
   - 异常情况详细记录

## 测试

使用提供的测试脚本：

```bash
# 测试健康检查功能
python test_health_check.py

# 测试健康感知的任务管理
python -m src.health_aware_task_manager
```

## 集成步骤

1. 启动边缘服务器时自动初始化健康检查服务
2. 健康检查API在端口8088提供服务
3. 基础健康检查自动运行
4. 任务执行前调用健康检查API或使用HealthAwareTaskManager

## 注意事项

1. 激光雷达和云台设备平时不上电，只在需要时临时上电
2. 深度健康检查会消耗时间（约30秒等待设备启动）
3. 建议在任务调度时提前进行健康检查
4. 网络故障不会阻止本地任务执行（降级模式）