#!/bin/bash

# 安装Camera Service为系统服务

if [ "$EUID" -ne 0 ]; then 
   echo "请使用sudo运行此脚本"
   exit 1
fi

# 创建日志目录
mkdir -p /var/log/camera-service
chown app:app /var/log/camera-service

# 复制service文件
cp camera-service.service /etc/systemd/system/

# 重新加载systemd
systemctl daemon-reload

# 启用服务
systemctl enable camera-service.service

echo "Camera Service已安装"
echo "使用以下命令管理服务:"
echo "  启动: sudo systemctl start camera-service"
echo "  停止: sudo systemctl stop camera-service"
echo "  状态: sudo systemctl status camera-service"
echo "  日志: sudo journalctl -u camera-service -f"