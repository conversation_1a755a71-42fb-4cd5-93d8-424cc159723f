"""
PTZ控制服务模块
处理云台控制相关的业务逻辑
"""

import asyncio
import logging
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class PTZService:
    """PTZ控制服务"""
    
    def __init__(self, sdk_instance):
        """初始化PTZ服务
        
        Args:
            sdk_instance: HikvisionSDK实例
        """
        self.sdk = sdk_instance
        self._control_tasks = {}
        self._lock = asyncio.Lock()
    
    async def control_ptz(self, channel: int, action: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """控制PTZ
        
        Args:
            channel: 通道号
            action: 动作类型 (move/zoom/stop)
            params: 控制参数
            
        Returns:
            Dict: 操作结果
        """
        if action == "move":
            return await self._move_control(channel, params)
        elif action == "zoom":
            return await self._zoom_control(channel, params)
        elif action == "cruise":
            return await self._cruise_control(channel, params)
        elif action == "stop":
            return await self._stop_all(channel)
        else:
            return {
                "success": False,
                "error": f"未知的PTZ动作: {action}"
            }
    
    async def _move_control(self, channel: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """移动控制"""
        direction = params.get("direction", "").lower()
        speed = params.get("speed", 4)
        duration = params.get("duration", 1.0)
        
        if direction not in ["up", "down", "left", "right"]:
            return {
                "success": False,
                "error": f"无效的方向: {direction}"
            }
        
        # 开始移动
        success = await asyncio.get_event_loop().run_in_executor(
            None, self.sdk.ptz_control, channel, direction, False, speed
        )
        
        if not success:
            return {
                "success": False,
                "error": "PTZ控制失败"
            }
        
        # 创建自动停止任务
        task_key = f"{channel}_move"
        async with self._lock:
            # 取消之前的任务
            if task_key in self._control_tasks:
                self._control_tasks[task_key].cancel()
            
            # 创建新任务
            task = asyncio.create_task(self._auto_stop(channel, direction, duration))
            self._control_tasks[task_key] = task
        
        return {
            "success": True,
            "data": {
                "channel": channel,
                "action": "move",
                "direction": direction,
                "speed": speed,
                "duration": duration
            }
        }
    
    async def _zoom_control(self, channel: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """缩放控制"""
        zoom_type = params.get("type", "").lower()
        speed = params.get("speed", 4)
        duration = params.get("duration", 1.0)
        
        logger.info(f"PTZ缩放控制: channel={channel}, params={params}, zoom_type={zoom_type}")
        
        if zoom_type not in ["in", "out"]:
            return {
                "success": False,
                "error": f"无效的缩放类型: {zoom_type}"
            }
        
        command = f"zoom_{zoom_type}"
        
        # 开始缩放
        success = await asyncio.get_event_loop().run_in_executor(
            None, self.sdk.ptz_control, channel, command, False, speed
        )
        
        if not success:
            return {
                "success": False,
                "error": "缩放控制失败"
            }
        
        # 创建自动停止任务
        task_key = f"{channel}_zoom"
        async with self._lock:
            if task_key in self._control_tasks:
                self._control_tasks[task_key].cancel()
            
            task = asyncio.create_task(self._auto_stop(channel, command, duration))
            self._control_tasks[task_key] = task
        
        return {
            "success": True,
            "data": {
                "channel": channel,
                "action": "zoom",
                "type": zoom_type,
                "speed": speed,
                "duration": duration
            }
        }
    
    async def _stop_all(self, channel: int) -> Dict[str, Any]:
        """停止所有PTZ动作"""
        # 取消所有自动停止任务
        async with self._lock:
            for key in list(self._control_tasks.keys()):
                if key.startswith(f"{channel}_"):
                    self._control_tasks[key].cancel()
                    del self._control_tasks[key]
        
        # 停止所有方向的移动
        commands = ["up", "down", "left", "right", "zoom_in", "zoom_out"]
        for cmd in commands:
            await asyncio.get_event_loop().run_in_executor(
                None, self.sdk.ptz_control, channel, cmd, True
            )
        
        return {
            "success": True,
            "data": {
                "channel": channel,
                "action": "stop"
            }
        }
    
    async def _auto_stop(self, channel: int, command: str, duration: float):
        """自动停止PTZ动作"""
        try:
            await asyncio.sleep(duration)
            await asyncio.get_event_loop().run_in_executor(
                None, self.sdk.ptz_control, channel, command, True
            )
        except asyncio.CancelledError:
            pass
    
    async def set_preset(self, channel: int, preset_id: int) -> Dict[str, Any]:
        """设置预置点"""
        success = await asyncio.get_event_loop().run_in_executor(
            None, self.sdk.preset_control, channel, preset_id, 'set'
        )
        
        if success:
            return {
                "success": True,
                "data": {
                    "channel": channel,
                    "preset_id": preset_id,
                    "action": "set"
                }
            }
        else:
            return {
                "success": False,
                "error": f"设置预置点 {preset_id} 失败"
            }
    
    async def call_preset(self, channel: int, preset_id: int) -> Dict[str, Any]:
        """调用预置点"""
        success = await asyncio.get_event_loop().run_in_executor(
            None, self.sdk.preset_control, channel, preset_id, 'call'
        )
        
        if success:
            return {
                "success": True,
                "data": {
                    "channel": channel,
                    "preset_id": preset_id,
                    "action": "call"
                }
            }
        else:
            return {
                "success": False,
                "error": f"调用预置点 {preset_id} 失败"
            }
    
    async def delete_preset(self, channel: int, preset_id: int) -> Dict[str, Any]:
        """删除预置点"""
        success = await asyncio.get_event_loop().run_in_executor(
            None, self.sdk.preset_control, channel, preset_id, 'delete'
        )
        
        if success:
            return {
                "success": True,
                "data": {
                    "channel": channel,
                    "preset_id": preset_id,
                    "action": "delete"
                }
            }
        else:
            return {
                "success": False,
                "error": f"删除预置点 {preset_id} 失败"
            }
    
    async def _cruise_control(self, channel: int, params: Dict[str, Any]) -> Dict[str, Any]:
        """巡航控制"""
        action = params.get("action", "").lower()
        cruise_id = params.get("cruise_id", 1)
        
        if action not in ["start", "stop"]:
            return {
                "success": False,
                "error": f"无效的巡航动作: {action}"
            }
        
        # 使用NET_DVR_PTZCruise接口进行巡航控制
        # 注意：巡航功能需要先配置巡航路径，这里简化为直接调用
        logger.info(f"巡航控制: channel={channel}, action={action}, cruise_id={cruise_id}")
        
        # 暂时返回模拟成功，实际需要配置巡航路径
        success = True
        
        if success:
            return {
                "success": True,
                "data": {
                    "channel": channel,
                    "action": f"cruise_{action}",
                    "cruise_id": cruise_id
                }
            }
        else:
            return {
                "success": False,
                "error": f"巡航{action}失败"
            }
    
    async def cleanup(self):
        """清理资源"""
        # 取消所有控制任务
        async with self._lock:
            for task in self._control_tasks.values():
                task.cancel()
            self._control_tasks.clear()