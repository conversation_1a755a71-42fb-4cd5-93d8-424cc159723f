"""
录像服务模块
处理所有录像相关的业务逻辑
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class RecordingService:
    """录像服务管理器"""
    
    def __init__(self, sdk_instance):
        """初始化录像服务
        
        Args:
            sdk_instance: HikvisionSDK实例
        """
        self.sdk = sdk_instance
        self._recording_tasks = {}
        self._lock = asyncio.Lock()
    
    async def start_recording(self, channel: int, duration: Optional[int] = None) -> Dict[str, Any]:
        """开始录像
        
        Args:
            channel: 通道号
            duration: 录像时长（秒），None表示持续录像
            
        Returns:
            Dict: 录像信息
        """
        async with self._lock:
            # 检查是否已在录像
            if channel in self._recording_tasks:
                return {
                    "success": False,
                    "error": f"通道 {channel} 已经在录像中"
                }
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"record_ch{channel}_{timestamp}.mp4"
            filepath = f"/home/<USER>/server/edge-server/data/camera/recordings/{filename}"
            
            # 开始录像
            handle = await asyncio.get_event_loop().run_in_executor(
                None, self.sdk.start_recording, channel, filepath
            )
            
            if handle is None:
                return {
                    "success": False,
                    "error": "启动录像失败"
                }
            
            # 创建录像任务
            task_info = {
                "handle": handle,
                "channel": channel,
                "filename": filename,
                "filepath": filepath,
                "start_time": datetime.now(),
                "duration": duration,
                "status": "recording"
            }
            
            self._recording_tasks[channel] = task_info
            
            # 如果指定了时长，创建定时停止任务
            if duration:
                asyncio.create_task(self._auto_stop_recording(channel, duration))
            
            return {
                "success": True,
                "data": {
                    "channel": channel,
                    "filename": filename,
                    "start_time": task_info["start_time"].isoformat(),
                    "duration": duration
                }
            }
    
    async def stop_recording(self, channel: int) -> Dict[str, Any]:
        """停止录像
        
        Args:
            channel: 通道号
            
        Returns:
            Dict: 操作结果
        """
        async with self._lock:
            task = self._recording_tasks.get(channel)
            if not task:
                return {
                    "success": True,
                    "message": f"通道 {channel} 未在录像"
                }
            
            # 停止录像
            success = await asyncio.get_event_loop().run_in_executor(
                None, self.sdk.stop_recording, channel
            )
            
            if success:
                # 计算录像时长
                duration = (datetime.now() - task["start_time"]).total_seconds()
                
                # 清理任务
                del self._recording_tasks[channel]
                
                return {
                    "success": True,
                    "data": {
                        "channel": channel,
                        "filename": task["filename"],
                        "duration": duration,
                        "file_size": self._get_file_size(task["filepath"])
                    }
                }
            else:
                return {
                    "success": False,
                    "error": "停止录像失败"
                }
    
    async def get_recording_status(self, channel: int) -> Dict[str, Any]:
        """获取录像状态
        
        Args:
            channel: 通道号
            
        Returns:
            Dict: 录像状态信息
        """
        async with self._lock:
            task = self._recording_tasks.get(channel)
            if not task:
                return {
                    "recording": False,
                    "channel": channel
                }
            
            # 计算已录制时长
            elapsed = (datetime.now() - task["start_time"]).total_seconds()
            
            return {
                "recording": True,
                "channel": channel,
                "filename": task["filename"],
                "start_time": task["start_time"].isoformat(),
                "elapsed_seconds": elapsed,
                "duration": task["duration"],
                "remaining_seconds": task["duration"] - elapsed if task["duration"] else None
            }
    
    async def preset_recording(self, channel: int, preset_id: int, duration: int) -> Dict[str, Any]:
        """定点录像
        
        Args:
            channel: 通道号
            preset_id: 预置点ID
            duration: 录像时长（秒）
            
        Returns:
            Dict: 操作结果
        """
        # 先调用预置点
        success = await asyncio.get_event_loop().run_in_executor(
            None, self.sdk.preset_control, channel, preset_id, 'call'
        )
        
        if not success:
            return {
                "success": False,
                "error": f"调用预置点 {preset_id} 失败"
            }
        
        # 等待云台到位
        await asyncio.sleep(3)
        
        # 开始录像
        return await self.start_recording(channel, duration)
    
    async def terrain_recording(self, channel: int, duration: int, 
                               tilt_step: int = 15, pan_speed: int = 2) -> Dict[str, Any]:
        """地形扫描录像
        
        Args:
            channel: 通道号
            duration: 总录像时长（秒）
            tilt_step: 俯仰角步进（度）
            pan_speed: 水平扫描速度
            
        Returns:
            Dict: 操作结果
        """
        # 开始录像
        result = await self.start_recording(channel, duration)
        if not result["success"]:
            return result
        
        # 创建扫描任务
        asyncio.create_task(self._terrain_scan(channel, duration, tilt_step, pan_speed))
        
        return {
            "success": True,
            "data": {
                "channel": channel,
                "filename": result["data"]["filename"],
                "duration": duration,
                "scan_params": {
                    "tilt_step": tilt_step,
                    "pan_speed": pan_speed
                }
            }
        }
    
    async def _auto_stop_recording(self, channel: int, duration: int):
        """自动停止录像任务"""
        await asyncio.sleep(duration)
        await self.stop_recording(channel)
    
    async def _terrain_scan(self, channel: int, duration: int, tilt_step: int, pan_speed: int):
        """执行地形扫描"""
        try:
            # 计算扫描参数
            scan_layers = 180 // tilt_step  # 扫描层数
            time_per_layer = duration / scan_layers
            
            # 开始扫描
            for layer in range(scan_layers):
                # 设置俯仰角
                tilt_angle = -90 + layer * tilt_step
                
                # 360度水平扫描
                await asyncio.get_event_loop().run_in_executor(
                    None, self.sdk.ptz_control, channel, 'right', False, pan_speed
                )
                
                # 等待一层扫描完成
                await asyncio.sleep(time_per_layer)
                
                # 停止水平移动
                await asyncio.get_event_loop().run_in_executor(
                    None, self.sdk.ptz_control, channel, 'right', True
                )
                
                # 调整俯仰角
                if layer < scan_layers - 1:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.sdk.ptz_control, channel, 'down', False, 3
                    )
                    await asyncio.sleep(0.5)
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.sdk.ptz_control, channel, 'down', True
                    )
                
        except Exception as e:
            logger.error(f"地形扫描异常: {e}")
    
    def _get_file_size(self, filepath: str) -> int:
        """获取文件大小"""
        try:
            import os
            return os.path.getsize(filepath)
        except:
            return 0
    
    async def cleanup(self):
        """清理资源"""
        # 停止所有录像
        channels = list(self._recording_tasks.keys())
        for channel in channels:
            await self.stop_recording(channel)