#!/usr/bin/env python3
"""
相机服务业务逻辑层（优化版）
管理相机操作的核心业务逻辑
"""

import os
import time
import asyncio
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
from pathlib import Path

from sdk.hikvision import HikvisionSDK
# from sdk.connection_pool import ConnectionPool  # 暂时不使用连接池
from .recording_service import RecordingService
from .ptz_service import PTZService
from utils import ResourceManager, ErrorRecovery, SimpleMetrics

logger = logging.getLogger(__name__)


class CameraService:
    """相机服务管理器"""
    
    # 通道映射: 用户通道 -> 实际NVR通道
    CHANNEL_MAPPING = {
        1: 33,  # 支持PTZ
        2: 34,
        3: 35,  # 支持PTZ
        4: 36
    }
    
    # 支持PTZ的通道
    PTZ_CHANNELS = [1, 3]
    
    def __init__(self, config: Dict[str, Any]):
        """初始化相机服务
        
        Args:
            config: 配置信息
        """
        self.config = config
        self.nvr_config = config.get('nvr', {})
        
        # 初始化SDK（暂时不使用连接池）
        self.sdk = None
        
        # 初始化服务
        self.recording_service = None
        self.ptz_service = None
        
        # 初始化优化组件
        self.resource_manager = None
        self.error_recovery = None
        self.metrics = SimpleMetrics()
        
        # 数据目录 - 使用edge-server的统一数据目录
        self.data_dir = Path("/home/<USER>/server/edge-server/data/camera")
        # 确保子目录存在
        (self.data_dir / "snapshots").mkdir(parents=True, exist_ok=True)
        (self.data_dir / "recordings").mkdir(parents=True, exist_ok=True)
        (self.data_dir / "playback").mkdir(parents=True, exist_ok=True)
        
        logger.info("相机服务初始化完成")
    
    async def initialize(self):
        """异步初始化"""
        try:
            # 导入SDK
            from sdk.hikvision import HikvisionSDK
            
            # 创建SDK实例
            self.sdk = HikvisionSDK()
            
            # 登录NVR
            if self.sdk.login(
                self.nvr_config.get('ip', '*************'),
                self.nvr_config.get('port', 8000),
                self.nvr_config.get('username', 'admin'),
                self.nvr_config.get('password', 'Dhdjktsz')
            ):
                # 初始化子服务
                self.recording_service = RecordingService(self.sdk)
                self.ptz_service = PTZService(self.sdk)
                logger.info("相机服务初始化成功")
                return True
            else:
                logger.error("NVR登录失败")
                return False
        except Exception as e:
            logger.error(f"初始化异常: {e}")
            return False
    
    async def _get_sdk(self) -> Optional[HikvisionSDK]:
        """获取SDK实例"""
        return self.sdk
    
    def _get_actual_channel(self, user_channel: int) -> int:
        """获取实际通道号"""
        return self.CHANNEL_MAPPING.get(user_channel, user_channel)
    
    def _validate_channel(self, channel: int) -> bool:
        """验证通道号"""
        return channel in self.CHANNEL_MAPPING
    
    def _validate_ptz_channel(self, channel: int) -> bool:
        """验证PTZ通道"""
        return channel in self.PTZ_CHANNELS
    
    async def capture_snapshot(self, channel: int, quality: int = 0) -> Dict[str, Any]:
        """抓拍照片
        
        Args:
            channel: 用户通道号 (1-4)
            quality: 图片质量 (0=最好, 1=较好, 2=一般)
            
        Returns:
            Dict: 操作结果
        """
        start_time = time.time()
        
        if not self._validate_channel(channel):
            return {
                "success": False,
                "error": f"无效的通道号: {channel}"
            }
        
        sdk = await self._get_sdk()
        if not sdk:
            return {
                "success": False,
                "error": "SDK未初始化"
            }
        
        actual_channel = self._get_actual_channel(channel)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"snapshot_ch{channel}_{timestamp}.jpg"
        filepath = self.data_dir / "snapshots" / filename
        
        try:
            # 使用资源管理器获取预览句柄
            preview_handle = await self.resource_manager.get_preview_handle(actual_channel)
            if preview_handle is None:
                return {
                    "success": False,
                    "error": "获取预览句柄失败"
                }
            
            # 执行抓拍
            success = await asyncio.get_event_loop().run_in_executor(
                None, sdk.capture_picture, actual_channel, str(filepath), quality
            )
            
            # 记录指标
            duration = time.time() - start_time
            self.metrics.record_duration('snapshot', duration)
            
            if success:
                self.metrics.increment('snapshot_success')
                self.metrics.increment(f'channel_{channel}_snapshot')
                
                return {
                    "success": True,
                    "data": {
                        "channel": channel,
                        "filename": filename,
                        "path": str(filepath),
                        "timestamp": timestamp,
                        "file_size": filepath.stat().st_size if filepath.exists() else 0
                    }
                }
            else:
                self.metrics.record_error('snapshot', '抓拍失败', channel)
                return {
                    "success": False,
                    "error": "抓拍失败"
                }
                
        except Exception as e:
            self.metrics.record_error('snapshot', str(e), channel)
            logger.error(f"抓拍异常: {e}")
            return {
                "success": False,
                "error": f"抓拍异常: {str(e)}"
            }
    
    async def start_recording(self, channel: int, duration: Optional[int] = None) -> Dict[str, Any]:
        """开始录像"""
        if not self._validate_channel(channel):
            return {
                "success": False,
                "error": f"无效的通道号: {channel}"
            }
        
        if not self.recording_service:
            return {
                "success": False,
                "error": "录像服务未初始化"
            }
        
        actual_channel = self._get_actual_channel(channel)
        return await self.recording_service.start_recording(actual_channel, duration)
    
    async def stop_recording(self, channel: int) -> Dict[str, Any]:
        """停止录像"""
        if not self._validate_channel(channel):
            return {
                "success": False,
                "error": f"无效的通道号: {channel}"
            }
        
        if not self.recording_service:
            return {
                "success": False,
                "error": "录像服务未初始化"
            }
        
        actual_channel = self._get_actual_channel(channel)
        return await self.recording_service.stop_recording(actual_channel)
    
    async def get_recording_status(self, channel: int) -> Dict[str, Any]:
        """获取录像状态"""
        if not self._validate_channel(channel):
            return {
                "recording": False,
                "error": f"无效的通道号: {channel}"
            }
        
        if not self.recording_service:
            return {
                "recording": False,
                "error": "录像服务未初始化"
            }
        
        actual_channel = self._get_actual_channel(channel)
        return await self.recording_service.get_recording_status(actual_channel)
    
    async def control_ptz(self, channel: int, action: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """PTZ控制"""
        if not self._validate_channel(channel):
            return {
                "success": False,
                "error": f"无效的通道号: {channel}"
            }
        
        if not self._validate_ptz_channel(channel):
            return {
                "success": False,
                "error": f"通道 {channel} 不支持PTZ控制"
            }
        
        if not self.ptz_service:
            return {
                "success": False,
                "error": "PTZ服务未初始化"
            }
        
        actual_channel = self._get_actual_channel(channel)
        return await self.ptz_service.control_ptz(actual_channel, action, params)
    
    async def set_preset(self, channel: int, preset_id: int) -> Dict[str, Any]:
        """设置预置点"""
        if not self._validate_ptz_channel(channel):
            return {
                "success": False,
                "error": f"通道 {channel} 不支持预置点"
            }
        
        if not self.ptz_service:
            return {
                "success": False,
                "error": "PTZ服务未初始化"
            }
        
        actual_channel = self._get_actual_channel(channel)
        return await self.ptz_service.set_preset(actual_channel, preset_id)
    
    async def call_preset(self, channel: int, preset_id: int) -> Dict[str, Any]:
        """调用预置点"""
        if not self._validate_ptz_channel(channel):
            return {
                "success": False,
                "error": f"通道 {channel} 不支持预置点"
            }
        
        if not self.ptz_service:
            return {
                "success": False,
                "error": "PTZ服务未初始化"
            }
        
        actual_channel = self._get_actual_channel(channel)
        return await self.ptz_service.call_preset(actual_channel, preset_id)
    
    async def delete_preset(self, channel: int, preset_id: int) -> Dict[str, Any]:
        """删除预置点"""
        if not self._validate_ptz_channel(channel):
            return {
                "success": False,
                "error": f"通道 {channel} 不支持预置点"
            }
        
        if not self.ptz_service:
            return {
                "success": False,
                "error": "PTZ服务未初始化"
            }
        
        actual_channel = self._get_actual_channel(channel)
        return await self.ptz_service.delete_preset(actual_channel, preset_id)
    
    async def preset_recording(self, channel: int, preset_id: int, duration: int) -> Dict[str, Any]:
        """定点录像"""
        if not self._validate_ptz_channel(channel):
            return {
                "success": False,
                "error": f"通道 {channel} 不支持定点录像"
            }
        
        if not self.recording_service:
            return {
                "success": False,
                "error": "录像服务未初始化"
            }
        
        actual_channel = self._get_actual_channel(channel)
        return await self.recording_service.preset_recording(actual_channel, preset_id, duration)
    
    async def terrain_recording(self, channel: int, duration: int, 
                               tilt_step: int = 15, pan_speed: int = 2) -> Dict[str, Any]:
        """地形扫描录像"""
        if not self._validate_ptz_channel(channel):
            return {
                "success": False,
                "error": f"通道 {channel} 不支持地形扫描"
            }
        
        if not self.recording_service:
            return {
                "success": False,
                "error": "录像服务未初始化"
            }
        
        actual_channel = self._get_actual_channel(channel)
        return await self.recording_service.terrain_recording(
            actual_channel, duration, tilt_step, pan_speed
        )
    
    async def get_stream_url(self, channel: int) -> Dict[str, Any]:
        """获取流媒体URL"""
        if not self._validate_channel(channel):
            return {
                "success": False,
                "error": f"无效的通道号: {channel}"
            }
        
        sdk = await self._get_sdk()
        if not sdk:
            return {
                "success": False,
                "error": "SDK未初始化"
            }
        
        actual_channel = self._get_actual_channel(channel)
        urls = await asyncio.get_event_loop().run_in_executor(
            None, sdk.get_stream_url, actual_channel
        )
        
        return {
            "success": True,
            "data": {
                "channel": channel,
                **urls
            }
        }
    
    async def download_playback(self, channel: int, start_time: datetime, 
                               end_time: datetime) -> Dict[str, Any]:
        """下载历史录像"""
        if not self._validate_channel(channel):
            return {
                "success": False,
                "error": f"无效的通道号: {channel}"
            }
        
        sdk = await self._get_sdk()
        if not sdk:
            return {
                "success": False,
                "error": "SDK未初始化"
            }
        
        actual_channel = self._get_actual_channel(channel)
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"playback_ch{channel}_{timestamp}.mp4"
        filepath = self.data_dir / "playback" / filename
        
        # 执行下载
        success = await asyncio.get_event_loop().run_in_executor(
            None, sdk.download_playback, actual_channel, 
            start_time, end_time, str(filepath)
        )
        
        if success:
            return {
                "success": True,
                "data": {
                    "channel": channel,
                    "filename": filename,
                    "path": str(filepath),
                    "start_time": start_time.isoformat(),
                    "end_time": end_time.isoformat(),
                    "file_size": filepath.stat().st_size if filepath.exists() else 0
                }
            }
        else:
            return {
                "success": False,
                "error": "下载失败"
            }
    
    async def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        sdk = await self._get_sdk()
        nvr_connected = sdk is not None
        
        # 获取录像状态
        recording_status = {}
        if self.recording_service:
            for channel in self.CHANNEL_MAPPING.keys():
                actual = self._get_actual_channel(channel)
                status = await self.recording_service.get_recording_status(actual)
                recording_status[channel] = status.get("recording", False)
        
        return {
            "nvr_connected": nvr_connected,
            "nvr_config": {
                "ip": self.nvr_config.get('ip'),
                "port": self.nvr_config.get('port')
            },
            "channels": {
                "total": len(self.CHANNEL_MAPPING),
                "mapping": self.CHANNEL_MAPPING,
                "ptz_enabled": self.PTZ_CHANNELS
            },
            "recording_status": recording_status,
            "sdk_connected": self.sdk is not None and self.sdk.user_id >= 0
        }
    
    async def list_files(self) -> List[Dict[str, Any]]:
        """列出录像文件"""
        files = []
        # 遍历所有子目录
        for subdir in ['snapshots', 'recordings', 'playback']:
            subdir_path = self.data_dir / subdir
            if subdir_path.exists():
                for filepath in subdir_path.glob("*"):
                    if filepath.is_file():
                        files.append({
                            "filename": filepath.name,
                            "path": f"{subdir}/{filepath.name}",
                            "size": filepath.stat().st_size,
                            "created": datetime.fromtimestamp(filepath.stat().st_ctime).isoformat(),
                            "type": "video" if filepath.suffix in ['.mp4', '.avi'] else "image",
                            "category": subdir
                        })
        
        return sorted(files, key=lambda x: x['created'], reverse=True)
    
    async def delete_file(self, filename: str) -> bool:
        """删除文件"""
        # 检查所有子目录
        for subdir in ['snapshots', 'recordings', 'playback']:
            filepath = self.data_dir / subdir / filename
            if filepath.exists() and filepath.is_file():
                filepath.unlink()
                return True
        return False
    
    async def get_metrics(self) -> Dict[str, Any]:
        """获取服务指标"""
        metrics = self.metrics.get_stats()
        
        # 添加资源管理器状态
        if self.resource_manager:
            metrics['resources'] = {
                'handles': len(self.resource_manager.handles),
                'details': list(self.resource_manager.handles.keys())
            }
        
        # 添加SDK状态
        metrics['sdk'] = {
            'connected': self.sdk is not None and self.sdk.user_id >= 0,
            'user_id': self.sdk.user_id if self.sdk else -1
        }
        
        return metrics
    
    async def cleanup(self):
        """清理资源"""
        logger.info("开始清理相机服务资源")
        
        # 停止错误恢复监控
        if self.error_recovery:
            await self.error_recovery.stop_monitoring()
        
        # 清理资源管理器
        if self.resource_manager:
            await self.resource_manager.cleanup_all()
        
        # 清理子服务
        if self.recording_service:
            await self.recording_service.cleanup()
        if self.ptz_service:
            await self.ptz_service.cleanup()
        
        # 登出SDK
        if self.sdk:
            self.sdk.logout()
            del self.sdk
            self.sdk = None
            
        logger.info("相机服务资源清理完成")