"""
相机服务补丁 - 修复资源管理器初始化问题
"""

import asyncio
from services.camera_service import CameraService
from utils import ResourceManager, ErrorRecovery

# 保存原始方法
_original_initialize = CameraService.initialize
_original_capture_snapshot = CameraService.capture_snapshot

def patched_initialize(self) -> bool:
    """修补的初始化方法"""
    # 调用原始初始化
    result = _original_initialize(self)
    
    if result and self.sdk:
        # 初始化优化组件
        self.resource_manager = ResourceManager(self.sdk)
        self.error_recovery = ErrorRecovery(self.sdk)
        
        # 创建异步任务来启动错误恢复监控
        async def start_monitoring():
            await self.error_recovery.start_monitoring()
        
        # 在事件循环中运行
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(start_monitoring())
            else:
                loop.run_until_complete(start_monitoring())
        except:
            # 如果失败，至少组件已初始化
            pass
    
    return result

async def patched_capture_snapshot(self, channel: int, quality: int = 0):
    """修补的抓拍方法 - 暂时不使用资源管理器"""
    import time
    start_time = time.time()
    
    # 如果资源管理器未初始化，使用原始方法
    if not hasattr(self, 'resource_manager') or self.resource_manager is None:
        # 调用SDK直接抓拍
        if not self._validate_channel(channel):
            return {
                "success": False,
                "error": f"无效的通道号: {channel}"
            }
        
        sdk = await self._get_sdk()
        if not sdk:
            return {
                "success": False,
                "error": "SDK未初始化"
            }
        
        actual_channel = self._get_actual_channel(channel)
        
        # 生成文件名
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"snapshot_ch{channel}_{timestamp}.jpg"
        filepath = self.data_dir / "snapshots" / filename
        
        # 执行抓拍
        success = await asyncio.get_event_loop().run_in_executor(
            None, sdk.capture_picture, actual_channel, str(filepath), quality
        )
        
        # 记录指标
        duration = time.time() - start_time
        self.metrics.record_duration('snapshot', duration)
        
        if success:
            self.metrics.increment('snapshot_success')
            self.metrics.increment(f'channel_{channel}_snapshot')
            
            return {
                "success": True,
                "data": {
                    "channel": channel,
                    "filename": filename,
                    "path": str(filepath),
                    "timestamp": timestamp,
                    "file_size": filepath.stat().st_size if filepath.exists() else 0
                }
            }
        else:
            self.metrics.record_error('snapshot', '抓拍失败', channel)
            return {
                "success": False,
                "error": "抓拍失败"
            }
    else:
        # 使用原始方法
        return await _original_capture_snapshot(self, channel, quality)

# 应用补丁
CameraService.initialize = patched_initialize
CameraService.capture_snapshot = patched_capture_snapshot