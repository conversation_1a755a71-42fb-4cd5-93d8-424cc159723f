#!/usr/bin/env python3
"""
REST API服务器
提供相机控制的HTTP接口
"""

import logging
from datetime import datetime
from aiohttp import web
import aiohttp_cors

from services.camera_service import CameraService

logger = logging.getLogger(__name__)


class APIServer:
    """REST API服务器"""
    
    def __init__(self, camera_service: CameraService):
        """初始化API服务器
        
        Args:
            camera_service: 相机服务实例
        """
        self.camera_service = camera_service
        self.app = web.Application()
        self.setup_routes()
        self.setup_cors()
    
    def setup_routes(self):
        """设置路由"""
        # 基础路由
        self.app.router.add_get('/health', self.health_check)
        self.app.router.add_get('/', self.index)
        self.app.router.add_get('/api/v2', self.api_info)
        
        # 相机控制
        self.app.router.add_post('/api/v2/cameras/{channel}/snapshot', self.snapshot)
        self.app.router.add_post('/api/v2/cameras/{channel}/recording', self.start_recording)
        self.app.router.add_delete('/api/v2/cameras/{channel}/recording', self.stop_recording)
        self.app.router.add_get('/api/v2/cameras/{channel}/recording', self.get_recording_status)
        
        # PTZ控制
        self.app.router.add_post('/api/v2/cameras/{channel}/ptz', self.control_ptz)
        
        # 预置点管理
        self.app.router.add_post('/api/v2/cameras/{channel}/preset/{preset_id}/set', self.set_preset)
        self.app.router.add_post('/api/v2/cameras/{channel}/preset/{preset_id}/call', self.call_preset)
        self.app.router.add_delete('/api/v2/cameras/{channel}/preset/{preset_id}', self.delete_preset)
        
        # 高级录像功能
        self.app.router.add_post('/api/v2/cameras/{channel}/recording/preset', self.preset_recording)
        self.app.router.add_post('/api/v2/cameras/{channel}/recording/terrain', self.terrain_recording)
        
        # 流媒体
        self.app.router.add_get('/api/v2/cameras/{channel}/stream', self.get_stream)
        self.app.router.add_post('/api/v2/cameras/{channel}/stream', self.get_stream)  # 兼容POST
        
        # 历史回放
        self.app.router.add_post('/api/v2/cameras/{channel}/playback', self.playback)
        
        # 系统管理
        self.app.router.add_get('/api/v2/status', self.get_status)
        self.app.router.add_get('/api/v2/files', self.list_files)
        self.app.router.add_delete('/api/v2/files/{filename}', self.delete_file)
        
        # 监控指标
        self.app.router.add_get('/api/v2/metrics', self.get_metrics)
    
    def setup_cors(self):
        """设置CORS"""
        cors = aiohttp_cors.setup(self.app, defaults={
            "*": aiohttp_cors.ResourceOptions(
                allow_credentials=True,
                expose_headers="*",
                allow_headers="*",
                allow_methods="*"
            )
        })
        
        # 为所有路由添加CORS
        for route in list(self.app.router.routes()):
            cors.add(route)
    
    async def health_check(self, request):
        """健康检查"""
        status = await self.camera_service.get_status()
        
        return web.json_response({
            "status": "healthy" if status.get("nvr_connected") else "unhealthy",
            "service": "Camera Service",
            "version": "2.0.0",
            "timestamp": datetime.now().isoformat(),
            "details": status
        })
    
    async def index(self, request):
        """首页"""
        return web.json_response({
            "service": "Camera Service API",
            "description": "海康威视相机控制服务",
            "version": "2.0.0",
            "api_base": "/api/v2",
            "documentation": "/api/v2"
        })
    
    async def api_info(self, request):
        """API信息"""
        return web.json_response({
            "version": "2.0.0",
            "endpoints": {
                "cameras": {
                    "snapshot": {
                        "method": "POST",
                        "path": "/api/v2/cameras/{channel}/snapshot",
                        "description": "抓拍照片"
                    },
                    "recording": {
                        "start": {
                            "method": "POST",
                            "path": "/api/v2/cameras/{channel}/recording",
                            "description": "开始录像"
                        },
                        "stop": {
                            "method": "DELETE",
                            "path": "/api/v2/cameras/{channel}/recording",
                            "description": "停止录像"
                        },
                        "status": {
                            "method": "GET",
                            "path": "/api/v2/cameras/{channel}/recording",
                            "description": "获取录像状态"
                        }
                    },
                    "ptz": {
                        "method": "POST",
                        "path": "/api/v2/cameras/{channel}/ptz",
                        "description": "PTZ控制"
                    },
                    "stream": {
                        "method": "GET",
                        "path": "/api/v2/cameras/{channel}/stream",
                        "description": "获取流地址"
                    },
                    "playback": {
                        "method": "POST",
                        "path": "/api/v2/cameras/{channel}/playback",
                        "description": "历史回放"
                    }
                },
                "system": {
                    "status": {
                        "method": "GET",
                        "path": "/api/v2/status",
                        "description": "系统状态"
                    },
                    "files": {
                        "list": {
                            "method": "GET",
                            "path": "/api/v2/files",
                            "description": "文件列表"
                        },
                        "delete": {
                            "method": "DELETE",
                            "path": "/api/v2/files/{filename}",
                            "description": "删除文件"
                        }
                    }
                }
            },
            "channels": {
                "available": [1, 2, 3, 4],
                "ptz_enabled": [1, 3],
                "mapping": {
                    "1": "33",
                    "2": "34",
                    "3": "35",
                    "4": "36"
                }
            }
        })
    
    async def snapshot(self, request):
        """抓拍接口"""
        try:
            channel = int(request.match_info['channel'])
            
            # 验证通道号
            if channel not in [1, 2, 3, 4]:
                return web.json_response({
                    "success": False,
                    "error": "Invalid channel number, must be 1-4"
                }, status=400)
            
            # 获取请求参数
            data = await request.json() if request.body_exists else {}
            quality = data.get("quality", 0)
            
            # 执行抓拍
            result = await self.camera_service.capture_snapshot(channel, quality)
            
            if result.get("success"):
                return web.json_response(result)
            else:
                return web.json_response({
                    "success": False,
                    "error": result.get("error", "Snapshot failed")
                }, status=400)
                
        except ValueError:
            return web.json_response({
                "success": False,
                "error": "Invalid channel format"
            }, status=400)
        except Exception as e:
            logger.error(f"Snapshot API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def start_recording(self, request):
        """开始录像接口"""
        try:
            channel = int(request.match_info['channel'])
            
            # 验证通道号
            if channel not in [1, 2, 3, 4]:
                return web.json_response({
                    "success": False,
                    "error": "Invalid channel number, must be 1-4"
                }, status=400)
            
            # 获取请求参数
            data = await request.json() if request.body_exists else {}
            duration = data.get("duration", 0)
            
            # 执行录像
            result = await self.camera_service.start_recording(channel, duration)
            
            if result.get("success"):
                return web.json_response(result)
            else:
                return web.json_response({
                    "success": False,
                    "error": result.get("error", "Start recording failed")
                }, status=400)
                
        except ValueError:
            return web.json_response({
                "success": False,
                "error": "Invalid channel format"
            }, status=400)
        except Exception as e:
            logger.error(f"Start recording API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def stop_recording(self, request):
        """停止录像接口"""
        try:
            channel = int(request.match_info['channel'])
            
            # 验证通道号
            if channel not in [1, 2, 3, 4]:
                return web.json_response({
                    "success": False,
                    "error": "Invalid channel number, must be 1-4"
                }, status=400)
            
            # 停止录像
            result = await self.camera_service.stop_recording(channel)
            
            if result.get("success"):
                return web.json_response(result)
            else:
                return web.json_response({
                    "success": False,
                    "error": result.get("error", "Stop recording failed")
                }, status=400)
                
        except ValueError:
            return web.json_response({
                "success": False,
                "error": "Invalid channel format"
            }, status=400)
        except Exception as e:
            logger.error(f"Stop recording API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_recording_status(self, request):
        """获取录像状态接口"""
        try:
            channel = int(request.match_info['channel'])
            
            # 验证通道号
            if channel not in [1, 2, 3, 4]:
                return web.json_response({
                    "success": False,
                    "error": "Invalid channel number, must be 1-4"
                }, status=400)
            
            # 获取状态
            status = await self.camera_service.get_status()
            recording_channels = status.get("recording_channels", [])
            
            return web.json_response({
                "success": True,
                "data": {
                    "channel": channel,
                    "is_recording": channel in recording_channels,
                    "all_recording_channels": recording_channels
                }
            })
            
        except ValueError:
            return web.json_response({
                "success": False,
                "error": "Invalid channel format"
            }, status=400)
        except Exception as e:
            logger.error(f"Get recording status API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def control_ptz(self, request):
        """PTZ控制接口"""
        try:
            channel = int(request.match_info['channel'])
            
            # 验证通道号
            if channel not in [1, 3]:
                return web.json_response({
                    "success": False,
                    "error": "Channel does not support PTZ, only channel 1 and 3 support PTZ"
                }, status=400)
            
            # 获取请求参数
            data = await request.json()
            action = data.get("action")
            speed = data.get("speed", 50)
            duration = data.get("duration", 1.0)
            
            logger.info(f"PTZ控制请求: channel={channel}, action={action}, data={data}")
            
            if not action:
                return web.json_response({
                    "success": False,
                    "error": "Missing action parameter"
                }, status=400)
            
            # 转换为PTZService期望的格式
            if action.lower() in ["up", "down", "left", "right"]:
                # 方向控制
                ptz_action = "move"
                params = {
                    "direction": action.lower(),
                    "speed": speed,
                    "duration": duration
                }
            elif action.lower() in ["zoom_in", "zoom_out"]:
                # 缩放控制
                ptz_action = "zoom"
                params = {
                    "type": "in" if action.lower() == "zoom_in" else "out",
                    "speed": speed,
                    "duration": duration
                }
            elif action.lower() in ["cruise_start", "cruise_stop"]:
                # 巡航控制
                ptz_action = "cruise"
                params = {
                    "action": "start" if action.lower() == "cruise_start" else "stop",
                    "cruise_id": data.get("cruise_id", 1)
                }
            elif action.lower() == "stop":
                # 停止控制
                ptz_action = "stop"
                params = {}
            else:
                return web.json_response({
                    "success": False,
                    "error": f"Unknown PTZ action: {action}"
                }, status=400)
            
            # 执行PTZ控制
            logger.info(f"PTZ控制参数: ptz_action={ptz_action}, params={params}")
            result = await self.camera_service.control_ptz(channel, ptz_action, params)
            
            if result.get("success"):
                return web.json_response(result)
            else:
                return web.json_response({
                    "success": False,
                    "error": result.get("error", "PTZ control failed")
                }, status=400)
                
        except ValueError:
            return web.json_response({
                "success": False,
                "error": "Invalid channel format"
            }, status=400)
        except Exception as e:
            logger.error(f"PTZ control API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_stream(self, request):
        """获取流地址接口"""
        try:
            channel = int(request.match_info['channel'])
            
            # 验证通道号
            if channel not in [1, 2, 3, 4]:
                return web.json_response({
                    "success": False,
                    "error": "Invalid channel number, must be 1-4"
                }, status=400)
            
            # 获取参数
            if request.method == "GET":
                stream_type = request.query.get("stream_type", "main")
            else:
                data = await request.json() if request.body_exists else {}
                stream_type = data.get("stream_type", "main")
            
            # 获取流地址
            result = await self.camera_service.get_stream_url(channel)
            
            if result.get("success"):
                return web.json_response(result)
            else:
                return web.json_response({
                    "success": False,
                    "error": result.get("error", "Get stream failed")
                }, status=400)
                
        except ValueError:
            return web.json_response({
                "success": False,
                "error": "Invalid channel format"
            }, status=400)
        except Exception as e:
            logger.error(f"Get stream API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def playback(self, request):
        """历史回放接口"""
        try:
            channel = int(request.match_info['channel'])
            
            # 验证通道号
            if channel not in [1, 2, 3, 4]:
                return web.json_response({
                    "success": False,
                    "error": "Invalid channel number, must be 1-4"
                }, status=400)
            
            # 获取请求参数
            data = await request.json()
            start_time = data.get("start_time")
            end_time = data.get("end_time")
            
            if not start_time or not end_time:
                return web.json_response({
                    "success": False,
                    "error": "Missing start_time or end_time parameter"
                }, status=400)
            
            # 转换时间字符串为datetime对象
            from datetime import datetime
            try:
                start_dt = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
                end_dt = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            except ValueError:
                return web.json_response({
                    "success": False,
                    "error": "Invalid time format, use ISO format (e.g., 2025-07-28T10:00:00)"
                }, status=400)
            
            # 执行回放
            result = await self.camera_service.download_playback(channel, start_dt, end_dt)
            
            if result.get("success"):
                return web.json_response(result)
            else:
                return web.json_response({
                    "success": False,
                    "error": result.get("error", "Playback failed")
                }, status=400)
                
        except ValueError:
            return web.json_response({
                "success": False,
                "error": "Invalid channel format"
            }, status=400)
        except Exception as e:
            logger.error(f"Playback API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_status(self, request):
        """获取系统状态接口"""
        try:
            status = await self.camera_service.get_status()
            
            return web.json_response({
                "success": True,
                "data": status
            })
            
        except Exception as e:
            logger.error(f"Get status API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def list_files(self, request):
        """列出文件接口"""
        try:
            file_type = request.query.get("type", "all")
            files = await self.camera_service.list_files()
            
            return web.json_response({
                "success": True,
                "data": {
                    "type": file_type,
                    "count": len(files),
                    "files": files
                }
            })
            
        except Exception as e:
            logger.error(f"List files API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def delete_file(self, request):
        """删除文件接口"""
        try:
            filename = request.match_info['filename']
            
            success = await self.camera_service.delete_file(filename)
            
            if success:
                return web.json_response({
                    "success": True,
                    "message": f"File {filename} deleted successfully"
                })
            else:
                return web.json_response({
                    "success": False,
                    "error": "File not found or delete failed"
                }, status=404)
                
        except Exception as e:
            logger.error(f"Delete file API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    # ==================== 预置点管理API ====================
    
    async def set_preset(self, request):
        """设置预置点"""
        try:
            channel = int(request.match_info['channel'])
            preset_id = int(request.match_info['preset_id'])
            
            result = await self.camera_service.set_preset(channel, preset_id)
            
            if result["success"]:
                return web.json_response(result)
            else:
                return web.json_response(result, status=400)
                
        except ValueError:
            return web.json_response({
                "success": False,
                "error": "Invalid channel or preset_id"
            }, status=400)
        except Exception as e:
            logger.error(f"Set preset API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def call_preset(self, request):
        """调用预置点"""
        try:
            channel = int(request.match_info['channel'])
            preset_id = int(request.match_info['preset_id'])
            
            result = await self.camera_service.call_preset(channel, preset_id)
            
            if result["success"]:
                return web.json_response(result)
            else:
                return web.json_response(result, status=400)
                
        except ValueError:
            return web.json_response({
                "success": False,
                "error": "Invalid channel or preset_id"
            }, status=400)
        except Exception as e:
            logger.error(f"Call preset API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def delete_preset(self, request):
        """删除预置点"""
        try:
            channel = int(request.match_info['channel'])
            preset_id = int(request.match_info['preset_id'])
            
            result = await self.camera_service.delete_preset(channel, preset_id)
            
            if result["success"]:
                return web.json_response(result)
            else:
                return web.json_response(result, status=400)
                
        except ValueError:
            return web.json_response({
                "success": False,
                "error": "Invalid channel or preset_id"
            }, status=400)
        except Exception as e:
            logger.error(f"Delete preset API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    # ==================== 高级录像功能API ====================
    
    async def preset_recording(self, request):
        """定点录像功能"""
        try:
            channel = int(request.match_info['channel'])
            data = await request.json()
            
            preset_id = data.get('preset_id')
            duration = data.get('duration', 300)  # 默认5分钟
            
            if preset_id is None:
                return web.json_response({
                    "success": False,
                    "error": "preset_id is required"
                }, status=400)
            
            result = await self.camera_service.preset_recording(channel, preset_id, duration)
            
            if result["success"]:
                return web.json_response(result)
            else:
                return web.json_response(result, status=400)
                
        except ValueError:
            return web.json_response({
                "success": False,
                "error": "Invalid channel"
            }, status=400)
        except Exception as e:
            logger.error(f"Preset recording API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def terrain_recording(self, request):
        """地形扫描录像功能"""
        try:
            channel = int(request.match_info['channel'])
            data = await request.json()
            
            duration = data.get('duration', 1800)     # 默认30分钟
            tilt_step = data.get('tilt_step', 15)     # 默认15度步进
            pan_speed = data.get('pan_speed', 2)      # 默认速度2
            
            result = await self.camera_service.terrain_recording(
                channel, duration, tilt_step, pan_speed
            )
            
            if result["success"]:
                return web.json_response(result)
            else:
                return web.json_response(result, status=400)
                
        except ValueError:
            return web.json_response({
                "success": False,
                "error": "Invalid channel"
            }, status=400)
        except Exception as e:
            logger.error(f"Terrain recording API error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)
    
    async def get_metrics(self, request):
        """获取服务指标"""
        try:
            metrics = await self.camera_service.get_metrics()
            
            return web.json_response({
                "success": True,
                "data": metrics
            })
            
        except Exception as e:
            logger.error(f"Get metrics error: {e}")
            return web.json_response({
                "success": False,
                "error": str(e)
            }, status=500)