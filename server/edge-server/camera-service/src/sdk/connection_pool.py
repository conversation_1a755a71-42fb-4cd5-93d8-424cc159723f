#!/usr/bin/env python3
"""
SDK连接池管理器
提供连接池管理、健康检查和自动重连机制
"""

import asyncio
import logging
import time
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from threading import Lock

logger = logging.getLogger(__name__)


class ConnectionPool:
    """SDK连接池管理器"""
    
    def __init__(self, sdk_factory, nvr_config: Dict[str, Any], pool_config: Optional[Dict[str, Any]] = None):
        """初始化连接池
        
        Args:
            sdk_factory: SDK工厂函数，用于创建SDK实例
            nvr_config: NVR配置信息
            pool_config: 连接池配置
        """
        self.sdk_factory = sdk_factory
        self.nvr_config = nvr_config
        self.pool_config = pool_config or {}
        
        # 连接池配置
        self.max_connections = self.pool_config.get('max_connections', 1)  # 海康SDK通常只支持单连接
        self.health_check_interval = self.pool_config.get('health_check_interval', 30)  # 健康检查间隔（秒）
        self.reconnect_delay = self.pool_config.get('reconnect_delay', 5)  # 重连延迟（秒）
        self.max_reconnect_attempts = self.pool_config.get('max_reconnect_attempts', 3)  # 最大重连次数
        
        # 连接状态
        self.sdk = None
        self.is_connected = False
        self.last_health_check = None
        self.connection_time = None
        self.reconnect_attempts = 0
        
        # 线程安全锁
        self._lock = Lock()
        
        # 健康检查任务
        self.health_check_task = None
        
        # 统计信息
        self.stats = {
            'total_connections': 0,
            'failed_connections': 0,
            'reconnections': 0,
            'health_checks': 0,
            'last_error': None
        }
    
    async def initialize(self) -> bool:
        """初始化连接池"""
        try:
            logger.info("初始化SDK连接池...")
            
            # 创建SDK实例
            self.sdk = self.sdk_factory()
            
            # 初始化SDK
            if not self.sdk.initialize():
                logger.error("SDK初始化失败")
                return False
            
            # 建立连接
            if not await self._connect():
                logger.error("初始连接失败")
                return False
            
            # 启动健康检查
            self.health_check_task = asyncio.create_task(self._health_check_loop())
            
            logger.info("SDK连接池初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"连接池初始化异常: {e}")
            self.stats['last_error'] = str(e)
            return False
    
    async def cleanup(self):
        """清理连接池资源"""
        try:
            logger.info("清理SDK连接池...")
            
            # 停止健康检查
            if self.health_check_task and not self.health_check_task.done():
                self.health_check_task.cancel()
                try:
                    await self.health_check_task
                except asyncio.CancelledError:
                    pass
            
            # 断开连接
            await self._disconnect()
            
            # 清理SDK
            if self.sdk:
                self.sdk.cleanup()
                self.sdk = None
            
            logger.info("SDK连接池已清理")
            
        except Exception as e:
            logger.error(f"连接池清理异常: {e}")
    
    def get_connection(self):
        """获取SDK连接
        
        Returns:
            SDK实例，如果连接不可用返回None
        """
        with self._lock:
            if self.is_connected and self.sdk and self.sdk.user_id >= 0:
                return self.sdk
            else:
                logger.warning("连接不可用")
                return None
    
    async def execute_with_retry(self, func, *args, **kwargs):
        """使用重试机制执行SDK操作
        
        Args:
            func: 要执行的函数
            *args, **kwargs: 函数参数
            
        Returns:
            函数执行结果
        """
        max_retries = 2
        retry_count = 0
        
        while retry_count <= max_retries:
            sdk = self.get_connection()
            if not sdk:
                # 尝试重连
                logger.warning("连接不可用，尝试重连...")
                if await self._reconnect():
                    sdk = self.get_connection()
                else:
                    raise Exception("无法获取有效连接")
            
            try:
                # 执行操作
                result = await asyncio.get_event_loop().run_in_executor(
                    None, func, *args, **kwargs
                )
                return result
                
            except Exception as e:
                logger.error(f"SDK操作失败: {e}")
                retry_count += 1
                
                if retry_count > max_retries:
                    raise
                
                # 检查是否需要重连
                if "登录" in str(e) or "连接" in str(e):
                    logger.info(f"检测到连接问题，尝试重连 ({retry_count}/{max_retries})")
                    await self._reconnect()
                
                await asyncio.sleep(1)
    
    async def _connect(self) -> bool:
        """建立连接"""
        try:
            with self._lock:
                if self.is_connected:
                    return True
                
                logger.info("正在连接NVR...")
                
                # 登录NVR
                success = self.sdk.login(
                    self.nvr_config['ip'],
                    self.nvr_config['port'],
                    self.nvr_config['username'],
                    self.nvr_config['password']
                )
                
                if success:
                    self.is_connected = True
                    self.connection_time = datetime.now()
                    self.reconnect_attempts = 0
                    self.stats['total_connections'] += 1
                    logger.info("NVR连接成功")
                    return True
                else:
                    self.stats['failed_connections'] += 1
                    logger.error("NVR连接失败")
                    return False
                    
        except Exception as e:
            logger.error(f"连接异常: {e}")
            self.stats['last_error'] = str(e)
            return False
    
    async def _disconnect(self):
        """断开连接"""
        try:
            with self._lock:
                if self.sdk and self.is_connected:
                    logger.info("断开NVR连接...")
                    self.sdk.logout()
                    self.is_connected = False
                    self.connection_time = None
                    
        except Exception as e:
            logger.error(f"断开连接异常: {e}")
    
    async def _reconnect(self) -> bool:
        """重新连接"""
        try:
            if self.reconnect_attempts >= self.max_reconnect_attempts:
                logger.error(f"重连次数超过限制 ({self.max_reconnect_attempts})")
                return False
            
            self.reconnect_attempts += 1
            logger.info(f"尝试重连 ({self.reconnect_attempts}/{self.max_reconnect_attempts})...")
            
            # 先断开现有连接
            await self._disconnect()
            
            # 等待一段时间
            await asyncio.sleep(self.reconnect_delay)
            
            # 重新连接
            if await self._connect():
                self.stats['reconnections'] += 1
                logger.info("重连成功")
                return True
            else:
                logger.error("重连失败")
                return False
                
        except Exception as e:
            logger.error(f"重连异常: {e}")
            return False
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self.health_check_interval)
                await self._perform_health_check()
                
            except asyncio.CancelledError:
                logger.info("健康检查任务已取消")
                break
                
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
    
    async def _perform_health_check(self):
        """执行健康检查"""
        try:
            self.stats['health_checks'] += 1
            self.last_health_check = datetime.now()
            
            with self._lock:
                if not self.is_connected or not self.sdk or self.sdk.user_id < 0:
                    logger.warning("健康检查失败：连接已断开")
                    self.is_connected = False
                    
                    # 尝试重连
                    asyncio.create_task(self._reconnect())
                    return
            
            # 可以添加更多健康检查逻辑，比如执行一个简单的SDK操作
            # 这里暂时只检查连接状态
            
            logger.debug("健康检查通过")
            
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            self.is_connected = False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        stats = self.stats.copy()
        stats.update({
            'is_connected': self.is_connected,
            'connection_time': self.connection_time.isoformat() if self.connection_time else None,
            'last_health_check': self.last_health_check.isoformat() if self.last_health_check else None,
            'uptime': str(datetime.now() - self.connection_time) if self.connection_time else None,
            'reconnect_attempts': self.reconnect_attempts
        })
        return stats