#!/usr/bin/env python3
"""
海康威视SDK封装（优化版）
提供与硬件无关的SDK接口抽象
"""

import os
import ctypes
import threading
import logging
from typing import Optional, Dict, Any, Tuple
from datetime import datetime
from pathlib import Path
from ctypes import c_long, c_uint32, c_byte, byref, POINTER, c_char, c_char_p

# 导入结构体和常量
from .structures import (
    NET_DVR_DEVICEINFO_V30, NET_DVR_JPEGPARA, 
    NET_DVR_CLIENTINFO, NET_DVR_TIME,
    NET_DVR_FINDDATA_V40, NET_DVR_FILECOND_V40
)
from .constants import (
    PTZ_COMMANDS, PRESET_SET, PRESET_CLE, PRESET_GOTO,
    FILE_TYPE_ALL, ERROR_CODES
)

logger = logging.getLogger(__name__)


class HikvisionSDK:
    """海康威视SDK封装类"""
    
    def __init__(self):
        """初始化SDK"""
        self.sdk = None
        self.user_id = -1
        self.device_info = None
        self._lock = threading.Lock()
        self._real_play_handles = {}
        self._recording_handles = {}
        self._playback_handles = {}
        
        # 查找SDK路径
        sdk_paths = [
            # 相机服务内的SDK
            os.path.join(os.path.dirname(__file__), '../../lib/libhcnetsdk.so'),
            '/home/<USER>/server/edge-server/camera-service/lib/libhcnetsdk.so',
            # 第三方SDK目录
            '/home/<USER>/server/third-party/hikvision-sdk/lib/libhcnetsdk.so',
            '/home/<USER>/server/third-party/hikvision-sdk/库文件/libhcnetsdk.so',
            # 系统目录
            '/usr/lib/libhcnetsdk.so',
            '/usr/local/lib/libhcnetsdk.so'
        ]
        
        sdk_path = None
        for path in sdk_paths:
            abs_path = os.path.abspath(path)
            if os.path.exists(abs_path):
                sdk_path = abs_path
                break
        
        if not sdk_path:
            raise RuntimeError(f"找不到海康SDK库文件，搜索路径: {sdk_paths}")
        
        logger.info(f"加载SDK: {sdk_path}")
        
        try:
            # 设置库搜索路径
            sdk_dir = os.path.dirname(sdk_path)
            if hasattr(os, 'add_dll_directory'):
                os.add_dll_directory(sdk_dir)
            else:
                os.environ['LD_LIBRARY_PATH'] = f"{sdk_dir}:{os.environ.get('LD_LIBRARY_PATH', '')}"
            
            # 加载SDK
            self.sdk = ctypes.CDLL(sdk_path)
            self._setup_sdk_functions()
            
            # 初始化SDK
            if not self.sdk.NET_DVR_Init():
                raise RuntimeError("SDK初始化失败")
            
            # 设置连接参数
            self.sdk.NET_DVR_SetConnectTime(5000, 1)
            self.sdk.NET_DVR_SetReconnect(10000, 1)
            
            logger.info("海康SDK初始化成功")
            
        except Exception as e:
            logger.error(f"SDK初始化失败: {e}")
            raise
    
    def _setup_sdk_functions(self):
        """设置SDK函数原型"""
        # 基础函数
        self.sdk.NET_DVR_Init.restype = c_long
        self.sdk.NET_DVR_Cleanup.restype = c_long
        self.sdk.NET_DVR_GetLastError.restype = c_uint32
        
        # 登录登出
        self.sdk.NET_DVR_Login_V30.restype = c_long
        self.sdk.NET_DVR_Login_V30.argtypes = [
            c_char_p, c_uint32, c_char_p, c_char_p,
            POINTER(NET_DVR_DEVICEINFO_V30)
        ]
        self.sdk.NET_DVR_Logout.restype = c_long
        self.sdk.NET_DVR_Logout.argtypes = [c_long]
        
        # 抓拍功能
        self.sdk.NET_DVR_CaptureJPEGPicture.restype = c_long
        self.sdk.NET_DVR_CaptureJPEGPicture.argtypes = [
            c_long, c_long, POINTER(NET_DVR_JPEGPARA), c_char_p
        ]
        
        # 实时预览
        self.sdk.NET_DVR_RealPlay_V30.restype = c_long
        self.sdk.NET_DVR_RealPlay_V30.argtypes = [
            c_long, POINTER(NET_DVR_CLIENTINFO), c_long, c_long, c_long
        ]
        self.sdk.NET_DVR_StopRealPlay.restype = c_long
        self.sdk.NET_DVR_StopRealPlay.argtypes = [c_long]
        
        # 录像保存
        self.sdk.NET_DVR_SaveRealData.restype = c_long
        self.sdk.NET_DVR_SaveRealData.argtypes = [c_long, c_char_p]
        self.sdk.NET_DVR_StopSaveRealData.restype = c_long
        self.sdk.NET_DVR_StopSaveRealData.argtypes = [c_long]
        
        # PTZ控制
        self.sdk.NET_DVR_PTZControlWithSpeed_Other.restype = c_long
        self.sdk.NET_DVR_PTZControlWithSpeed_Other.argtypes = [
            c_long, c_long, c_uint32, c_uint32, c_uint32
        ]
        
        # 预置点
        self.sdk.NET_DVR_PTZPreset_Other.restype = c_long
        self.sdk.NET_DVR_PTZPreset_Other.argtypes = [
            c_long, c_long, c_uint32, c_uint32
        ]
        
        # 历史回放
        self.sdk.NET_DVR_PlayBackByTime.restype = c_long
        self.sdk.NET_DVR_PlayBackSaveData.restype = c_long
        self.sdk.NET_DVR_PlayBackControl.restype = c_long
        self.sdk.NET_DVR_StopPlayBack.restype = c_long
        self.sdk.NET_DVR_GetPlayBackPos.restype = c_long
    
    def login(self, ip: str, port: int, username: str, password: str) -> bool:
        """登录设备"""
        try:
            with self._lock:
                if self.user_id >= 0:
                    logger.warning("已经登录，先登出")
                    self.logout()
                
                self.device_info = NET_DVR_DEVICEINFO_V30()
                
                # 保存登录信息供后续使用
                self._device_ip = ip
                self._username = username
                self._password = password
                
                self.user_id = self.sdk.NET_DVR_Login_V30(
                    ip.encode('utf-8'),
                    port,
                    username.encode('utf-8'),
                    password.encode('utf-8'),
                    byref(self.device_info)
                )
                
                if self.user_id < 0:
                    error_code = self.sdk.NET_DVR_GetLastError()
                    error_msg = ERROR_CODES.get(error_code, f"未知错误({error_code})")
                    logger.error(f"登录失败: {error_msg}")
                    return False
                
                logger.info(f"登录成功, user_id={self.user_id}")
                logger.info(f"设备信息: 通道数={self.device_info.byChanNum}, "
                          f"起始通道={self.device_info.byStartChan}, "
                          f"IP通道数={self.device_info.byIPChanNum}")
                
                return True
                
        except Exception as e:
            logger.error(f"登录异常: {e}")
            return False
    
    def logout(self) -> bool:
        """登出设备"""
        try:
            with self._lock:
                if self.user_id < 0:
                    return True
                
                # 停止所有预览和录像
                self._cleanup_handles()
                
                if self.sdk.NET_DVR_Logout(self.user_id):
                    logger.info("登出成功")
                    self.user_id = -1
                    self.device_info = None
                    return True
                else:
                    logger.error("登出失败")
                    return False
                    
        except Exception as e:
            logger.error(f"登出异常: {e}")
            return False
    
    def capture_picture(self, channel: int, save_path: str, quality: int = 0) -> bool:
        """抓拍图片"""
        try:
            if self.user_id < 0:
                logger.error("未登录")
                return False
            
            # 创建目录
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 设置图片参数
            jpeg_param = NET_DVR_JPEGPARA()
            jpeg_param.wPicSize = 0  # 0=CIF, 1=QCIF, 2=D1, 3=UXGA...
            jpeg_param.wPicQuality = quality  # 0=最好, 1=较好, 2=一般
            
            # 执行抓拍
            if self.sdk.NET_DVR_CaptureJPEGPicture(
                self.user_id, 
                channel, 
                byref(jpeg_param), 
                save_path.encode('utf-8')
            ):
                logger.info(f"抓拍成功: {save_path}")
                return True
            else:
                error_code = self.sdk.NET_DVR_GetLastError()
                logger.error(f"抓拍失败: {ERROR_CODES.get(error_code, error_code)}")
                return False
                
        except Exception as e:
            logger.error(f"抓拍异常: {e}")
            return False
    
    def start_recording(self, channel: int, save_path: str) -> Optional[int]:
        """开始录像"""
        try:
            if self.user_id < 0:
                logger.error("未登录")
                return None
            
            # 创建目录
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 开始预览
            client_info = NET_DVR_CLIENTINFO()
            client_info.lChannel = channel
            client_info.lLinkMode = 0  # TCP
            client_info.hPlayWnd = 0
            
            play_handle = self.sdk.NET_DVR_RealPlay_V30(
                self.user_id,
                byref(client_info),
                0,  # 回调函数设为0
                0,  # 用户数据设为0
                1   # 是否阻塞
            )
            
            if play_handle < 0:
                error_code = self.sdk.NET_DVR_GetLastError()
                logger.error(f"预览失败: {ERROR_CODES.get(error_code, error_code)}")
                return None
            
            # 保存数据
            if not self.sdk.NET_DVR_SaveRealData(play_handle, save_path.encode('utf-8')):
                self.sdk.NET_DVR_StopRealPlay(play_handle)
                error_code = self.sdk.NET_DVR_GetLastError()
                logger.error(f"保存失败: {ERROR_CODES.get(error_code, error_code)}")
                return None
            
            # 记录句柄
            with self._lock:
                self._real_play_handles[channel] = play_handle
                self._recording_handles[channel] = play_handle
            
            logger.info(f"开始录像: 通道{channel} -> {save_path}")
            return play_handle
            
        except Exception as e:
            logger.error(f"开始录像异常: {e}")
            return None
    
    def stop_recording(self, channel: int) -> bool:
        """停止录像"""
        try:
            with self._lock:
                play_handle = self._recording_handles.get(channel)
                if play_handle is None:
                    logger.warning(f"通道{channel}未在录像")
                    return True
                
                # 停止保存
                if not self.sdk.NET_DVR_StopSaveRealData(play_handle):
                    logger.error("停止保存数据失败")
                
                # 停止预览
                if not self.sdk.NET_DVR_StopRealPlay(play_handle):
                    logger.error("停止预览失败")
                
                # 清理句柄
                self._real_play_handles.pop(channel, None)
                self._recording_handles.pop(channel, None)
                
                logger.info(f"停止录像: 通道{channel}")
                return True
                
        except Exception as e:
            logger.error(f"停止录像异常: {e}")
            return False
    
    def ptz_control(self, channel: int, command: str, stop: bool = False, speed: int = 4) -> bool:
        """PTZ控制"""
        try:
            if self.user_id < 0:
                logger.error("未登录")
                return False
            
            # 获取命令码
            cmd_code = PTZ_COMMANDS.get(command.lower())
            if cmd_code is None:
                logger.error(f"未知PTZ命令: {command}")
                return False
            
            # 执行控制
            if self.sdk.NET_DVR_PTZControlWithSpeed_Other(
                self.user_id,
                channel,
                cmd_code,
                1 if stop else 0,
                speed
            ):
                action = "停止" if stop else "开始"
                logger.info(f"PTZ控制: {action} {command} 速度={speed}")
                return True
            else:
                error_code = self.sdk.NET_DVR_GetLastError()
                logger.error(f"PTZ控制失败: {ERROR_CODES.get(error_code, error_code)}")
                return False
                
        except Exception as e:
            logger.error(f"PTZ控制异常: {e}")
            return False
    
    def preset_control(self, channel: int, preset_id: int, command: str) -> bool:
        """预置点控制"""
        try:
            if self.user_id < 0:
                logger.error("未登录")
                return False
            
            # 命令映射
            cmd_map = {
                'set': PRESET_SET,
                'call': PRESET_GOTO,
                'goto': PRESET_GOTO,
                'delete': PRESET_CLE,
                'clear': PRESET_CLE
            }
            
            cmd_code = cmd_map.get(command.lower())
            if cmd_code is None:
                logger.error(f"未知预置点命令: {command}")
                return False
            
            # 执行命令
            if self.sdk.NET_DVR_PTZPreset_Other(
                self.user_id,
                channel,
                cmd_code,
                preset_id
            ):
                logger.info(f"预置点控制: {command} 预置点{preset_id}")
                return True
            else:
                error_code = self.sdk.NET_DVR_GetLastError()
                logger.error(f"预置点控制失败: {ERROR_CODES.get(error_code, error_code)}")
                return False
                
        except Exception as e:
            logger.error(f"预置点控制异常: {e}")
            return False
    
    def get_stream_url(self, channel: int) -> Dict[str, str]:
        """获取流媒体URL"""
        if not self.device_info:
            return {}
        
        # 从设备信息获取
        device_ip = getattr(self, '_device_ip', '*************')
        username = getattr(self, '_username', 'admin')
        password = getattr(self, '_password', 'password')
        
        # 生成URL
        rtsp_url = f"rtsp://{username}:{password}@{device_ip}:554/Streaming/Channels/{channel}01"
        http_url = f"http://{device_ip}/ISAPI/Streaming/channels/{channel}01/httppreview"
        
        return {
            "rtsp_url": rtsp_url,
            "http_url": http_url
        }
    
    def download_playback(self, channel: int, start_time: datetime, end_time: datetime, 
                         save_path: str) -> bool:
        """下载历史录像"""
        try:
            if self.user_id < 0:
                logger.error("未登录")
                return False
            
            # 创建目录
            os.makedirs(os.path.dirname(save_path), exist_ok=True)
            
            # 转换时间
            start = NET_DVR_TIME()
            start.dwYear = start_time.year
            start.dwMonth = start_time.month
            start.dwDay = start_time.day
            start.dwHour = start_time.hour
            start.dwMinute = start_time.minute
            start.dwSecond = start_time.second
            
            end = NET_DVR_TIME()
            end.dwYear = end_time.year
            end.dwMonth = end_time.month
            end.dwDay = end_time.day
            end.dwHour = end_time.hour
            end.dwMinute = end_time.minute
            end.dwSecond = end_time.second
            
            # 开始回放
            play_handle = self.sdk.NET_DVR_PlayBackByTime(
                self.user_id,
                channel,
                byref(start),
                byref(end),
                0
            )
            
            if play_handle < 0:
                error_code = self.sdk.NET_DVR_GetLastError()
                logger.error(f"开始回放失败: {ERROR_CODES.get(error_code, error_code)}")
                return False
            
            # 保存数据
            if not self.sdk.NET_DVR_PlayBackSaveData(play_handle, save_path.encode('utf-8')):
                self.sdk.NET_DVR_StopPlayBack(play_handle)
                logger.error("保存回放数据失败")
                return False
            
            # 开始下载
            if not self.sdk.NET_DVR_PlayBackControl(play_handle, 1, 0, None):
                self.sdk.NET_DVR_StopPlayBack(play_handle)
                logger.error("开始下载失败")
                return False
            
            # 等待下载完成
            import time
            while True:
                pos = self.sdk.NET_DVR_GetPlayBackPos(play_handle)
                if pos >= 100:
                    break
                elif pos < 0:
                    logger.error("获取进度失败")
                    break
                time.sleep(0.1)
            
            # 停止回放
            self.sdk.NET_DVR_StopPlayBack(play_handle)
            
            logger.info(f"下载完成: {save_path}")
            return True
            
        except Exception as e:
            logger.error(f"下载回放异常: {e}")
            return False
    
    def _cleanup_handles(self):
        """清理所有句柄"""
        # 停止所有录像
        for channel, handle in list(self._recording_handles.items()):
            self.stop_recording(channel)
        
        # 停止所有预览
        for channel, handle in list(self._real_play_handles.items()):
            self.sdk.NET_DVR_StopRealPlay(handle)
        
        # 停止所有回放
        for handle in list(self._playback_handles.values()):
            self.sdk.NET_DVR_StopPlayBack(handle)
        
        # 清空记录
        self._real_play_handles.clear()
        self._recording_handles.clear()
        self._playback_handles.clear()
    
    def cleanup(self):
        """清理SDK资源"""
        try:
            self.logout()
            if self.sdk:
                self.sdk.NET_DVR_Cleanup()
                logger.info("SDK清理完成")
        except Exception as e:
            logger.error(f"SDK清理异常: {e}")
    
    def __del__(self):
        """析构函数"""
        self.cleanup()