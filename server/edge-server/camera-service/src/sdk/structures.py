"""
海康SDK数据结构定义
"""
import ctypes
from ctypes import c_char, c_byte, c_int32, c_uint32, c_long, c_ulong, Structure, Union


class NET_DVR_DEVICEINFO_V30(Structure):
    """设备信息结构体"""
    _fields_ = [
        ("sSerialNumber", c_byte * 48),
        ("byAlarmInPortNum", c_byte),
        ("byAlarmOutPortNum", c_byte),
        ("byDiskNum", c_byte),
        ("byDVRType", c_byte),
        ("byChanNum", c_byte),
        ("byStartChan", c_byte),
        ("byAudioChanNum", c_byte),
        ("byIPChanNum", c_byte),
        ("byZeroChanNum", c_byte),
        ("byMainProto", c_byte),
        ("bySubProto", c_byte),
        ("bySupport", c_byte),
        ("bySupport1", c_byte),
        ("bySupport2", c_byte),
        ("wDevType", c_uint32),
        ("bySupport3", c_byte),
        ("byMultiStreamProto", c_byte),
        ("byStartDChan", c_byte),
        ("byStartDTalkChan", c_byte),
        ("byHighDChanNum", c_byte),
        ("bySupport4", c_byte),
        ("byLanguageType", c_byte),
        ("byVoiceInChanNum", c_byte),
        ("byStartVoiceInChanNo", c_byte),
        ("byRes3", c_byte * 2),
        ("byMirrorChanNum", c_byte),
        ("wStartMirrorChanNo", c_uint32),
        ("byRes2", c_byte * 2)
    ]


class NET_DVR_JPEGPARA(Structure):
    """JPEG图片参数"""
    _fields_ = [
        ("wPicSize", c_uint32),
        ("wPicQuality", c_uint32)
    ]


class NET_DVR_CLIENTINFO(Structure):
    """客户端信息"""
    _fields_ = [
        ("lChannel", c_long),
        ("lLinkMode", c_long),
        ("hPlayWnd", c_ulong),
        ("sMultiCastIP", c_char * 16),
        ("byProtoType", c_byte),
        ("byRes", c_byte * 3)
    ]


class NET_DVR_TIME(Structure):
    """时间结构体"""
    _fields_ = [
        ("dwYear", c_uint32),
        ("dwMonth", c_uint32),
        ("dwDay", c_uint32),
        ("dwHour", c_uint32),
        ("dwMinute", c_uint32),
        ("dwSecond", c_uint32)
    ]


class NET_DVR_FINDDATA_V40(Structure):
    """录像文件查找结果V40"""
    _fields_ = [
        ("sFileName", c_char * 100),
        ("struStartTime", NET_DVR_TIME),
        ("struStopTime", NET_DVR_TIME),
        ("dwFileSize", c_uint32),
        ("sCardNum", c_char * 32),
        ("byLocked", c_byte),
        ("byFileType", c_byte),
        ("byRes", c_byte * 2)
    ]


class NET_DVR_FILECOND_V40(Structure):
    """录像文件查找条件V40"""
    _fields_ = [
        ("lChannel", c_long),
        ("dwFileType", c_uint32),
        ("dwIsLocked", c_uint32),
        ("dwUseCardNo", c_uint32),
        ("sCardNumber", c_char * 32),
        ("struStartTime", NET_DVR_TIME),
        ("struStopTime", NET_DVR_TIME),
        ("byDrawFrame", c_byte),
        ("byFindType", c_byte),
        ("byQuickSearch", c_byte),
        ("byRes", c_byte * 1),
        ("dwVolumeNum", c_uint32),
        ("byWorkingDeviceGUID", c_byte * 16),
        ("byRes1", c_byte * 940)
    ]