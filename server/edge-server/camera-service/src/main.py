#!/usr/bin/env python3
"""
相机服务主入口
"""

import os
import sys
import asyncio
import logging
import signal
import argparse
from pathlib import Path
from aiohttp import web
import yaml

# 添加src目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api.server import APIServer
from services.camera_service import CameraService

# 应用优化补丁
try:
    import services.camera_service_patch
except ImportError:
    logger.warning("无法加载优化补丁")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CameraServiceApp:
    """相机服务应用"""
    
    def __init__(self, config_path: str = None):
        """初始化应用
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self.load_config(config_path)
        self.camera_service = None
        self.api_server = None
        self.runner = None
        self.site = None
    
    def load_config(self, config_path: str = None) -> dict:
        """加载配置
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            配置字典
        """
        # 默认配置
        default_config = {
            'server': {
                'host': '0.0.0.0',
                'port': 7090
            },
            'nvr': {
                'ip': os.environ.get('NVR_IP', '*************'),
                'port': int(os.environ.get('NVR_PORT', '8000')),
                'username': os.environ.get('NVR_USERNAME', 'admin'),
                'password': os.environ.get('NVR_PASSWORD', 'Dhdjktsz')
            },
            'sdk': {
                'path': os.environ.get('SDK_PATH', '/home/<USER>/server/edge-server/camera-service/lib/libhcnetsdk.so')
            },
            'data': {
                'dir': os.environ.get('DATA_DIR', '/data/camera')
            },
            'logging': {
                'level': os.environ.get('LOG_LEVEL', 'INFO')
            }
        }
        
        # 如果指定了配置文件，加载并合并
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    file_config = yaml.safe_load(f)
                    
                # 深度合并配置
                self._merge_config(default_config, file_config)
                logger.info(f"配置文件加载成功: {config_path}")
            except Exception as e:
                logger.error(f"配置文件加载失败: {e}")
        
        return default_config
    
    def _merge_config(self, base: dict, update: dict):
        """深度合并配置
        
        Args:
            base: 基础配置
            update: 更新配置
        """
        for key, value in update.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_config(base[key], value)
            else:
                base[key] = value
    
    async def initialize(self):
        """初始化服务"""
        try:
            # 设置日志级别
            log_level = getattr(logging, self.config['logging']['level'].upper())
            logging.getLogger().setLevel(log_level)
            
            # 创建相机服务
            camera_config = {
                'nvr': self.config['nvr'],
                'sdk_path': self.config['sdk']['path'],
                'data_dir': self.config['data']['dir']
            }
            self.camera_service = CameraService(camera_config)
            
            # 初始化相机服务
            if not await self.camera_service.initialize():
                logger.error("相机服务初始化失败")
                return False
            
            # 创建API服务器
            self.api_server = APIServer(self.camera_service)
            
            # 创建Web应用运行器
            self.runner = web.AppRunner(self.api_server.app)
            await self.runner.setup()
            
            # 创建TCP站点
            host = self.config['server']['host']
            port = self.config['server']['port']
            self.site = web.TCPSite(self.runner, host, port)
            await self.site.start()
            
            logger.info("=" * 60)
            logger.info("相机服务已启动")
            logger.info(f"REST API: http://{host}:{port}")
            logger.info(f"API文档: http://{host}:{port}/api/v2")
            logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            logger.error(f"服务初始化异常: {e}")
            return False
    
    async def cleanup(self):
        """清理服务资源"""
        try:
            # 停止Web站点
            if self.site:
                await self.site.stop()
            
            # 清理运行器
            if self.runner:
                await self.runner.cleanup()
            
            # 清理相机服务
            if self.camera_service:
                await self.camera_service.cleanup()
            
            logger.info("服务资源已清理")
            
        except Exception as e:
            logger.error(f"清理服务资源异常: {e}")
    
    async def run(self):
        """运行服务"""
        # 初始化服务
        if not await self.initialize():
            logger.error("服务启动失败")
            return
        
        # 设置信号处理
        def signal_handler(sig, frame):
            logger.info(f"收到信号 {sig}")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # 保持运行
            await asyncio.Event().wait()
        except asyncio.CancelledError:
            logger.info("服务停止")
    
    async def shutdown(self):
        """关闭服务"""
        logger.info("正在关闭服务...")
        await self.cleanup()
        
        # 取消所有任务
        tasks = [t for t in asyncio.all_tasks() if t is not asyncio.current_task()]
        for task in tasks:
            task.cancel()
        
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # 停止事件循环
        asyncio.get_event_loop().stop()


def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='相机服务')
    parser.add_argument('-c', '--config', help='配置文件路径')
    parser.add_argument('--host', help='服务监听地址')
    parser.add_argument('--port', type=int, help='服务监听端口')
    args = parser.parse_args()
    
    # 创建应用实例
    app = CameraServiceApp(args.config)
    
    # 覆盖配置
    if args.host:
        app.config['server']['host'] = args.host
    if args.port:
        app.config['server']['port'] = args.port
    
    # 运行应用
    try:
        asyncio.run(app.run())
    except KeyboardInterrupt:
        logger.info("服务已停止")


if __name__ == "__main__":
    main()