"""
简单的监控指标收集
轻量级实现，不影响性能
"""

import time
import json
from collections import defaultdict, deque
from datetime import datetime
from typing import Dict, Any

class SimpleMetrics:
    """简单指标收集器"""
    
    def __init__(self):
        # 计数器
        self.counters = defaultdict(int)
        # 最近的错误（最多保存100条）
        self.errors = deque(maxlen=100)
        # 开始时间
        self.start_time = time.time()
        # 简单的耗时统计（保存最近100次）
        self.recent_durations = defaultdict(lambda: deque(maxlen=100))
        
    def increment(self, name: str):
        """增加计数器"""
        self.counters[name] += 1
        
    def record_error(self, operation: str, error: str, channel: int = None):
        """记录错误"""
        self.errors.append({
            'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'operation': operation,
            'error': str(error)[:200],  # 限制错误信息长度
            'channel': channel
        })
        self.increment(f"errors_{operation}")
        
    def record_duration(self, operation: str, duration: float):
        """记录操作耗时"""
        self.recent_durations[operation].append(duration)
        
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        uptime_hours = (time.time() - self.start_time) / 3600
        
        # 计算平均耗时
        avg_durations = {}
        for op, durations in self.recent_durations.items():
            if durations:
                avg_durations[op] = {
                    'avg_ms': round(sum(durations) / len(durations) * 1000, 2),
                    'max_ms': round(max(durations) * 1000, 2),
                    'samples': len(durations)
                }
        
        # 统计通道使用情况
        channel_stats = {}
        for key, value in self.counters.items():
            if key.startswith('channel_'):
                channel = key.split('_')[1]
                if channel not in channel_stats:
                    channel_stats[channel] = {}
                operation = '_'.join(key.split('_')[2:])
                channel_stats[channel][operation] = value
        
        return {
            'uptime_hours': round(uptime_hours, 2),
            'total_operations': {
                'snapshots': self.counters.get('snapshot_success', 0),
                'recordings': self.counters.get('recording_start', 0),
                'ptz_controls': self.counters.get('ptz_control', 0),
                'errors': len(self.errors)
            },
            'performance': avg_durations,
            'channel_usage': channel_stats,
            'recent_errors': list(self.errors)[-10:],  # 最近10个错误
            'hourly_rate': {
                'snapshots': round(self.counters.get('snapshot_success', 0) / uptime_hours, 2),
                'recordings': round(self.counters.get('recording_start', 0) / uptime_hours, 2)
            }
        }
    
    def reset_counters(self):
        """重置计数器（可选的每日重置）"""
        self.counters.clear()
        self.recent_durations.clear()
        self.errors.clear()
        self.start_time = time.time()