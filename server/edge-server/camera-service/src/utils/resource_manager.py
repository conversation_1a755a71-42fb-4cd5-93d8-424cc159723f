"""
简单的资源管理器
统一管理SDK句柄，防止资源泄露
"""

import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class ResourceManager:
    """资源管理器 - 简单实现"""
    
    def __init__(self, sdk_instance):
        self.sdk = sdk_instance
        self.handles = {}  # {resource_key: handle}
        self._lock = asyncio.Lock()
        
    async def get_preview_handle(self, channel: int) -> Optional[int]:
        """获取或创建预览句柄"""
        key = f"preview_{channel}"
        
        async with self._lock:
            # 复用已有句柄
            if key in self.handles:
                return self.handles[key]
            
            # 创建新句柄
            try:
                handle = await asyncio.get_event_loop().run_in_executor(
                    None, self.sdk._start_preview, channel
                )
                if handle >= 0:
                    self.handles[key] = handle
                    logger.info(f"创建预览句柄: channel={channel}, handle={handle}")
                    return handle
            except Exception as e:
                logger.error(f"创建预览句柄失败: {e}")
                
        return None
    
    async def release_preview(self, channel: int):
        """释放预览句柄"""
        key = f"preview_{channel}"
        
        async with self._lock:
            if key in self.handles:
                handle = self.handles.pop(key)
                try:
                    await asyncio.get_event_loop().run_in_executor(
                        None, self.sdk.sdk.NET_DVR_StopRealPlay, handle
                    )
                    logger.info(f"释放预览句柄: channel={channel}")
                except Exception as e:
                    logger.error(f"释放预览句柄失败: {e}")
    
    async def cleanup_all(self):
        """清理所有资源"""
        async with self._lock:
            # 清理所有预览句柄
            for key in list(self.handles.keys()):
                if key.startswith("preview_"):
                    channel = int(key.split("_")[1])
                    await self.release_preview(channel)
            
            logger.info("资源管理器清理完成")