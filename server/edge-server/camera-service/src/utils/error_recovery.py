"""
错误恢复机制
SDK自动重连和操作重试
"""

import asyncio
import logging
from typing import Callable, Any, Optional
from functools import wraps

logger = logging.getLogger(__name__)


class ErrorRecovery:
    """错误恢复管理器"""
    
    def __init__(self, sdk_instance, max_retries: int = 3):
        self.sdk = sdk_instance
        self.max_retries = max_retries
        self.reconnect_interval = 30  # 重连间隔（秒）
        self._reconnect_task = None
        
    async def start_monitoring(self):
        """启动连接监控"""
        self._reconnect_task = asyncio.create_task(self._monitor_connection())
        logger.info("错误恢复监控已启动")
        
    async def stop_monitoring(self):
        """停止连接监控"""
        if self._reconnect_task:
            self._reconnect_task.cancel()
            try:
                await self._reconnect_task
            except asyncio.CancelledError:
                pass
            logger.info("错误恢复监控已停止")
    
    async def _monitor_connection(self):
        """监控SDK连接状态"""
        while True:
            try:
                await asyncio.sleep(self.reconnect_interval)
                
                # 检查连接状态
                if not self._check_connection():
                    logger.warning("检测到连接断开，尝试重连...")
                    if await self._reconnect():
                        logger.info("重连成功")
                    else:
                        logger.error("重连失败，将在下次检查时重试")
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"连接监控异常: {e}")
    
    def _check_connection(self) -> bool:
        """检查SDK连接状态"""
        try:
            # 简单的连接检查 - 获取设备能力
            if self.sdk.user_id >= 0:
                ret = self.sdk.sdk.NET_DVR_GetDVRConfig(
                    self.sdk.user_id,
                    3000,  # NET_DVR_GET_DEVICECFG
                    1,
                    None,
                    0,
                    None,
                    0
                )
                return ret >= 0
        except:
            pass
        return False
    
    async def _reconnect(self) -> bool:
        """重新连接SDK"""
        try:
            # 先登出
            if self.sdk.user_id >= 0:
                self.sdk.sdk.NET_DVR_Logout(self.sdk.user_id)
                self.sdk.user_id = -1
            
            # 重新登录
            await asyncio.sleep(1)
            return self.sdk._login()
            
        except Exception as e:
            logger.error(f"重连异常: {e}")
            return False


def with_retry(max_retries: int = 3, delay: float = 1.0):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        logger.warning(f"{func.__name__} 失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                        await asyncio.sleep(delay * (attempt + 1))  # 递增延迟
                    
            logger.error(f"{func.__name__} 失败，已达最大重试次数")
            raise last_exception
            
        return wrapper
    return decorator