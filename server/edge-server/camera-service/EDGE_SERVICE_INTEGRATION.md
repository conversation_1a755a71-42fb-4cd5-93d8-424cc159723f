# Camera Service 边缘服务集成指南

**服务地址**: `http://localhost:7090`  
**文档路径**: `/home/<USER>/server/edge-server/camera-service/EDGE_SERVICE_INTEGRATION.md`  
**更新时间**: 2025-07-23  
**版本**: v2.18

## 🎯 快速集成

### 服务检查
```python
import requests

# 检查Camera Service是否可用
def check_camera_service():
    try:
        response = requests.get('http://localhost:7090/api/v2/status', timeout=5)
        return response.status_code == 200 and response.json().get('success', False)
    except:
        return False
```

### 通用请求封装
```python
import requests
import json
from typing import Dict, Any, Optional

class CameraServiceClient:
    def __init__(self, base_url="http://localhost:7090"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({'Content-Type': 'application/json'})
    
    def _request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict[str, Any]:
        """统一请求方法"""
        url = f"{self.base_url}/api/v2{endpoint}"
        try:
            if method.upper() == 'GET':
                response = self.session.get(url, timeout=10)
            elif method.upper() == 'POST':
                response = self.session.post(url, json=data, timeout=30)
            elif method.upper() == 'DELETE':
                response = self.session.delete(url, timeout=10)
            
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"success": False, "error": f"请求失败: {str(e)}"}
```

## 📋 常用功能调用

### 1. 抓拍功能
```python
# 高质量抓拍
def capture_snapshot(channel: int = 1, quality: int = 0):
    """
    抓拍照片
    
    Args:
        channel: 通道号 1-4
        quality: 图片质量 0-最佳, 1-较好, 2-一般
    
    Returns:
        {"success": bool, "data": {"filename": str, "path": str}}
    """
    client = CameraServiceClient()
    return client._request('POST', f'/cameras/{channel}/snapshot', 
                          {'quality': quality})

# 示例调用
result = capture_snapshot(channel=1, quality=0)
if result['success']:
    filename = result['data']['filename']
    print(f"抓拍成功: {filename}")
```

### 2. 录像控制
```python
# 开始录像
def start_recording(channel: int, duration: Optional[int] = None):
    """
    开始录像
    
    Args:
        channel: 通道号 1-4
        duration: 录像时长(秒), None为手动停止
    
    Returns:
        {"success": bool, "data": {"filename": str, "channel": int}}
    """
    client = CameraServiceClient()
    data = {}
    if duration:
        data['duration'] = duration
    
    return client._request('POST', f'/cameras/{channel}/recording', data)

# 停止录像
def stop_recording(channel: int):
    """停止录像"""
    client = CameraServiceClient()
    return client._request('DELETE', f'/cameras/{channel}/recording')

# 示例调用
# 录像60秒
result = start_recording(channel=1, duration=60)

# 手动录像（需要手动停止）
start_result = start_recording(channel=1)
# ... 执行其他任务
stop_result = stop_recording(channel=1)
```

### 3. PTZ控制
```python
# PTZ移动控制
def control_ptz_move(channel: int, direction: str, speed: int = 3, duration: float = 1.0):
    """
    PTZ移动控制
    
    Args:
        channel: 通道号 (仅1、3支持PTZ)
        direction: 方向 'up'/'down'/'left'/'right'
        speed: 速度 1-7
        duration: 持续时间(秒)
    
    Returns:
        {"success": bool, "data": dict}
    """
    client = CameraServiceClient()
    return client._request('POST', f'/cameras/{channel}/ptz', {
        'action': 'move',
        'params': {
            'direction': direction,
            'speed': speed,
            'duration': duration
        }
    })

# PTZ缩放控制
def control_ptz_zoom(channel: int, direction: str, speed: int = 3):
    """PTZ缩放控制"""
    client = CameraServiceClient()
    return client._request('POST', f'/cameras/{channel}/ptz', {
        'action': 'zoom',
        'params': {
            'direction': direction,  # 'in' 或 'out'
            'speed': speed
        }
    })

# 示例调用
# 向上移动2秒
control_ptz_move(channel=1, direction='up', speed=5, duration=2.0)

# 放大
control_ptz_zoom(channel=1, direction='in', speed=4)
```

## 🌟 高级业务功能

### 1. 预置点管理
```python
class PresetManager:
    def __init__(self):
        self.client = CameraServiceClient()
    
    def set_preset(self, channel: int, preset_id: int):
        """设置预置点"""
        return self.client._request('POST', f'/cameras/{channel}/preset/{preset_id}/set')
    
    def call_preset(self, channel: int, preset_id: int):
        """调用预置点"""
        return self.client._request('POST', f'/cameras/{channel}/preset/{preset_id}/call')
    
    def delete_preset(self, channel: int, preset_id: int):
        """删除预置点"""
        return self.client._request('DELETE', f'/cameras/{channel}/preset/{preset_id}')

# 示例：设置监控预置点
preset_mgr = PresetManager()

# 手动调整摄像头到理想位置后设置预置点
preset_mgr.set_preset(channel=1, preset_id=1)  # 设置预置点1
preset_mgr.set_preset(channel=1, preset_id=2)  # 设置预置点2

# 快速切换到预置点
preset_mgr.call_preset(channel=1, preset_id=1)  # 切换到预置点1
```

### 2. 定点录像 (配合其他服务使用)
```python
def preset_point_recording(channel: int, preset_id: int, duration: int = 300):
    """
    定点录像 - 适用于固定点位监控
    
    Args:
        channel: 通道号
        preset_id: 预置点ID
        duration: 录像时长(秒)
    
    Returns:
        {"success": bool, "data": {"filename": str, "preset_id": int}}
    """
    client = CameraServiceClient()
    return client._request('POST', f'/cameras/{channel}/recording/preset', {
        'preset_id': preset_id,
        'duration': duration
    })

# 使用场景示例
def security_monitoring_task():
    """安防监控任务"""
    # 1. 切换到监控点1录像5分钟
    result1 = preset_point_recording(channel=1, preset_id=1, duration=300)
    
    # 2. 切换到监控点2录像5分钟  
    result2 = preset_point_recording(channel=1, preset_id=2, duration=300)
    
    return [result1, result2]
```

### 3. 地形扫描录像 (用于3D重建)
```python
def terrain_scanning_recording(channel: int, duration: int = 1800, 
                             tilt_step: int = 15, pan_speed: int = 2):
    """
    地形扫描录像 - 用于3D地形重建
    
    Args:
        channel: 通道号
        duration: 总录像时长(秒)
        tilt_step: 俯仰角步进(度) - 推荐15-30度
        pan_speed: 水平扫描速度 1-7
    
    Returns:
        {"success": bool, "data": {"filename": str, "scan_params": dict}}
    """
    client = CameraServiceClient()
    return client._request('POST', f'/cameras/{channel}/recording/terrain', {
        'duration': duration,
        'tilt_step': tilt_step,
        'pan_speed': pan_speed
    })

# 使用场景示例
def mapping_survey_task():
    """测绘调查任务"""
    # 执行地形扫描，30分钟完整扫描
    result = terrain_scanning_recording(
        channel=3,           # 使用支持PTZ的通道3
        duration=1800,       # 30分钟扫描
        tilt_step=20,        # 20度俯仰步进
        pan_speed=3          # 中等扫描速度
    )
    
    if result['success']:
        filename = result['data']['filename']
        scan_params = result['data']['scan_params']
        print(f"地形扫描完成: {filename}")
        print(f"扫描参数: {scan_params}")
        return filename
    else:
        print(f"扫描失败: {result.get('error')}")
        return None
```

## 🔗 与其他边缘服务协同

### 1. 与激光雷达服务协同
```python
def lidar_camera_coordination():
    """激光雷达与摄像头协同工作"""
    
    # 1. 设置摄像头到激光雷达扫描位置
    preset_mgr = PresetManager()
    preset_mgr.call_preset(channel=1, preset_id=3)  # 激光雷达配合位置
    
    # 2. 开始定点录像（与激光雷达扫描同步）
    camera_result = preset_point_recording(
        channel=1, 
        preset_id=3, 
        duration=600  # 10分钟激光雷达扫描时间
    )
    
    # 3. 调用激光雷达服务开始扫描
    # lidar_service.start_scanning(duration=600)
    
    return camera_result
```

### 2. 与AI分析服务协同
```python
def get_realtime_stream_for_ai():
    """获取实时流供AI分析使用"""
    client = CameraServiceClient()
    
    # 获取实时流地址
    stream_result = client._request('GET', '/cameras/1/stream')
    
    if stream_result['success']:
        rtsp_url = stream_result['data']['rtsp_url']
        http_url = stream_result['data']['http_url']
        
        # 提供给AI分析服务
        ai_analysis_config = {
            'stream_source': rtsp_url,
            'backup_source': http_url,
            'analysis_duration': 3600  # 1小时分析
        }
        
        return ai_analysis_config
    
    return None
```

### 3. 与存储服务协同
```python
def recording_with_backup():
    """录像并备份到存储服务"""
    
    # 1. 开始录像
    recording_result = start_recording(channel=1, duration=600)
    
    if recording_result['success']:
        filename = recording_result['data']['filename']
        
        # 2. 录像完成后调用存储服务备份
        # storage_service.backup_file(
        #     source_path=f"/tmp/camera_data/{filename}",
        #     destination="backup/camera_recordings/"
        # )
        
        return filename
    
    return None
```

## 📊 服务状态监控

### 1. 健康检查
```python
def health_check():
    """Camera Service健康检查"""
    client = CameraServiceClient()
    status = client._request('GET', '/status')
    
    if status.get('success'):
        data = status['data']
        return {
            'service_available': True,
            'nvr_connected': data.get('nvr_connected', False),
            'sdk_initialized': data.get('sdk_initialized', False),
            'active_recordings': len(data.get('recording_channels', [])),
            'data_directory': data.get('data_directory')
        }
    else:
        return {'service_available': False}
```

### 2. 录像状态查询
```python
def get_recording_status():
    """获取当前录像状态"""
    client = CameraServiceClient()
    status = client._request('GET', '/status')
    
    if status.get('success'):
        recording_channels = status['data'].get('recording_channels', [])
        return {
            'active_recordings': len(recording_channels),
            'channels': recording_channels
        }
    
    return {'active_recordings': 0, 'channels': []}
```

## ⚠️ 错误处理建议

### 1. 通用错误处理
```python
def safe_camera_operation(operation_func, *args, **kwargs):
    """安全的摄像头操作包装器"""
    try:
        # 先检查服务状态
        if not check_camera_service():
            return {"success": False, "error": "Camera Service不可用"}
        
        # 执行操作
        result = operation_func(*args, **kwargs)
        
        # 检查结果
        if not result.get('success', False):
            error_msg = result.get('error', '未知错误')
            print(f"摄像头操作失败: {error_msg}")
        
        return result
        
    except Exception as e:
        return {"success": False, "error": f"操作异常: {str(e)}"}

# 使用示例
result = safe_camera_operation(capture_snapshot, channel=1, quality=0)
```

### 2. 重试机制
```python
import time
from typing import Callable

def retry_camera_operation(operation_func: Callable, max_retries: int = 3, 
                          delay: float = 1.0, *args, **kwargs):
    """重试机制包装器"""
    for attempt in range(max_retries):
        try:
            result = operation_func(*args, **kwargs)
            if result.get('success', False):
                return result
            
            if attempt < max_retries - 1:
                print(f"操作失败，{delay}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                time.sleep(delay)
                
        except Exception as e:
            if attempt < max_retries - 1:
                print(f"操作异常，重试中... 错误: {str(e)}")
                time.sleep(delay)
            else:
                return {"success": False, "error": f"重试失败: {str(e)}"}
    
    return {"success": False, "error": f"达到最大重试次数 ({max_retries})"}
```

## 🚀 最佳实践

### 1. 资源管理
```python
# 好的做法：确保录像任务有明确的结束时间或条件
start_recording(channel=1, duration=300)  # 5分钟后自动停止

# 避免：无限期录像不手动停止
# start_recording(channel=1)  # 需要手动停止，容易忘记
```

### 2. 通道使用
```python
# PTZ功能只在通道1、3可用
ptz_channels = [1, 3]
recording_channels = [1, 2, 3, 4]  # 所有通道都支持录像

def is_ptz_supported(channel: int) -> bool:
    return channel in [1, 3]

# 使用前检查
if is_ptz_supported(channel):
    control_ptz_move(channel, 'up', speed=3)
```

### 3. 性能优化
```python
# 复用客户端连接
camera_client = CameraServiceClient()

# 批量操作
def batch_snapshots(channels: list):
    """批量抓拍"""
    results = {}
    for channel in channels:
        results[channel] = camera_client._request(
            'POST', f'/cameras/{channel}/snapshot', {'quality': 0}
        )
    return results
```

---

## 📞 集成支持

**服务端点**: `http://localhost:7090`  
**文档位置**: `/home/<USER>/server/edge-server/camera-service/EDGE_SERVICE_INTEGRATION.md`  
**API版本**: v2  
**服务状态**: `GET /api/v2/status`  

其他边缘服务如需集成Camera Service，请参考本文档示例代码，或调用服务状态接口获取实时信息。