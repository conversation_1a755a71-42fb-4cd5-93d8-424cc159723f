# Camera Service 连接池优化

## 概述

本次优化为Camera Service实现了完整的连接池管理机制，改进了SDK连接的生命周期管理，提高了服务的稳定性和可靠性。

## 主要改进

### 1. 连接池管理器 (`ConnectionPool`)

新增了专门的连接池管理器，提供以下功能：

- **单例连接管理**: 服务启动时建立一次连接，整个生命周期内复用
- **健康检查机制**: 定期检查连接状态，及时发现问题
- **自动重连功能**: 连接断开时自动尝试重连
- **连接统计信息**: 记录连接次数、失败次数、重连次数等

### 2. 代码结构改进

#### 之前的模式
```python
# 每个服务实例登录一次
self.sdk = HikvisionSDK(config)
self.sdk.initialize()
self.sdk.login(ip, port, username, password)

# 直接调用SDK方法
filepath = self.sdk.capture_jpeg(channel, quality)
```

#### 优化后的模式
```python
# 使用连接池管理
self.connection_pool = ConnectionPool(sdk_factory, nvr_config, pool_config)
await self.connection_pool.initialize()

# 通过连接池执行操作，带重试机制
filepath = await self.connection_pool.execute_with_retry(
    sdk.capture_jpeg, channel, quality
)
```

### 3. 配置参数

连接池支持以下配置参数：

```yaml
connection_pool:
  health_check_interval: 30    # 健康检查间隔（秒）
  reconnect_delay: 5          # 重连延迟（秒）
  max_reconnect_attempts: 3   # 最大重连次数
```

### 4. 健康检查机制

- 每30秒执行一次健康检查
- 检查SDK连接状态（user_id >= 0）
- 发现连接断开时自动触发重连

### 5. 重试机制

`execute_with_retry`方法提供：
- 最多2次重试机会
- 检测到连接相关错误时自动重连
- 确保操作的可靠性

## 向后兼容性

- 所有API接口保持不变
- 配置文件格式兼容，新增参数有默认值
- 不影响现有功能的使用

## 性能优势

1. **减少连接开销**: 避免重复登录/登出
2. **提高响应速度**: 复用已建立的连接
3. **增强稳定性**: 自动处理连接异常
4. **便于监控**: 提供详细的连接统计信息

## 监控指标

通过`/api/v2/status`接口可以查看连接池状态：

```json
{
  "connection_pool": {
    "is_connected": true,
    "connection_time": "2025-01-24T10:00:00",
    "last_health_check": "2025-01-24T10:05:30",
    "uptime": "0:05:30",
    "total_connections": 1,
    "failed_connections": 0,
    "reconnections": 0,
    "health_checks": 10
  }
}
```

## 测试方法

运行测试脚本验证连接池功能：

```bash
cd /home/<USER>/server/edge-server/camera-service
python test_connection_pool.py
```

## 注意事项

1. 海康SDK通常只支持单个连接，因此`max_connections`设置为1
2. 健康检查间隔不宜太短，避免影响性能
3. 重连次数应根据网络环境适当调整