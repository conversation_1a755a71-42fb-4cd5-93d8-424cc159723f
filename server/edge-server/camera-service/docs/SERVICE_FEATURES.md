# Camera Service 功能清单

## 服务概览

Camera Service v2.18 是一个独立的微服务，专门负责海康威视摄像头和NVR的控制与管理。

## 支持的功能

### 📸 基础功能

#### 1. 抓拍照片
- **接口**: `POST /api/v2/cameras/{channel}/snapshot`
- **支持通道**: 1-4（映射到NVR通道33-36）
- **图片质量**: 0=最佳, 1=较好, 2=一般
- **输出格式**: JPEG
- **文件存储**: `/tmp/camera_data/`

#### 2. 实时录像
- **开始录像**: `POST /api/v2/cameras/{channel}/recording`
- **停止录像**: `DELETE /api/v2/cameras/{channel}/recording`
- **查询状态**: `GET /api/v2/cameras/{channel}/recording`
- **支持定时**: 可指定录像时长
- **输出格式**: MP4 (H.264编码)

#### 3. 流媒体获取
- **接口**: `GET /api/v2/cameras/{channel}/stream`
- **返回内容**: 
  - RTSP流地址
  - HTTP预览地址
- **支持所有通道**: 1-4

#### 4. 历史回放
- **接口**: `POST /api/v2/cameras/{channel}/playback`
- **功能**: 下载指定时间段的历史录像
- **输出格式**: MP4
- **下载速度**: 高速下载（2分钟视频约2秒完成）

### 🎯 PTZ控制功能（仅通道1、3）

#### 1. 方向控制
- **接口**: `POST /api/v2/cameras/{channel}/ptz`
- **动作**: move
- **方向**: up/down/left/right
- **速度**: 1-7
- **支持定时**: 可指定持续时间

#### 2. 缩放控制
- **接口**: `POST /api/v2/cameras/{channel}/ptz`
- **动作**: zoom
- **类型**: in/out
- **速度**: 1-7

#### 3. 预置点管理
- **设置预置点**: `POST /api/v2/cameras/{channel}/preset/{preset_id}/set`
- **调用预置点**: `POST /api/v2/cameras/{channel}/preset/{preset_id}/call`
- **删除预置点**: `DELETE /api/v2/cameras/{channel}/preset/{preset_id}`
- **预置点ID**: 1-255

### 🌟 高级业务功能

#### 1. 定点录像
- **接口**: `POST /api/v2/cameras/{channel}/recording/preset`
- **功能**: 自动移动到预置点后开始录像
- **参数**:
  - preset_id: 预置点ID
  - duration: 录像时长
- **应用场景**: 定点监控、特定位置记录

#### 2. 地形扫描录像
- **接口**: `POST /api/v2/cameras/{channel}/recording/terrain`
- **功能**: 360度全景扫描录像
- **参数**:
  - duration: 总录像时长
  - tilt_step: 俯仰角步进（默认15度）
  - pan_speed: 水平扫描速度（1-7）
- **应用场景**: 3D地形重建、全景监控

### 🔧 系统管理功能

#### 1. 健康检查
- **接口**: `GET /health`
- **返回**: 服务健康状态、版本信息

#### 2. 服务状态
- **接口**: `GET /api/v2/status`
- **返回**:
  - NVR连接状态
  - 通道映射信息
  - 录像状态
  - 连接池统计

#### 3. 文件管理
- **列出文件**: `GET /api/v2/files`
- **删除文件**: `DELETE /api/v2/files/{filename}`
- **文件类型**: 支持图片和视频文件

## 技术特性

### 架构优势
- **微服务架构**: 独立进程，故障隔离
- **异步处理**: 基于aiohttp，高并发支持
- **连接池管理**: 优化资源使用
- **模块化设计**: 代码结构清晰，易于维护

### 性能指标
- **API响应**: < 100ms
- **录像启动**: < 2秒
- **PTZ响应**: < 500ms
- **并发支持**: 10+客户端
- **编码效率**: H.264，约1GB/小时

### 通道映射
| 用户通道 | NVR通道 | PTZ支持 |
|---------|---------|---------|
| 1       | 33      | ✅      |
| 2       | 34      | ❌      |
| 3       | 35      | ✅      |
| 4       | 36      | ❌      |

## 部署要求

### 系统要求
- Python 3.8+
- Linux操作系统
- 海康威视SDK v6.1.9.48

### 网络要求
- 与NVR网络连通
- 端口7090可用

### 依赖包
- aiohttp
- aiohttp-cors
- pyyaml

## 版本历史

- **v2.18** - 模块化重构，优化代码结构
- **v2.17** - 优化录像资源清理机制
- **v2.16** - 新增高级业务功能
- **v2.0** - 独立微服务架构