# Camera Service API 参考文档

## 目录
- [概述](#概述)
- [认证](#认证)
- [错误处理](#错误处理)
- [API端点](#api端点)
  - [健康检查](#健康检查)
  - [状态管理](#状态管理)
  - [视频捕获](#视频捕获)
  - [PTZ控制](#ptz控制)
  - [历史回放](#历史回放)
- [数据模型](#数据模型)
- [示例代码](#示例代码)

## 概述

### 基础信息
- **基础URL**: `http://localhost:7090`
- **协议**: HTTP/1.1
- **数据格式**: JSON
- **字符编码**: UTF-8
- **版本**: v2.18

### 请求头
```http
Content-Type: application/json
Accept: application/json
```

### 响应格式
所有响应遵循统一格式：

**成功响应**:
```json
{
  "success": true,
  "data": { ... }
}
```

**错误响应**:
```json
{
  "success": false,
  "error": "错误描述"
}
```

## 认证

当前版本不需要认证，服务仅在内网使用。

## 错误处理

### HTTP状态码

| 状态码 | 含义 | 说明 |
|--------|------|------|
| 200 | OK | 请求成功 |
| 400 | Bad Request | 请求参数错误 |
| 404 | Not Found | 端点不存在 |
| 500 | Internal Server Error | 服务器内部错误 |

### 错误响应示例
```json
{
  "success": false,
  "error": "通道1已在录像中"
}
```

## API端点

### 健康检查

#### GET /health

检查服务健康状态。

**请求**:
```http
GET /health HTTP/1.1
Host: localhost:8080
```

**响应**:
```json
{
  "status": "healthy",
  "service": "camera-service",
  "timestamp": "2025-01-23T10:30:00.123456"
}
```

**响应字段**:
| 字段 | 类型 | 说明 |
|------|------|------|
| status | string | 服务状态，固定为"healthy" |
| service | string | 服务名称 |
| timestamp | string | ISO格式时间戳 |

### 状态管理

#### GET /api/camera/status

获取相机服务详细状态。

**请求**:
```http
GET /api/camera/status HTTP/1.1
Host: localhost:8080
```

**响应**:
```json
{
  "success": true,
  "data": {
    "sdk_initialized": true,
    "nvr_connected": true,
    "nvr_config": {
      "ip": "*************",
      "port": 8000
    },
    "recording_channels": [1, 3],
    "data_directory": "/home/<USER>/server/edge-server/data/camera",
    "device_name": "NVR-Device",
    "timestamp": "2025-01-23T10:30:00.123456"
  }
}
```

**响应字段**:
| 字段 | 类型 | 说明 |
|------|------|------|
| sdk_initialized | boolean | SDK是否已初始化 |
| nvr_connected | boolean | 是否已连接NVR |
| nvr_config | object | NVR配置信息 |
| recording_channels | array | 正在录制的通道列表 |
| data_directory | string | 数据存储目录 |
| device_name | string | 设备名称（可选） |

### 视频捕获

#### POST /api/camera/snapshot

抓拍指定通道的照片。

**请求**:
```http
POST /api/camera/snapshot HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "channel": 1
}
```

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| channel | integer | 是 | 通道号（1-4） |

**响应**:
```json
{
  "success": true,
  "data": {
    "channel": 1,
    "nvr_channel": 33,
    "filename": "snapshot_1_20250123_103000.jpg",
    "filepath": "/tmp/camera_data/snapshot_1_20250123_103000.jpg",
    "size": 245760,
    "timestamp": "20250123_103000"
  }
}
```

**响应字段**:
| 字段 | 类型 | 说明 |
|------|------|------|
| channel | integer | 请求的通道号 |
| nvr_channel | integer | 映射后的NVR通道号 |
| filename | string | 文件名 |
| filepath | string | 完整文件路径 |
| size | integer | 文件大小（字节） |
| timestamp | string | 抓拍时间戳 |

#### POST /api/camera/record/start

开始录制视频。

**请求**:
```http
POST /api/camera/record/start HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "channel": 1,
  "duration": 60
}
```

**请求参数**:
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| channel | integer | 是 | - | 通道号（1-4） |
| duration | integer | 否 | 60 | 录制时长（秒） |

**响应**:
```json
{
  "success": true,
  "data": {
    "channel": 1,
    "nvr_channel": 33,
    "filename": "record_1_20250123_103000.mp4",
    "filepath": "/tmp/camera_data/record_1_20250123_103000.mp4",
    "start_time": "2025-01-23T10:25:00",
    "end_time": "2025-01-23T10:30:00",
    "duration": 60,
    "method": "playback_recording",
    "estimated_duration_seconds": 300,
    "status": "recording"
  }
}
```

#### POST /api/camera/record/stop

停止录制视频。

**请求**:
```http
POST /api/camera/record/stop HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "channel": 1
}
```

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| channel | integer | 是 | 通道号（1-4） |

**响应**:
```json
{
  "success": true,
  "data": {
    "channel": 1,
    "status": "stopped",
    "stop_time": "20250123_103100"
  }
}
```

### PTZ控制

#### POST /api/camera/ptz/control

控制PTZ摄像头移动。

**请求**:
```http
POST /api/camera/ptz/control HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "channel": 1,
  "action": "up",
  "speed": 50,
  "duration": 2.0
}
```

**请求参数**:
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| channel | integer | 是 | - | 通道号（1或3为PTZ） |
| action | string | 是 | - | 动作：up/down/left/right/stop |
| speed | integer | 否 | 50 | 速度（0-100） |
| duration | float | 否 | null | 持续时间（秒） |

**响应**:
```json
{
  "success": true,
  "data": {
    "channel": 1,
    "nvr_channel": 33,
    "action": "up",
    "speed": 5,
    "duration": 2.0
  }
}
```

#### POST /api/camera/ptz/zoom

控制PTZ摄像头变焦。

**请求**:
```http
POST /api/camera/ptz/zoom HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "channel": 1,
  "zoom": "in",
  "level": 2
}
```

**请求参数**:
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| channel | integer | 是 | - | 通道号（1或3为PTZ） |
| zoom | string | 是 | - | 变焦方向：in/out/stop |
| level | integer | 否 | 1 | 变焦级数 |

**响应**:
```json
{
  "success": true,
  "data": {
    "channel": 1,
    "nvr_channel": 33,
    "zoom": "in",
    "level": 2
  }
}
```

### 历史回放

#### POST /api/camera/playback

下载指定时间段的历史录像。

**请求**:
```http
POST /api/camera/playback HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "channel": 1,
  "start_time": "2025-01-23T09:00:00",
  "end_time": "2025-01-23T10:00:00"
}
```

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| channel | integer | 是 | 通道号（1-4） |
| start_time | string | 是 | 开始时间（ISO格式） |
| end_time | string | 是 | 结束时间（ISO格式） |

**响应**:
```json
{
  "success": true,
  "data": {
    "channel": 1,
    "nvr_channel": 33,
    "method": "playback",
    "filename": "playback_1_20250123_103000.mp4",
    "filepath": "/home/<USER>/server/edge-server/data/camera/playback/playback_1_20250123_103000.mp4",
    "start_time": "2025-01-23T09:00:00",
    "end_time": "2025-01-23T10:00:00",
    "duration_seconds": 3600,
    "play_handle": 12345,
    "status": "downloading"
  }
}
```

### 业务功能API

#### POST /api/camera/recording/preset

定点录像 - 移动到预置点后自动录像（仅支持PTZ通道1和3）。

**请求**:
```http
POST /api/camera/recording/preset HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "channel": 1,
  "preset_id": 1,
  "duration": 300
}
```

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| channel | integer | 是 | PTZ通道号（1或3） |
| preset_id | integer | 是 | 预置点ID（1-255） |
| duration | integer | 是 | 录像时长（秒） |

**响应**:
```json
{
  "success": true,
  "data": {
    "channel": 1,
    "nvr_channel": 33,
    "preset_id": 1,
    "filename": "record_ch33_20250123_103000.mp4",
    "filepath": "/home/<USER>/server/edge-server/data/camera/recordings/record_ch33_20250123_103000.mp4",
    "duration": 300,
    "status": "recording_at_preset"
  }
}
```

#### POST /api/camera/recording/terrain

地形扫描录像 - 360度全景扫描用于3D重建（仅支持PTZ通道1和3）。

**请求**:
```http
POST /api/camera/recording/terrain HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "channel": 1,
  "duration": 1800,
  "tilt_step": 15,
  "pan_speed": 2
}
```

**请求参数**:
| 参数 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| channel | integer | 是 | - | PTZ通道号（1或3） |
| duration | integer | 是 | - | 总录像时长（秒） |
| tilt_step | integer | 否 | 15 | 俯仰角步进（度） |
| pan_speed | integer | 否 | 2 | 水平扫描速度（1-7） |

**响应**:
```json
{
  "success": true,
  "data": {
    "channel": 1,
    "nvr_channel": 33,
    "scan_params": {
      "duration": 1800,
      "tilt_step": 15,
      "pan_speed": 2
    },
    "filename": "record_ch33_20250123_103000.mp4",
    "filepath": "/home/<USER>/server/edge-server/data/camera/recordings/record_ch33_20250123_103000.mp4",
    "status": "terrain_scanning"
  }
}
```

#### POST /api/camera/preset/set

设置预置点位置。

**请求**:
```http
POST /api/camera/preset/set HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "channel": 1,
  "preset_id": 1
}
```

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| channel | integer | 是 | PTZ通道号（1或3） |
| preset_id | integer | 是 | 预置点ID（1-255） |

**响应**:
```json
{
  "success": true,
  "data": {
    "channel": 1,
    "preset_id": 1,
    "message": "预置点设置成功"
  }
}
```

#### POST /api/camera/preset/call

调用预置点位置。

**请求**:
```http
POST /api/camera/preset/call HTTP/1.1
Host: localhost:8080
Content-Type: application/json

{
  "channel": 1,
  "preset_id": 1
}
```

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| channel | integer | 是 | PTZ通道号（1或3） |
| preset_id | integer | 是 | 预置点ID（1-255） |

**响应**:
```json
{
  "success": true,
  "data": {
    "channel": 1,
    "preset_id": 1,
    "message": "已移动到预置点"
  }
}
```

#### DELETE /api/camera/preset/{preset_id}

删除预置点。

**请求**:
```http
DELETE /api/camera/preset/1?channel=1 HTTP/1.1
Host: localhost:8080
```

**请求参数**:
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| channel | integer | 是 | PTZ通道号（1或3）- 查询参数 |
| preset_id | integer | 是 | 预置点ID（1-255）- 路径参数 |

**响应**:
```json
{
  "success": true,
  "data": {
    "channel": 1,
    "preset_id": 1,
    "message": "预置点删除成功"
  }
}
```

## 数据模型

### Channel（通道）

| 字段 | 类型 | 说明 |
|------|------|------|
| id | integer | 通道ID（1-4） |
| nvr_channel | integer | NVR实际通道（33-36） |
| type | string | 摄像头类型：ptz/fixed |
| status | string | 状态：idle/recording/error |

### RecordingInfo（录制信息）

| 字段 | 类型 | 说明 |
|------|------|------|
| channel | integer | 通道号 |
| filename | string | 文件名 |
| filepath | string | 文件路径 |
| start_time | string | 开始时间 |
| end_time | string | 结束时间 |
| duration | integer | 时长（秒） |
| size | integer | 文件大小（字节） |
| status | string | 状态：recording/completed/error |

### PTZPosition（云台位置）

| 字段 | 类型 | 说明 |
|------|------|------|
| pan | float | 水平角度（-180到180） |
| tilt | float | 垂直角度（-90到90） |
| zoom | float | 缩放级别（0到1） |

## 示例代码

### Python客户端

```python
import requests
import json
from datetime import datetime, timedelta

class CameraServiceClient:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def check_health(self):
        """健康检查"""
        response = self.session.get(f"{self.base_url}/health")
        return response.json()
    
    def get_status(self):
        """获取状态"""
        response = self.session.get(f"{self.base_url}/api/camera/status")
        return response.json()
    
    def snapshot(self, channel):
        """抓拍照片"""
        response = self.session.post(
            f"{self.base_url}/api/camera/snapshot",
            json={"channel": channel}
        )
        return response.json()
    
    def start_recording(self, channel, duration=60):
        """开始录制"""
        response = self.session.post(
            f"{self.base_url}/api/camera/record/start",
            json={"channel": channel, "duration": duration}
        )
        return response.json()
    
    def stop_recording(self, channel):
        """停止录制"""
        response = self.session.post(
            f"{self.base_url}/api/camera/record/stop",
            json={"channel": channel}
        )
        return response.json()
    
    def ptz_control(self, channel, action, speed=50, duration=None):
        """PTZ控制"""
        data = {
            "channel": channel,
            "action": action,
            "speed": speed
        }
        if duration:
            data["duration"] = duration
            
        response = self.session.post(
            f"{self.base_url}/api/camera/ptz/control",
            json=data
        )
        return response.json()
    
    def ptz_zoom(self, channel, zoom, level=1):
        """PTZ变焦"""
        response = self.session.post(
            f"{self.base_url}/api/camera/ptz/zoom",
            json={
                "channel": channel,
                "zoom": zoom,
                "level": level
            }
        )
        return response.json()
    
    def playback(self, channel, start_time, end_time):
        """历史回放"""
        response = self.session.post(
            f"{self.base_url}/api/camera/playback",
            json={
                "channel": channel,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat()
            }
        )
        return response.json()

# 使用示例
if __name__ == "__main__":
    client = CameraServiceClient()
    
    # 检查健康状态
    health = client.check_health()
    print(f"服务状态: {health['status']}")
    
    # 抓拍
    result = client.snapshot(1)
    if result['success']:
        print(f"抓拍成功: {result['data']['filepath']}")
    
    # PTZ控制
    client.ptz_control(1, "up", speed=70, duration=2.0)
    
    # 录制视频
    record_result = client.start_recording(1, duration=30)
    if record_result['success']:
        print(f"开始录制: {record_result['data']['filename']}")
```

### JavaScript/Node.js客户端

```javascript
const axios = require('axios');

class CameraServiceClient {
    constructor(baseUrl = 'http://localhost:8080') {
        this.baseUrl = baseUrl;
        this.client = axios.create({
            baseURL: baseUrl,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json'
            }
        });
    }
    
    async checkHealth() {
        const response = await this.client.get('/health');
        return response.data;
    }
    
    async getStatus() {
        const response = await this.client.get('/api/camera/status');
        return response.data;
    }
    
    async snapshot(channel) {
        const response = await this.client.post('/api/camera/snapshot', {
            channel: channel
        });
        return response.data;
    }
    
    async startRecording(channel, duration = 60) {
        const response = await this.client.post('/api/camera/record/start', {
            channel: channel,
            duration: duration
        });
        return response.data;
    }
    
    async stopRecording(channel) {
        const response = await this.client.post('/api/camera/record/stop', {
            channel: channel
        });
        return response.data;
    }
    
    async ptzControl(channel, action, speed = 50, duration = null) {
        const data = {
            channel: channel,
            action: action,
            speed: speed
        };
        if (duration !== null) {
            data.duration = duration;
        }
        
        const response = await this.client.post('/api/camera/ptz/control', data);
        return response.data;
    }
    
    async ptzZoom(channel, zoom, level = 1) {
        const response = await this.client.post('/api/camera/ptz/zoom', {
            channel: channel,
            zoom: zoom,
            level: level
        });
        return response.data;
    }
    
    async playback(channel, startTime, endTime) {
        const response = await this.client.post('/api/camera/playback', {
            channel: channel,
            start_time: startTime,
            end_time: endTime
        });
        return response.data;
    }
}

// 使用示例
async function main() {
    const client = new CameraServiceClient();
    
    try {
        // 健康检查
        const health = await client.checkHealth();
        console.log(`服务状态: ${health.status}`);
        
        // 抓拍
        const snapshot = await client.snapshot(1);
        if (snapshot.success) {
            console.log(`抓拍成功: ${snapshot.data.filepath}`);
        }
        
        // PTZ控制
        await client.ptzControl(1, 'up', 70, 2.0);
        
        // 开始录制
        const recording = await client.startRecording(1, 30);
        if (recording.success) {
            console.log(`开始录制: ${recording.data.filename}`);
        }
        
    } catch (error) {
        console.error('错误:', error.message);
    }
}

main();
```

### cURL示例

```bash
# 健康检查
curl -X GET http://localhost:8080/health

# 获取状态
curl -X GET http://localhost:8080/api/camera/status | jq

# 抓拍照片
curl -X POST http://localhost:8080/api/camera/snapshot \
  -H "Content-Type: application/json" \
  -d '{"channel": 1}' | jq

# 开始录制
curl -X POST http://localhost:8080/api/camera/record/start \
  -H "Content-Type: application/json" \
  -d '{"channel": 1, "duration": 60}' | jq

# PTZ控制
curl -X POST http://localhost:8080/api/camera/ptz/control \
  -H "Content-Type: application/json" \
  -d '{"channel": 1, "action": "up", "speed": 50, "duration": 2.0}' | jq

# 历史回放
curl -X POST http://localhost:8080/api/camera/playback \
  -H "Content-Type: application/json" \
  -d '{
    "channel": 1,
    "start_time": "2025-01-23T09:00:00",
    "end_time": "2025-01-23T10:00:00"
  }' | jq
```

## 错误处理最佳实践

### 重试机制

```python
import time
from functools import wraps

def retry(max_attempts=3, delay=1.0, backoff=2.0):
    """重试装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            attempt = 0
            current_delay = delay
            
            while attempt < max_attempts:
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    attempt += 1
                    if attempt >= max_attempts:
                        raise
                    
                    print(f"尝试 {attempt} 失败: {e}")
                    time.sleep(current_delay)
                    current_delay *= backoff
                    
            return None
        return wrapper
    return decorator

# 使用示例
@retry(max_attempts=3, delay=1.0)
def safe_snapshot(client, channel):
    result = client.snapshot(channel)
    if not result['success']:
        raise Exception(result.get('error', 'Unknown error'))
    return result
```

### 超时处理

```python
import asyncio
import aiohttp

async def snapshot_with_timeout(channel, timeout=10):
    """带超时的抓拍"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'http://localhost:8080/api/camera/snapshot',
                json={'channel': channel},
                timeout=aiohttp.ClientTimeout(total=timeout)
            ) as response:
                return await response.json()
    except asyncio.TimeoutError:
        return {'success': False, 'error': '请求超时'}
```

## 性能优化建议

1. **使用连接池**: 复用HTTP连接以减少开销
2. **批量操作**: 尽可能合并多个请求
3. **异步调用**: 使用异步库提高并发性能
4. **缓存结果**: 对状态查询等结果进行适当缓存
5. **限流保护**: 避免过于频繁的请求

## 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| 1.0.0 | 2025-01-15 | 初始版本 |
| 1.1.0 | 2025-01-20 | 添加历史回放功能 |
| 1.2.0 | 2025-01-23 | 优化录制方式，使用历史回放实现 |