# Camera Service 架构文档

## 目录
1. [服务概述](#服务概述)
2. [架构设计](#架构设计)
3. [文件结构](#文件结构)
4. [核心组件](#核心组件)
5. [数据流程](#数据流程)
6. [API接口](#api接口)
7. [部署架构](#部署架构)
8. [集成指南](#集成指南)

## 服务概述

Camera Service是一个独立的微服务，专门负责海康威视摄像头和NVR的控制与管理。通过HTTP REST API提供服务，实现了与主系统的解耦。

### 设计理念
- **微服务架构**：独立进程，故障隔离
- **异步处理**：基于aiohttp的高性能实现
- **SDK封装**：将复杂的海康SDK封装为简单API
- **状态管理**：实时跟踪设备和录制状态

### 主要功能
- 视频抓拍与录制
- PTZ云台控制
- 历史视频回放
- 实时流媒体服务
- 设备状态监控

## 架构设计

### 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                     边缘服务器系统                            │
├─────────────────────────────────────────────────────────────┤
│                                                              │
│  ┌──────────────┐    HTTP API    ┌─────────────────────┐   │
│  │ Edge Server  │ ◄────────────► │  Camera Service     │   │
│  │   主服务      │                │   (Port 7090)       │   │
│  └──────────────┘                └──────────┬──────────┘   │
│                                              │              │
│                                              ▼              │
│                                   ┌─────────────────────┐   │
│                                   │   Hikvision SDK     │   │
│                                   │   (libhcnetsdk.so)  │   │
│                                   └──────────┬──────────┘   │
│                                              │              │
└──────────────────────────────────────────────┼──────────────┘
                                               │
                              Network          │
                                               ▼
                                   ┌─────────────────────┐
                                   │    海康威视 NVR     │
                                   │  *************:8000 │
                                   └─────────────────────┘
                                              │
                    ┌─────────┬───────┬───────┴───────┬─────────┐
                    ▼         ▼       ▼               ▼         ▼
                 CH 33     CH 34   CH 35           CH 36    ...
                (PTZ摄像头) (固定)  (PTZ摄像头)     (固定)
```

### 组件关系图

```
┌─────────────────────────────────────────────────────────────┐
│                    Camera Service                            │
├─────────────────────────────────────────────────────────────┤
│                                                              │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────────┐   │
│  │  REST API   │   │   Service   │   │   SDK Manager   │   │
│  │   Server    │──►│   Layer     │──►│   (HikvisionSDK)│   │
│  └─────────────┘   └─────────────┘   └─────────────────┘   │
│         ▲                 │                    │             │
│         │                 ▼                    ▼             │
│  ┌─────────────┐   ┌─────────────┐   ┌─────────────────┐   │
│  │   Routes    │   │   Camera    │   │   Native SDK    │   │
│  │  Handlers   │   │   Service   │   │   Functions     │   │
│  └─────────────┘   └─────────────┘   └─────────────────┘   │
│                                                              │
└─────────────────────────────────────────────────────────────┘
```

## 文件结构

### 完整目录结构（v2.18更新）

```
camera-service/
├── src/                          # 源代码目录
│   ├── main.py                  # 服务主入口
│   ├── api/                     # API层
│   │   ├── __init__.py
│   │   └── server.py            # REST API服务器
│   ├── services/                # 业务逻辑层
│   │   ├── __init__.py
│   │   ├── camera_service.py    # 主业务逻辑
│   │   ├── recording_service.py # 录像服务模块
│   │   └── ptz_service.py       # PTZ控制模块
│   └── sdk/                     # SDK封装层
│       ├── __init__.py
│       ├── hikvision.py         # 海康SDK核心封装
│       ├── structures.py        # SDK数据结构定义
│       ├── constants.py         # 常量和错误码定义
│       └── connection_pool.py   # 连接池管理
├── config/                       # 配置文件
│   └── config.yaml              # 服务配置
├── lib/                          # SDK库文件
│   ├── libhcnetsdk.so           # 主SDK库
│   ├── HCNetSDK_Log_Switch.xml # SDK日志配置
│   └── HCNetSDKCom/             # 依赖库目录
│       ├── libHCCore.so
│       ├── libHCAlarm.so
│       └── ... (12个依赖库)
├── tests/                        # 测试文件
│   ├── __init__.py
│   ├── test_connection_pool.py  # 连接池测试
│   └── test_optimized_service.py # 服务功能测试
├── scripts/                      # 脚本文件
│   ├── start.sh                 # 启动脚本
│   └── stop.sh                  # 停止脚本
├── docs/                         # 文档
│   ├── API_REFERENCE.md         # API参考文档
│   ├── ARCHITECTURE.md          # 架构文档（本文件）
│   ├── SERVICE_GUIDE.md         # 服务使用指南
│   ├── INTEGRATION_GUIDE.md     # 集成指南
│   ├── DEPLOYMENT_CONFIG_GUIDE.md # 部署配置指南
│   └── CONNECTION_POOL_OPTIMIZATION.md # 连接池优化说明
├── logs/                         # 日志文件（git忽略）
├── .gitignore                   # Git忽略配置
├── README.md                    # 服务说明
└── camera-service.service       # systemd服务配置
```

### 文件功能说明

| 文件/目录 | 功能说明 | 重要性 |
|----------|---------|--------|
| **src/main.py** | 服务主入口，负责启动HTTP服务器 | 核心 |
| **src/api/server.py** | REST API服务器实现，定义所有HTTP接口 | 核心 |
| **src/services/** | 业务逻辑层，包含所有业务处理逻辑 | 核心 |
| **src/services/camera_service.py** | 主业务逻辑，协调各个子服务 | 核心 |
| **src/services/recording_service.py** | 录像相关业务，包括定点录像、地形扫描 | 核心 |
| **src/services/ptz_service.py** | PTZ控制业务，包括云台和预置点管理 | 核心 |
| **src/sdk/** | SDK封装层，提供底层硬件抽象 | 核心 |
| **src/sdk/hikvision.py** | 海康SDK核心封装，与硬件交互 | 核心 |
| **src/sdk/structures.py** | SDK数据结构定义，如设备信息、时间等 | 核心 |
| **src/sdk/constants.py** | 常量定义，包括PTZ命令、错误码等 | 核心 |
| **src/sdk/connection_pool.py** | 连接池管理，优化SDK连接资源 | 核心 |
| **config/config.yaml** | 服务配置文件，包含NVR连接信息等 | 重要 |
| **lib/** | SDK库文件目录，包含海康威视动态库 | 重要 |
| **logs/** | 日志目录，记录服务运行状态 | 重要 |

## 核心组件

### 1. HikvisionSDK类

**位置**: `src/sdk/hikvision.py`

**功能**: 
- 管理SDK初始化和资源清理
- 处理NVR登录和连接
- 维护录制句柄和状态

**关键方法**:
```python
- initialize()      # SDK初始化
- _login()         # NVR登录
- cleanup()        # 资源清理
```

### 2. CameraService类

**位置**: `src/services/camera_service.py`

**功能**:
- 主业务逻辑协调
- 处理通道映射（1-4 → 33-36）
- 管理各个子服务

**核心接口**:

| 方法 | 功能 | 参数 |
|-----|------|-----|
| `capture_snapshot()` | 抓拍照片 | channel, quality |
| `start_recording()` | 开始录像 | channel, duration |
| `stop_recording()` | 停止录像 | channel |
| `control_ptz()` | PTZ控制 | channel, action, params |
| `preset_recording()` | 定点录像 | channel, preset_id, duration |
| `terrain_recording()` | 地形扫描录像 | channel, duration, tilt_step |

### 3. RecordingService类

**位置**: `src/services/recording_service.py`

**功能**:
- 管理所有录像相关业务
- 处理定点录像和地形扫描
- 维护录像任务状态

### 4. PTZService类

**位置**: `src/services/ptz_service.py`

**功能**:
- PTZ云台控制
- 预置点管理
- 自动停止控制

### 5. APIServer类

**位置**: `src/api_server.py:906-1109`

**功能**:
- 处理HTTP请求
- 管理服务生命周期
- 提供REST API接口

**API路由**:
```python
GET  /                     # 服务首页
GET  /health              # 健康检查
GET  /api/camera/status   # 状态查询
POST /api/camera/snapshot # 抓拍
POST /api/camera/record/start    # 开始录像
POST /api/camera/record/stop     # 停止录像
POST /api/camera/playback        # 历史回放
POST /api/camera/ptz/control     # PTZ控制
POST /api/camera/ptz/zoom        # 变焦控制
```

## 数据流程

### 1. 抓拍流程

```
客户端请求 → REST API → CameraService.take_snapshot()
    │                              │
    │                              ▼
    │                    SDK.NET_DVR_CaptureJPEGPicture()
    │                              │
    │                              ▼
    │                         保存到 data/snapshots/
    │                              │
    └──────────── 返回文件路径 ◄────┘
```

### 2. 录像流程

```
开始录像请求 → start_recording() → 历史回放方式录制
    │                                      │
    │                                      ▼
    │                            NET_DVR_PlayBackByTime()
    │                                      │
    │                                      ▼
    │                            NET_DVR_PlayBackSaveData()
    │                                      │
    │                                      ▼
    │                            NET_DVR_PlayBackControl()
    │                                      │
    └──────────── 返回录制信息 ◄──────────┘
```

### 3. PTZ控制流程

```
PTZ控制请求 → ptz_control() → 创建预览句柄
    │                              │
    │                              ▼
    │                    NET_DVR_PTZControl()
    │                              │
    │                              ▼
    │                       执行PTZ动作
    │                              │
    └──────────── 返回控制结果 ◄────┘
```

## API接口

### 接口规范

所有接口遵循RESTful设计原则：
- 使用JSON格式传输数据
- 成功响应包含`success: true`
- 错误响应包含`success: false`和`error`信息
- HTTP状态码遵循标准语义

### 接口列表

#### 1. 健康检查

```http
GET /health
```

**响应示例**:
```json
{
  "status": "healthy",
  "service": "camera-service",
  "timestamp": "2025-01-23T10:30:00"
}
```

#### 2. 状态查询

```http
GET /api/camera/status
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "sdk_initialized": true,
    "nvr_connected": true,
    "nvr_config": {
      "ip": "*************",
      "port": 8000
    },
    "recording_channels": [33],
    "data_directory": "/tmp/camera_data",
    "timestamp": "2025-01-23T10:30:00"
  }
}
```

#### 3. 抓拍照片

```http
POST /api/camera/snapshot
Content-Type: application/json

{
  "channel": 1
}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "channel": 1,
    "nvr_channel": 33,
    "filename": "snapshot_1_20250123_103000.jpg",
    "filepath": "/tmp/camera_data/snapshot_1_20250123_103000.jpg",
    "size": 245760,
    "timestamp": "20250123_103000"
  }
}
```

#### 4. 录像控制

**开始录像**:
```http
POST /api/camera/record/start
Content-Type: application/json

{
  "channel": 1,
  "duration": 60
}
```

**停止录像**:
```http
POST /api/camera/record/stop
Content-Type: application/json

{
  "channel": 1
}
```

#### 5. PTZ控制

```http
POST /api/camera/ptz/control
Content-Type: application/json

{
  "channel": 1,
  "action": "up",
  "speed": 50,
  "duration": 2.0
}
```

**动作参数**:
- `up`: 向上
- `down`: 向下
- `left`: 向左
- `right`: 向右
- `stop`: 停止

## 部署架构

### 系统部署图

```
┌─────────────────────────────────────────────────────────────┐
│                    生产环境部署                               │
├─────────────────────────────────────────────────────────────┤
│                                                              │
│  ┌─────────────────┐         ┌─────────────────────────┐   │
│  │  Systemd Unit   │         │   Environment Variables  │   │
│  │ camera-service  │         │   LD_LIBRARY_PATH       │   │
│  │   (自动重启)     │         │   LOG_LEVEL             │   │
│  └────────┬────────┘         └─────────────────────────┘   │
│           │                                                  │
│           ▼                                                  │
│  ┌─────────────────────────────────────────────────────┐   │
│  │            Camera Service Process                     │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌────────────┐  │   │
│  │  │   aiohttp   │  │   Camera    │  │  Hikvision │  │   │
│  │  │   Server    │  │   Service   │  │    SDK     │  │   │
│  │  │  Port 8080  │  │             │  │            │  │   │
│  │  └─────────────┘  └─────────────┘  └────────────┘  │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                              │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                  文件系统                              │   │
│  │  /tmp/camera_data/     - 临时数据存储                 │   │
│  │  /home/<USER>/.../logs/   - 日志文件                    │   │
│  │  /home/<USER>/.../config/ - 配置文件                    │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                              │
└─────────────────────────────────────────────────────────────┘
```

### 服务配置

**Systemd服务文件**: `/etc/systemd/system/camera-service.service`

```ini
[Unit]
Description=Camera Control Service
After=network.target

[Service]
Type=simple
User=app
WorkingDirectory=/home/<USER>/server/edge-server/camera-service
Environment="LD_LIBRARY_PATH=/home/<USER>/server/edge-server/camera-service/src/hikvision/lib"
ExecStart=/usr/bin/python3 /home/<USER>/server/edge-server/camera-service/src/main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

### 资源需求

| 资源 | 最小需求 | 推荐配置 |
|------|---------|---------|
| CPU | 1核心 | 2核心 |
| 内存 | 1GB | 2GB |
| 存储 | 10GB | 50GB |
| 网络 | 100Mbps | 1Gbps |

## 集成指南

### 1. 与Edge Server集成

Edge Server通过HTTP客户端调用Camera Service API：

```python
# edge_server/modules/camera_client.py
import aiohttp

class CameraClient:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url
        
    async def take_snapshot(self, channel):
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/api/camera/snapshot",
                json={"channel": channel}
            ) as response:
                return await response.json()
```

### 2. 与中央服务器集成

通过Edge Server的gRPC接口间接访问：

```
中央服务器 → gRPC → Edge Server → HTTP → Camera Service
```

### 3. 监控集成

使用Prometheus监控指标：

```yaml
# prometheus配置
scrape_configs:
  - job_name: 'camera-service'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
```

### 4. 日志集成

配置rsyslog或filebeat收集日志：

```yaml
# filebeat配置
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /home/<USER>/server/edge-server/camera-service/logs/*.log
  fields:
    service: camera-service
```

## 故障处理

### 常见问题

1. **SDK加载失败**
   - 检查LD_LIBRARY_PATH
   - 验证库文件权限
   - 确认系统依赖

2. **NVR连接失败**
   - 验证网络连通性
   - 检查防火墙规则
   - 确认认证信息

3. **录像文件损坏**
   - 检查磁盘空间
   - 验证写入权限
   - 查看SDK错误日志

### 故障恢复

1. **服务重启**:
   ```bash
   sudo systemctl restart camera-service
   ```

2. **清理临时文件**:
   ```bash
   rm -rf /tmp/camera_data/*
   ```

3. **重置连接**:
   ```bash
   # 重启后服务会自动重新连接NVR
   ```

## 性能优化

### 1. 连接池优化

在高并发场景下调整连接池参数：

```python
# 修改 src/api_server.py
connector = aiohttp.TCPConnector(
    limit=200,           # 增加总连接数
    limit_per_host=50,   # 增加单主机连接数
    ttl_dns_cache=600    # 延长DNS缓存
)
```

### 2. 异步优化

使用异步I/O处理文件操作：

```python
import aiofiles

async def save_file_async(filepath, data):
    async with aiofiles.open(filepath, 'wb') as f:
        await f.write(data)
```

### 3. 内存优化

- 定期清理未使用的句柄
- 限制并发录制数量
- 使用流式处理大文件

## 安全考虑

### 1. 网络安全

- 服务仅监听内网地址
- 使用防火墙限制访问
- 考虑添加API认证

### 2. 数据安全

- 定期清理临时文件
- 设置合适的文件权限
- 加密敏感配置信息

### 3. 运行安全

- 使用非特权用户运行
- 启用systemd安全特性
- 限制资源使用

## 版本历史

| 版本 | 日期 | 主要变更 |
|------|------|---------|
| v2.15 | 2025-01-20 | 修复文件结构和服务初始化问题 |
| v2.14 | 2025-01-20 | 实现实时录像功能修复 |
| v2.13 | 2025-01-19 | 修复硬件控制API兼容性 |
| v2.12 | 2025-01-18 | 完成v2.0微服务架构集成 |

## 总结

Camera Service作为边缘服务器的核心组件，通过微服务架构实现了：

1. **高可用性**: 独立进程，故障隔离
2. **易维护性**: 模块化设计，清晰的接口
3. **可扩展性**: 标准REST API，易于集成
4. **高性能**: 异步处理，资源优化

未来可以考虑的改进方向：
- 添加WebSocket支持实时视频流
- 实现分布式录制存储
- 增加AI视频分析功能
- 支持更多品牌的摄像头