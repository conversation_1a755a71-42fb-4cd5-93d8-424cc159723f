#!/usr/bin/env python3
"""
优化功能自动化测试脚本
测试资源管理、错误恢复和监控功能
"""

import asyncio
import aiohttp
import json
import time
from datetime import datetime
import sys

# 配置
BASE_URL = "http://localhost:7090"
TEST_CHANNELS = [1, 2, 3, 4]

class TestOptimization:
    """优化功能测试类"""
    
    def __init__(self):
        self.session = None
        self.results = {
            'passed': 0,
            'failed': 0,
            'errors': []
        }
        
    async def setup(self):
        """初始化"""
        self.session = aiohttp.ClientSession()
        print(f"\n{'='*60}")
        print(f"相机服务优化功能自动化测试")
        print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}\n")
        
    async def teardown(self):
        """清理"""
        if self.session:
            await self.session.close()
            
    async def test(self, name: str, func):
        """执行单个测试"""
        print(f"\n[测试] {name}")
        try:
            result = await func()
            if result:
                print(f"✅ 通过")
                self.results['passed'] += 1
            else:
                print(f"❌ 失败")
                self.results['failed'] += 1
        except Exception as e:
            print(f"❌ 异常: {e}")
            self.results['failed'] += 1
            self.results['errors'].append(f"{name}: {str(e)}")
            
    async def test_metrics_api(self):
        """测试指标API"""
        async with self.session.get(f"{BASE_URL}/api/v2/metrics") as resp:
            if resp.status != 200:
                return False
            
            data = await resp.json()
            if not data.get('success'):
                return False
                
            metrics = data.get('data', {})
            print(f"  - 运行时间: {metrics.get('uptime_hours', 0):.2f} 小时")
            print(f"  - 总操作数: {metrics.get('total_operations', {})}")
            print(f"  - 资源使用: {metrics.get('resources', {}).get('handles', 0)} 个句柄")
            
            return True
            
    async def test_resource_management(self):
        """测试资源管理"""
        # 连续抓拍测试资源复用
        print("  - 测试资源句柄复用...")
        
        for i in range(5):
            async with self.session.post(
                f"{BASE_URL}/api/v2/cameras/1/snapshot",
                json={}
            ) as resp:
                if resp.status != 200:
                    return False
                    
            # 短暂延迟
            await asyncio.sleep(0.5)
            
        # 检查资源状态
        async with self.session.get(f"{BASE_URL}/api/v2/metrics") as resp:
            data = await resp.json()
            handles = data.get('data', {}).get('resources', {}).get('handles', 0)
            print(f"  - 5次抓拍后句柄数: {handles} (应该复用同一个)")
            
            # 理想情况下应该只有1个句柄
            return handles <= 2
            
    async def test_error_tracking(self):
        """测试错误追踪"""
        # 故意触发错误
        print("  - 测试错误记录...")
        
        # 使用无效通道
        async with self.session.post(
            f"{BASE_URL}/api/v2/cameras/99/snapshot",
            json={}
        ) as resp:
            # 应该返回错误
            if resp.status == 200:
                return False
                
        # 检查错误是否被记录
        async with self.session.get(f"{BASE_URL}/api/v2/metrics") as resp:
            data = await resp.json()
            metrics = data.get('data', {})
            errors = metrics.get('recent_errors', [])
            error_count = metrics.get('total_operations', {}).get('errors', 0)
            
            print(f"  - 错误数量: {error_count}")
            if errors:
                print(f"  - 最近错误: {errors[-1]}")
                
            return error_count > 0
            
    async def test_performance_monitoring(self):
        """测试性能监控"""
        print("  - 测试性能指标收集...")
        
        # 执行多次操作
        for channel in [1, 2]:
            async with self.session.post(
                f"{BASE_URL}/api/v2/cameras/{channel}/snapshot",
                json={}
            ) as resp:
                await resp.read()
                
        # 获取性能指标
        async with self.session.get(f"{BASE_URL}/api/v2/metrics") as resp:
            data = await resp.json()
            performance = data.get('data', {}).get('performance', {})
            
            if 'snapshot' in performance:
                avg_ms = performance['snapshot']['avg_ms']
                max_ms = performance['snapshot']['max_ms']
                samples = performance['snapshot']['samples']
                
                print(f"  - 抓拍平均耗时: {avg_ms:.2f}ms")
                print(f"  - 抓拍最大耗时: {max_ms:.2f}ms")
                print(f"  - 样本数量: {samples}")
                
                # 平均耗时应该在合理范围内
                return avg_ms < 1000  # 小于1秒
                
        return False
        
    async def test_channel_statistics(self):
        """测试通道统计"""
        print("  - 测试通道使用统计...")
        
        # 不同通道执行操作
        for channel in TEST_CHANNELS:
            async with self.session.post(
                f"{BASE_URL}/api/v2/cameras/{channel}/snapshot",
                json={}
            ) as resp:
                await resp.read()
                
        # 获取通道统计
        async with self.session.get(f"{BASE_URL}/api/v2/metrics") as resp:
            data = await resp.json()
            channel_usage = data.get('data', {}).get('channel_usage', {})
            
            print(f"  - 通道使用情况:")
            for channel, stats in channel_usage.items():
                print(f"    通道{channel}: {stats}")
                
            # 应该有4个通道的统计数据
            return len(channel_usage) == len(TEST_CHANNELS)
            
    async def test_concurrent_operations(self):
        """测试并发操作"""
        print("  - 测试并发请求处理...")
        
        # 并发抓拍
        tasks = []
        for channel in TEST_CHANNELS:
            task = self.session.post(
                f"{BASE_URL}/api/v2/cameras/{channel}/snapshot",
                json={}
            )
            tasks.append(task)
            
        # 等待所有请求完成
        start_time = time.time()
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        elapsed = time.time() - start_time
        
        # 检查结果
        success_count = 0
        for resp in responses:
            if not isinstance(resp, Exception):
                async with resp as r:
                    if r.status == 200:
                        success_count += 1
                        
        print(f"  - 并发成功率: {success_count}/{len(TEST_CHANNELS)}")
        print(f"  - 总耗时: {elapsed:.2f}秒")
        
        return success_count == len(TEST_CHANNELS)
        
    async def run_all_tests(self):
        """运行所有测试"""
        await self.setup()
        
        # 测试列表
        tests = [
            ("监控指标API", self.test_metrics_api),
            ("资源管理功能", self.test_resource_management),
            ("错误追踪功能", self.test_error_tracking),
            ("性能监控功能", self.test_performance_monitoring),
            ("通道使用统计", self.test_channel_statistics),
            ("并发请求处理", self.test_concurrent_operations),
        ]
        
        # 执行测试
        for name, test_func in tests:
            await self.test(name, test_func)
            await asyncio.sleep(1)  # 测试间隔
            
        # 打印总结
        print(f"\n{'='*60}")
        print(f"测试完成")
        print(f"通过: {self.results['passed']}")
        print(f"失败: {self.results['failed']}")
        
        if self.results['errors']:
            print(f"\n错误详情:")
            for error in self.results['errors']:
                print(f"  - {error}")
                
        print(f"{'='*60}\n")
        
        await self.teardown()
        
        # 返回是否全部通过
        return self.results['failed'] == 0


async def main():
    """主函数"""
    tester = TestOptimization()
    success = await tester.run_all_tests()
    
    # 退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    asyncio.run(main())