#!/usr/bin/env python3
"""
测试优化后的相机服务
"""

import asyncio
import aiohttp
import json
from datetime import datetime


async def test_service():
    """测试服务功能"""
    base_url = "http://localhost:7090"
    
    async with aiohttp.ClientSession() as session:
        # 1. 测试健康检查
        print("1. 测试健康检查...")
        async with session.get(f"{base_url}/health") as resp:
            if resp.status == 200:
                data = await resp.json()
                print(f"✓ 健康检查通过: {data['status']}")
            else:
                print(f"✗ 健康检查失败: {resp.status}")
        
        # 2. 测试服务状态
        print("\n2. 测试服务状态...")
        async with session.get(f"{base_url}/api/v2/status") as resp:
            if resp.status == 200:
                data = await resp.json()
                print(f"✓ NVR连接: {data.get('nvr_connected', False)}")
                print(f"  通道映射: {data.get('channels', {}).get('mapping', {})}")
            else:
                print(f"✗ 状态获取失败: {resp.status}")
        
        # 3. 测试抓拍功能
        print("\n3. 测试抓拍功能...")
        async with session.post(
            f"{base_url}/api/v2/cameras/1/snapshot",
            json={"quality": 0}
        ) as resp:
            if resp.status == 200:
                data = await resp.json()
                if data.get("success"):
                    print(f"✓ 抓拍成功: {data['data']['filename']}")
                else:
                    print(f"✗ 抓拍失败: {data.get('error')}")
            else:
                print(f"✗ 抓拍请求失败: {resp.status}")
        
        # 4. 测试录像功能
        print("\n4. 测试录像功能...")
        # 开始录像
        async with session.post(
            f"{base_url}/api/v2/cameras/1/recording",
            json={"duration": 5}
        ) as resp:
            if resp.status == 200:
                data = await resp.json()
                if data.get("success"):
                    print(f"✓ 开始录像: {data['data']['filename']}")
                    
                    # 等待2秒
                    await asyncio.sleep(2)
                    
                    # 查询录像状态
                    async with session.get(f"{base_url}/api/v2/cameras/1/recording") as status_resp:
                        if status_resp.status == 200:
                            status_data = await status_resp.json()
                            print(f"  录像状态: {status_data.get('recording', False)}")
                            print(f"  已录制: {status_data.get('elapsed_seconds', 0):.1f}秒")
                else:
                    print(f"✗ 开始录像失败: {data.get('error')}")
            else:
                print(f"✗ 录像请求失败: {resp.status}")
        
        # 5. 测试PTZ控制（仅限通道1和3）
        print("\n5. 测试PTZ控制...")
        async with session.post(
            f"{base_url}/api/v2/cameras/1/ptz",
            json={
                "action": "move",
                "params": {
                    "direction": "up",
                    "speed": 3,
                    "duration": 1.0
                }
            }
        ) as resp:
            if resp.status == 200:
                data = await resp.json()
                if data.get("success"):
                    print(f"✓ PTZ控制成功: {data['data']}")
                else:
                    print(f"✗ PTZ控制失败: {data.get('error')}")
            else:
                print(f"✗ PTZ请求失败: {resp.status}")
        
        # 6. 测试文件列表
        print("\n6. 测试文件列表...")
        async with session.get(f"{base_url}/api/v2/files") as resp:
            if resp.status == 200:
                data = await resp.json()
                files = data.get("data", [])
                print(f"✓ 文件数量: {len(files)}")
                if files:
                    print("  最近文件:")
                    for f in files[:3]:
                        print(f"    - {f['filename']} ({f['size']} bytes)")
            else:
                print(f"✗ 文件列表获取失败: {resp.status}")


async def test_new_modules():
    """测试新模块导入"""
    print("\n7. 测试模块导入...")
    try:
        # 测试新模块
        from src.sdk.structures import NET_DVR_DEVICEINFO_V30
        from src.sdk.constants import PTZ_COMMANDS
        from src.services.recording_service import RecordingService
        from src.services.ptz_service import PTZService
        print("✓ 所有新模块导入成功")
        print(f"  - PTZ命令数: {len(PTZ_COMMANDS)}")
        print(f"  - 结构体定义: NET_DVR_DEVICEINFO_V30")
        print(f"  - 录像服务: RecordingService")
        print(f"  - PTZ服务: PTZService")
    except ImportError as e:
        print(f"✗ 模块导入失败: {e}")


if __name__ == "__main__":
    print("相机服务优化测试\n" + "="*50)
    
    # 测试模块
    asyncio.run(test_new_modules())
    
    # 测试服务
    print("\n开始测试服务功能...")
    asyncio.run(test_service())
    
    print("\n" + "="*50)
    print("测试完成！")