#!/usr/bin/env python3
"""
测试连接池功能
"""

import asyncio
import logging
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from sdk.hikvision import HikvisionSDK
from sdk.connection_pool import ConnectionPool

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_connection_pool():
    """测试连接池功能"""
    
    # 配置
    nvr_config = {
        'ip': '*************',
        'port': 8000,
        'username': 'admin',
        'password': 'Dhdjktsz'
    }
    
    pool_config = {
        'health_check_interval': 10,  # 10秒健康检查
        'reconnect_delay': 5,
        'max_reconnect_attempts': 3
    }
    
    # SDK工厂函数
    def sdk_factory(nvr_config):
        sdk_config = {
            'sdk_path': '/home/<USER>/server/edge-server/camera-service/lib/libhcnetsdk.so',
            'data_dir': '/data/camera/temp'
        }
        return HikvisionSDK(sdk_config)
    
    # 创建连接池
    pool = ConnectionPool(sdk_factory, nvr_config, pool_config)
    
    try:
        # 初始化连接池
        logger.info("=== 测试1: 初始化连接池 ===")
        if await pool.initialize():
            logger.info("✓ 连接池初始化成功")
        else:
            logger.error("✗ 连接池初始化失败")
            return
        
        # 测试获取连接
        logger.info("\n=== 测试2: 获取SDK连接 ===")
        sdk = pool.get_connection()
        if sdk:
            logger.info(f"✓ 获取连接成功，用户ID: {sdk.user_id}")
        else:
            logger.error("✗ 获取连接失败")
        
        # 测试执行操作
        logger.info("\n=== 测试3: 执行抓拍操作 ===")
        try:
            filepath = await pool.execute_with_retry(sdk.capture_jpeg, 1, 0)
            if filepath:
                logger.info(f"✓ 抓拍成功: {filepath}")
            else:
                logger.error("✗ 抓拍失败")
        except Exception as e:
            logger.error(f"✗ 抓拍异常: {e}")
        
        # 显示统计信息
        logger.info("\n=== 测试4: 连接池统计信息 ===")
        stats = pool.get_stats()
        logger.info("连接池统计:")
        for key, value in stats.items():
            logger.info(f"  {key}: {value}")
        
        # 等待健康检查
        logger.info("\n=== 测试5: 健康检查 ===")
        logger.info("等待30秒进行健康检查...")
        await asyncio.sleep(30)
        
        # 再次显示统计信息
        stats = pool.get_stats()
        logger.info("\n更新后的统计信息:")
        logger.info(f"  健康检查次数: {stats['health_checks']}")
        logger.info(f"  最后健康检查: {stats['last_health_check']}")
        logger.info(f"  连接状态: {'已连接' if stats['is_connected'] else '未连接'}")
        
        # 测试重连机制（模拟连接断开）
        logger.info("\n=== 测试6: 重连机制 ===")
        logger.info("模拟连接断开...")
        pool.is_connected = False
        pool.sdk.user_id = -1
        
        # 尝试执行操作，应该触发重连
        try:
            filepath = await pool.execute_with_retry(sdk.capture_jpeg, 1, 0)
            if filepath:
                logger.info(f"✓ 重连后抓拍成功: {filepath}")
            else:
                logger.error("✗ 重连后抓拍失败")
        except Exception as e:
            logger.error(f"✗ 重连失败: {e}")
        
        # 最终统计
        logger.info("\n=== 最终统计 ===")
        stats = pool.get_stats()
        logger.info(f"总连接次数: {stats['total_connections']}")
        logger.info(f"失败连接次数: {stats['failed_connections']}")
        logger.info(f"重连次数: {stats['reconnections']}")
        logger.info(f"健康检查次数: {stats['health_checks']}")
        
    finally:
        # 清理
        logger.info("\n=== 清理连接池 ===")
        await pool.cleanup()
        logger.info("✓ 连接池已清理")


async def main():
    """主函数"""
    try:
        await test_connection_pool()
    except KeyboardInterrupt:
        logger.info("\n测试被中断")
    except Exception as e:
        logger.error(f"\n测试异常: {e}")


if __name__ == "__main__":
    asyncio.run(main())