# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 日志文件
logs/
*.log

# 配置文件（本地覆盖）
config/local.yaml
config/*.local.yaml
config/config.yaml.local

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 测试
.coverage
.pytest_cache/
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 系统文件
.DS_Store
Thumbs.db
.env
.env.local

# 临时文件
*.tmp
*.bak
*.backup
*.old
temp/
tmp/

# 录像数据（如果需要保留请注释）
/tmp/camera_data/