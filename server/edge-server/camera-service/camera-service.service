[Unit]
Description=Camera Service v2.0
After=network.target

[Service]
Type=simple
User=app
Group=app
WorkingDirectory=/home/<USER>/server/edge-server/camera-service
Environment="PYTHONPATH=/home/<USER>/server/edge-server/camera-service/src"
Environment="LD_LIBRARY_PATH=/home/<USER>/server/edge-server/camera-service/lib:$LD_LIBRARY_PATH"
Environment="NVR_IP=*************"
Environment="NVR_PORT=8000"
Environment="NVR_USERNAME=admin"
Environment="NVR_PASSWORD=Dhdjktsz"
Environment="SDK_PATH=/home/<USER>/server/edge-server/camera-service/lib/libhcnetsdk.so"
Environment="DATA_DIR=/data/camera/temp"
ExecStart=/usr/bin/python3 /home/<USER>/server/edge-server/camera-service/src/main.py -c /home/<USER>/server/edge-server/camera-service/config/config.yaml
Restart=on-failure
RestartSec=10
StandardOutput=append:/home/<USER>/server/edge-server/camera-service/logs/camera-service.log
StandardError=append:/home/<USER>/server/edge-server/camera-service/logs/camera-service-error.log

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 安全选项
PrivateTmp=false
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/server/edge-server/camera-service/logs
ReadWritePaths=/home/<USER>/server/edge-server/camera-service/data
ReadWritePaths=/data/camera/temp

[Install]
WantedBy=multi-user.target