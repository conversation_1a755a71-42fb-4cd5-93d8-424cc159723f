#!/bin/bash

# Camera Service停止脚本

# 设置工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# PID文件
PID_FILE="$PROJECT_DIR/camera-service.pid"

if [ -f "$PID_FILE" ]; then
    PID=$(cat "$PID_FILE")
    if kill -0 $PID 2>/dev/null; then
        echo "Stopping Camera Service (PID: $PID)..."
        kill -TERM $PID
        
        # 等待进程结束
        for i in {1..10}; do
            if ! kill -0 $PID 2>/dev/null; then
                echo "Camera Service stopped successfully"
                rm -f "$PID_FILE"
                exit 0
            fi
            sleep 1
        done
        
        # 如果进程仍在运行，强制结束
        echo "Force stopping Camera Service..."
        kill -KILL $PID
        rm -f "$PID_FILE"
    else
        echo "Camera Service is not running (stale PID file)"
        rm -f "$PID_FILE"
    fi
else
    echo "Camera Service is not running (no PID file found)"
    
    # 尝试通过进程名查找
    PIDS=$(pgrep -f "camera-service/src/main.py")
    if [ -n "$PIDS" ]; then
        echo "Found Camera Service processes: $PIDS"
        echo "Stopping..."
        kill -TERM $PIDS
    fi
fi