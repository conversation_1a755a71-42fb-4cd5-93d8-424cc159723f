#!/bin/bash

# Camera Service启动脚本

# 设置工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 切换到项目目录
cd "$PROJECT_DIR"

# 设置环境变量
export PYTHONPATH="$PROJECT_DIR/src:$PYTHONPATH"
export LD_LIBRARY_PATH="$PROJECT_DIR/lib:$LD_LIBRARY_PATH"

# 默认配置
CONFIG_FILE="$PROJECT_DIR/config/config.yaml"
LOG_DIR="$PROJECT_DIR/logs"

# 创建必要的目录
mkdir -p "$LOG_DIR"
mkdir -p /data/camera/temp

# 检查参数
if [ "$1" == "--help" ] || [ "$1" == "-h" ]; then
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  -c, --config FILE    指定配置文件"
    echo "  -p, --port PORT      指定服务端口"
    echo "  -d, --daemon         后台运行"
    echo "  -h, --help           显示帮助信息"
    exit 0
fi

# 解析参数
DAEMON=0
EXTRA_ARGS=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--config)
            CONFIG_FILE="$2"
            shift 2
            ;;
        -p|--port)
            EXTRA_ARGS="$EXTRA_ARGS --port $2"
            shift 2
            ;;
        -d|--daemon)
            DAEMON=1
            shift
            ;;
        *)
            shift
            ;;
    esac
done

# 启动服务
if [ $DAEMON -eq 1 ]; then
    echo "Starting Camera Service in daemon mode..."
    nohup python3 "$PROJECT_DIR/src/main.py" -c "$CONFIG_FILE" $EXTRA_ARGS \
        > "$LOG_DIR/camera-service.log" 2>&1 &
    PID=$!
    echo "Camera Service started with PID: $PID"
    echo $PID > "$PROJECT_DIR/camera-service.pid"
else
    echo "Starting Camera Service..."
    python3 "$PROJECT_DIR/src/main.py" -c "$CONFIG_FILE" $EXTRA_ARGS
fi