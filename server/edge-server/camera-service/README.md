# Camera Service - 海康威视相机控制服务 v2.18

边缘服务器摄像头控制微服务，提供完整的海康威视网络摄像机控制功能，支持基础操作和高级业务功能。

> **v2.18 更新**: 完成模块化重构，优化代码结构，提升可维护性

## 🎯 核心功能

### 基础功能
- ✅ **抓拍照片** - 高质量图像抓取
- ✅ **实时录像** - 手动/自动录像控制  
- ✅ **PTZ控制** - 云台转动、缩放、预置点
- ✅ **历史回放** - 录像文件查询和下载
- ✅ **流媒体** - 获取实时视频流地址

### 高级业务功能 🌟
- ✅ **定点录像** - 移动到预置点后自动录像
- ✅ **地形扫描录像** - 360度全景扫描用于3D重建
- ✅ **预置点管理** - 设置、调用、删除预置点

## 🏗️ 服务架构

采用三层微服务架构设计：
```
API层 (REST接口)
    ↓
业务层 (录像/PTZ/抓拍逻辑)
    ↓  
SDK层 (海康威视SDK封装)
```

**通道映射**：用户通道1-4 → NVR实际通道33-36  
**PTZ支持**：通道1、3支持云台控制  
**运行端口**：7090（固定）

## ⚙️ NVR配置

默认连接配置（可在`config/config.yaml`修改）：
```yaml
nvr:
  ip: *************      # NVR设备IP
  port: 8000             # 设备端口  
  username: admin        # 登录用户名
  password: Dhdjktsz     # 登录密码
```

## 🚀 快速启动

### 1. 安装依赖
```bash
pip install aiohttp aiohttp-cors pyyaml
```

### 2. 启动服务
```bash
# 使用默认配置启动
python src/main.py

# 指定配置文件启动  
python src/main.py -c config/config.yaml

# 服务将运行在 http://0.0.0.0:7090
```

### 3. 验证服务
```bash
# 服务状态检查
curl http://localhost:7090/api/v2/status

# API文档
curl http://localhost:7090/api/v2
```

## 📋 API接口总览

### 基础功能 API

#### 抓拍照片
```bash
POST /api/v2/cameras/{channel}/snapshot
Content-Type: application/json

{
  "quality": 0    # 0-最佳, 1-较好, 2-一般
}

# 示例
curl -X POST http://localhost:7090/api/v2/cameras/1/snapshot \
  -H "Content-Type: application/json" \
  -d '{"quality": 0}'
```

#### 录像控制
```bash
# 开始录像
POST /api/v2/cameras/{channel}/recording
{
  "duration": 60    # 录像时长(秒), 可选
}

# 停止录像  
DELETE /api/v2/cameras/{channel}/recording

# 示例
curl -X POST http://localhost:7090/api/v2/cameras/1/recording \
  -H "Content-Type: application/json" \
  -d '{"duration": 60}'
```

#### PTZ控制
```bash
POST /api/v2/cameras/{channel}/ptz
{
  "action": "move",           # move/zoom/stop
  "params": {
    "direction": "up",        # up/down/left/right
    "speed": 3,              # 速度 1-7
    "duration": 1.0          # 持续时间(秒)
  }
}

# 示例 - 向上移动
curl -X POST http://localhost:7090/api/v2/cameras/1/ptz \
  -H "Content-Type: application/json" \
  -d '{
    "action": "move",
    "params": {
      "direction": "up",
      "speed": 3,
      "duration": 1.0
    }
  }'
```

#### 历史回放
```bash
# 查询录像文件
GET /api/v2/cameras/{channel}/playback?start_time=2025-01-01T00:00:00&end_time=2025-01-01T23:59:59

# 下载录像文件
GET /api/v2/cameras/{channel}/playback/download?filename=record_ch33_20250123_120000.mp4
```

#### 流媒体
```bash
# 获取实时流地址
GET /api/v2/cameras/{channel}/stream

# 返回示例
{
  "success": true,
  "data": {
    "rtsp_url": "rtsp://admin:password@*************:554/Streaming/Channels/3301",
    "http_url": "http://*************/ISAPI/Streaming/channels/3301/httppreview"
  }
}
```

### 高级业务功能 API 🌟

#### 预置点管理
```bash
# 设置预置点
POST /api/v2/cameras/{channel}/preset/{preset_id}/set

# 调用预置点
POST /api/v2/cameras/{channel}/preset/{preset_id}/call

# 删除预置点
DELETE /api/v2/cameras/{channel}/preset/{preset_id}

# 示例 - 设置预置点1
curl -X POST http://localhost:7090/api/v2/cameras/1/preset/1/set
```

#### 定点录像 📍
移动到指定预置点后自动开始录像
```bash  
POST /api/v2/cameras/{channel}/recording/preset
{
  "preset_id": 1,     # 预置点ID
  "duration": 300     # 录像时长(秒)
}

# 示例
curl -X POST http://localhost:7090/api/v2/cameras/1/recording/preset \
  -H "Content-Type: application/json" \
  -d '{"preset_id": 1, "duration": 300}'
```

#### 地形扫描录像 🌍
360度全景扫描录像，用于3D地形重建
```bash
POST /api/v2/cameras/{channel}/recording/terrain  
{
  "duration": 1800,      # 总录像时长(秒)
  "tilt_step": 15,       # 俯仰角步进(度)
  "pan_speed": 2         # 水平扫描速度 1-7
}

# 示例 - 进行地形扫描
curl -X POST http://localhost:7090/api/v2/cameras/1/recording/terrain \
  -H "Content-Type: application/json" \
  -d '{"duration": 1800, "tilt_step": 15, "pan_speed": 2}'
```

## 📁 数据存储

录像文件存储在edge-server统一数据目录：`/home/<USER>/server/edge-server/data/camera/`

子目录结构：
- `snapshots/` - 抓拍照片
- `recordings/` - 实时录像文件
- `playback/` - 历史回放文件

文件命名规则：
- 抓拍照片：`snapshot_ch{channel}_{timestamp}.jpg`
- 普通录像：`record_ch{channel}_{timestamp}.mp4`
- 定点录像：`record_ch{actual_channel}_{timestamp}.mp4`  
- 地形扫描：`record_ch{actual_channel}_{timestamp}.mp4`
- 历史回放：`playback_ch{channel}_{timestamp}.mp4`

## 🔧 项目结构

```
camera-service/
├── src/                           # 源代码
│   ├── main.py                   # 服务主入口
│   ├── api/
│   │   └── server.py             # REST API服务器
│   ├── services/
│   │   ├── camera_service.py     # 主业务逻辑层
│   │   ├── recording_service.py  # 录像服务模块
│   │   └── ptz_service.py        # PTZ控制模块
│   └── sdk/
│       ├── hikvision.py          # 海康SDK核心封装
│       ├── structures.py         # SDK数据结构定义
│       ├── constants.py          # 常量和错误码定义
│       └── connection_pool.py    # 连接池管理
├── config/
│   └── config.yaml               # 服务配置
├── lib/
│   ├── libhcnetsdk.so           # 海康威视SDK主库
│   └── HCNetSDKCom/             # SDK依赖库
├── scripts/
│   ├── start.sh                 # 启动脚本
│   └── stop.sh                  # 停止脚本  
├── tests/
│   ├── test_connection_pool.py   # 连接池测试
│   └── test_optimized_service.py # 服务功能测试
├── docs/                         # 技术文档
├── logs/                         # 日志目录（git忽略）
├── .gitignore                   # Git忽略配置
└── README.md                    # 本文档
```

## 💡 使用场景

### 边缘服务集成
其他边缘服务可通过HTTP API调用相机功能：

```python
import requests

# 获取服务状态
response = requests.get('http://localhost:7090/api/v2/status')

# 执行定点录像
response = requests.post(
    'http://localhost:7090/api/v2/cameras/1/recording/preset',
    json={'preset_id': 1, 'duration': 300}
)

# 执行地形扫描
response = requests.post(
    'http://localhost:7090/api/v2/cameras/3/recording/terrain', 
    json={'duration': 1800, 'tilt_step': 20, 'pan_speed': 3}
)
```

### 业务流程示例
1. **安防监控**：设置预置点 → 定点录像 → 自动停止
2. **地形测绘**：启动地形扫描 → 360度录像 → 数据采集完成
3. **实时监控**：获取流媒体地址 → 显示实时画面

## ⚠️ 注意事项

1. **通道限制**：
   - 用户通道：1-4（映射到NVR通道33-36）
   - PTZ功能：仅通道1、3支持

2. **并发限制**：
   - 每个通道同时只能有一个录像任务
   - 录像任务完成后会自动清理资源

3. **存储管理**：
   - 录像文件默认保留24小时
   - 最大存储空间10GB，超出自动清理

4. **网络要求**：
   - 确保与NVR网络连通（*************:8000）
   - 服务运行端口7090不被占用

## 🐛 故障排除

### SDK初始化失败
```bash
# 检查SDK库文件
ls -la lib/libhcnetsdk.so
# 确保有执行权限
chmod +x lib/libhcnetsdk.so
```

### NVR连接失败  
```bash
# 测试网络连通性
ping *************
telnet ************* 8000

# 检查配置
cat config/config.yaml
```

### 录像功能异常
```bash
# 检查磁盘空间
df -h /tmp

# 查看服务日志  
tail -f logs/camera-service.log

# 检查通道映射
curl http://localhost:7090/api/v2/status
```

## 📈 性能指标

- **响应时间**：API调用 < 100ms
- **录像启动**：< 2秒  
- **PTZ响应**：< 500ms
- **并发连接**：支持10+客户端
- **存储效率**：H.264编码，约1GB/小时

## 🔄 版本历史

- **v2.18** - 模块化重构：拆分大文件，优化项目结构，提升代码可维护性
- **v2.17** - 优化录像资源清理机制，确保通道正确释放
- **v2.16** - 修复定点录像功能，新增高级业务功能  
- **v2.15** - 完成相机服务架构优化重构
- **v2.0** - 独立微服务架构，完整API设计
- **v1.0** - 初始版本

---

## 🤝 服务对接

此Camera Service为边缘服务器的核心组件，其他服务可通过REST API进行集成：

- **激光雷达服务**：可调用定点录像配合激光扫描
- **AI分析服务**：获取实时流进行智能分析  
- **存储服务**：管理录像文件的存储和备份
- **监控中心**：统一的设备状态监控和管理

**联系信息**：通过HTTP API与服务通信，无需额外配置。