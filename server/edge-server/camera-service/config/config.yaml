# Camera Service Configuration v2.0
# 相机服务配置文件

# 服务器配置
server:
  host: 0.0.0.0          # 监听地址，0.0.0.0表示监听所有接口
  port: 7090             # API服务端口（固定使用7090）

# NVR设备配置
nvr:
  ip: *************      # NVR设备IP地址
  port: 8000             # NVR设备端口
  username: admin        # NVR登录用户名
  password: Dhdjktsz     # NVR登录密码
  timeout: 30            # 连接超时时间（秒）

# SDK配置
sdk:
  # SDK库文件路径（支持多个路径，按优先级搜索）
  paths:
    - /home/<USER>/server/edge-server/camera-service/lib/libhcnetsdk.so
    - /usr/local/lib/libhcnetsdk.so
    - ./lib/libhcnetsdk.so
  # SDK初始化参数
  init_timeout: 10       # SDK初始化超时时间

# 连接池配置
connection_pool:
  max_connections: 1     # 最大连接数（海康SDK通常只支持单连接）
  health_check_interval: 30  # 健康检查间隔（秒）
  reconnect_delay: 5     # 重连延迟（秒）
  max_reconnect_attempts: 3  # 最大重连次数

# 数据存储配置
data:
  # 数据存储目录
  dir: /data/camera
  # 文件保留时间（小时）
  retention_hours: 24
  # 最大存储空间（MB）
  max_storage_mb: 10240

# 日志配置
logging:
  level: INFO            # 日志级别：DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 通道映射配置
channels:
  # 用户通道号 -> NVR实际通道号
  mapping:
    1: 33  # 球机1 - 支持PTZ控制
    2: 34  # 球机2
    3: 35  # 球机3 - 支持PTZ控制  
    4: 36  # 球机4
  
  # PTZ支持的通道
  ptz_enabled: [1, 3]    # 对应NVR通道33和35
  
  # 通道描述信息
  descriptions:
    1: "球机1 - 主要监控区域"
    2: "球机2 - 次要监控区域"
    3: "球机3 - 重点监控区域"
    4: "球机4 - 备用监控区域"

# 功能配置
features:
  # 抓拍配置
  snapshot:
    default_quality: 0   # 默认图片质量：0-最佳，1-较好，2-一般
    formats: ["jpg"]     # 支持的图片格式
  
  # 录像配置
  recording:
    max_duration: 1800   # 最大录像时长（秒）
    auto_stop: true      # 自动停止录像
    
  # PTZ控制配置
  ptz:
    default_speed: 3     # 默认PTZ速度（1-7）
    max_duration: 30     # 最大单次控制时长（秒）
    
  # 历史回放配置
  playback:
    max_duration: 3600   # 最大回放时长（秒）
    download_timeout: 300 # 下载超时时间（秒）

# 安全配置
security:
  # API访问控制
  cors_enabled: true     # 启用跨域请求
  allowed_origins: ["*"] # 允许的来源
  
  # 请求限制
  rate_limit:
    enabled: false       # 启用请求限制
    requests_per_minute: 60